<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { ElScrollbar } from 'element-plus';

import { getInvcAdjustDoc } from '#/api/warehouse-management';
import ApprovalTimeline from '#/components/approval-timeline/Index.vue';
import FormCard from '#/components/form-card/Index.vue';

import AdjustFormView from './adjust-form/form-view/index.vue';
import MaterialFormView from './material-form/form-view/index.vue';

const props = defineProps({
  /** 库存调整单据编号 */
  invcAdjustDocNumber: {
    type: String,
    default: '',
  },
  /** 库存调整单据id */
  invcAdjustDocId: {
    type: String,
    default: '',
  },
  /** 当前单据状态*/
  docStatus: {
    type: String,
    default: '',
  },
  /** 审核流程实例ID */
  processInstanceId: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
/** 审核流程实例ID */
const processId = ref(props.processInstanceId);
/** 获取库存调整单详细信息 */
const getData = async () => {
  try {
    loading.value = true;
    const res = await getInvcAdjustDoc(
      props.invcAdjustDocId,
      props.invcAdjustDocNumber,
    );
    processId.value = res?.processInstanceId;
  } catch (error) {
    console.error(error);
    // ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  // 如果没有审核流程，发送请求获取
  if (!props.processInstanceId) {
    getData();
  }
});
defineExpose({
  loading,
});
</script>
<template>
  <div v-loading="loading">
    <AdjustFormView
      :invc-adjust-doc-number="invcAdjustDocNumber"
      :invc-adjust-doc-id="invcAdjustDocId"
    />
    <MaterialFormView
      :invc-adjust-doc-id="invcAdjustDocId"
      :invc-adjust-doc-number="invcAdjustDocNumber"
      :doc-status="docStatus"
    />
    <FormCard :is-footer="false">
      <template #title>
        <span>审核流程</span>
      </template>
      <ElScrollbar>
        <ApprovalTimeline :process-instance-id="processId" />
      </ElScrollbar>
    </FormCard>
  </div>
</template>
