<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { InventoryAdjustment } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getInvcAdjustDoc } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';
import MaterialForm from '#/views/warehouse-management/basic-data/material/modules/material-form/index.vue';

import MaterialViewField from '../../../components/material-view-field/index.vue';
import { openModal } from '../../method';
import { useColumns } from './data';

const props = defineProps({
  /** 库存调整单据ID */
  invcAdjustDocId: {
    type: String,
    default: '',
  },
  /** 库存调整单据编号 */
  invcAdjustDocNumber: {
    type: String,
    default: '',
  },
  /** 当前单据状态*/
  docStatus: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
const data = ref<InventoryAdjustment.InvcAdjustDoc>();
/** 所选物料Id */
const productMaterialId = ref();
/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  confirmText: '提交',
  showCancelButton: true,
  showConfirmButton: true,
});

/** 库存调整子项列表 */
const invcAdjustItemList = ref<InventoryAdjustment.InvcAdjustItem[]>();
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(props.docStatus),
    maxHeight: '500',
    keepSource: true,
    pagerConfig: {
      enabled: false,
    },
    data: [],
  } as VxeTableGridOptions,
});
/** 获取物料信息列表 */
const getData = async () => {
  try {
    loading.value = true;
    data.value = await getInvcAdjustDoc(
      props.invcAdjustDocId,
      props.invcAdjustDocNumber,
      true,
    );
    invcAdjustItemList.value = data.value?.invcAdjustItemList;
    // 设置表格数据
    gridApi.setGridOptions({
      data: data.value?.invcAdjustItemList,
    });
  } catch {
    ElMessage.error('获取物料信息失败');
  } finally {
    loading.value = false;
  }
};

/** 显示物料详情信息 */
const showProductMaterial = (row: any) => {
  productMaterialId.value = row.materialId;
  openModal(formModalApi, false, '物料详情');
};
onMounted(() => {
  if (props.invcAdjustDocId || props.invcAdjustDocNumber) {
    getData();
  } else {
    ElMessage.error('没有调整单编号和ID');
  }
});
defineExpose({
  gridApi,
  Grid,
});
</script>
<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>调整明细</span>
    </template>
    <FormModal class="h-full w-10/12">
      <MaterialForm :material-id="productMaterialId" />
    </FormModal>
    <Grid>
      <template #quantity="{ row }">
        <span :class="row.quantity > 0 ? 'text-lime-500' : 'text-red-500'">
          <span v-text="row.quantity > 0 ? '+' : ''"></span>
          <span>{{ row.quantity }}</span>
        </span>
      </template>
      <template #materialName="{ row }">
        <MaterialViewField
          :material-name="row.materialName"
          :material-code="row.materialCode"
          :picture-file-id="row.pictureFileId"
          @click="showProductMaterial(row)"
        />
      </template>
    </Grid>
  </FormCard>
</template>
