<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { ElMessage, ElScrollbar } from 'element-plus';

import {
  getAssemblyDocDetail,
  saveOrModAssemblyDoc,
  submitAssemblyDoc,
} from '#/api';
import ApprovalTimeline from '#/components/approval-timeline/Index.vue';
import FormCard from '#/components/form-card/Index.vue';

import DisassemblyFormEdit from './disassembly-form/form-edit/index.vue';
import DisassemblyFormView from './disassembly-form/form-view/index.vue';
import MaterialFormEdit from './material-form/form-edit/index.vue';
import MaterialFormView from './material-form/form-view/index.vue';
import { dialog } from './method';

const props = defineProps({
  isView: {
    type: Boolean,
    default: false,
  },
  /** 组装拆卸单据ID */
  assemblyDocId: {
    type: String,
    default: '',
  },
  /** 组装拆卸单据编号 */
  assemblyDocNumber: {
    type: String,
    default: '',
  },
  /** 组装拆卸类型值 */
  docCode: {
    type: String,
    default: 'WM0061',
  },
  /** 审核流程实例ID */
  processInstanceId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['submitSuccess']);

const loading = ref(false);
/** 拆卸信息ref*/
const disassemblyFormRef = ref();
/** 原料明细ref*/
const materialFormRef = ref();
/** 审核流程实例ID */
const processId = ref(props.processInstanceId);
/** 母件BomID */
const bomId = ref();
/** 拆卸数量 */
const currentQuantity = ref();
/** 拆卸旧数量 */
const oldQuantity = ref();

onMounted(async () => {
  try {
    // 如果没有审核流程，发送请求获取
    if (!processId.value) {
      const res = await getAssemblyDocDetail(
        props.assemblyDocId,
        props.assemblyDocNumber,
      );
      processId.value = res?.processInstanceId;
    }
  } catch (error) {
    console.error(error);
  }
});

/** 拆卸成品变化 */
const productChanged = (id: string) => {
  bomId.value = id;
};

/** 拆卸数量变化 */
const quantityChange = (currentValue: number, oldValue: number) => {
  currentQuantity.value = currentValue;
  oldQuantity.value = oldValue;
};

/** 校验表单 */
const validateForm = async () => {
  // 校验调整信息表单 // 校验物料信息表单
  const [verification, verification2] = await Promise.all([
    disassemblyFormRef.value?.validateForm(),
    materialFormRef.value?.validateForm(),
  ]);

  // 获取表单数据
  const data = await getFormData();
  if (data.assemblyMaterialList.length === 0) {
    ElMessage.error('请填写原料信息');
    return false;
  }
  if (!verification || !verification2) {
    ElMessage.error('请填写完整表单');
    return false;
  }

  // 检查物料是否重复
  const seenIds = new Set();
  for (const item of data.assemblyMaterialList) {
    // 检查当前materialId是否已存在
    if (seenIds.has(item.materialId)) {
      // 发现重复，返回true和重复的ID
      ElMessage.error('原料存在重复，请确保所有原料唯一');
      return false;
    }
    // 将当前ID添加到集合中
    seenIds.add(item.materialId);
  }

  return true;
};

/** 获取表单数据 */
const getFormData = async () => {
  const [data, data2] = await Promise.all([
    disassemblyFormRef.value?.getFormData(),
    materialFormRef.value?.getFormData(),
  ]);
  return {
    ...data,
    assemblyMaterialList: data2,
  };
};

/** 提交表单 新增*/
const onSubmit = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    const isUpdate = !!props.assemblyDocId;
    if (await dialog('确定提交单据吗？', '提示')) {
      loading.value = true;
      await submitAssemblyDoc({
        assemblyDocId: isUpdate ? props.assemblyDocId : '',
        ...data,
      });
    }
    ElMessage.success(isUpdate ? '提交成功' : '新增成功');
    emit('submitSuccess');
  } catch (error) {
    console.error(error);
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};

/** 提交暂存 */
const onSave = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    const isUpdate = !!props.assemblyDocId;
    if (await dialog('确定提交暂存吗？', '提示')) {
      loading.value = true;
      await saveOrModAssemblyDoc({
        assemblyDocId: isUpdate ? props.assemblyDocId : '',
        ...data,
      });
    }
    ElMessage.success('提交暂存成功');
    emit('submitSuccess');
  } catch (error) {
    console.error(error);
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};

defineExpose({
  onSubmit,
  onSave,
  validateForm,
  loading,
});
</script>

<template>
  <div v-loading="loading">
    <DisassemblyFormView
      v-if="isView"
      ref="disassemblyFormRef"
      :assembly-doc-id="assemblyDocId"
      :assembly-doc-number="assemblyDocNumber"
    />
    <DisassemblyFormEdit
      v-else
      ref="disassemblyFormRef"
      :assembly-doc-id="assemblyDocId"
      :assembly-doc-number="assemblyDocNumber"
      @product-changed="productChanged"
      @quantity-change="quantityChange"
    />
    <MaterialFormView
      v-if="isView"
      ref="materialFormRef"
      :assembly-doc-id="assemblyDocId"
      :assembly-doc-number="assemblyDocNumber"
    />
    <MaterialFormEdit
      v-else
      ref="materialFormRef"
      :assembly-doc-id="assemblyDocId"
      :assembly-doc-number="assemblyDocNumber"
      :bom-id="bomId"
      :current-quantity="currentQuantity"
      :old-quantity="oldQuantity"
    />
    <FormCard :is-footer="false" v-if="isView">
      <template #title>
        <span>审核流程</span>
      </template>
      <ElScrollbar>
        <ApprovalTimeline v-if="processId" :process-instance-id="processId" />
      </ElScrollbar>
    </FormCard>
  </div>
</template>
