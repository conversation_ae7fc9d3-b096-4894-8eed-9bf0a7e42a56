import type { Recordable } from '@vben/types';

import type { InBoundDocApi } from './stockPending';

import { requestClient } from '#/api/request';

import { warehousePath } from '../../path';
// -------------------------------------------------入库单据明细-------------------------------------------------
/** 入库单据明细接口 */
export namespace InboundDetailsApi {
  /** 入库单据明细查询参数 */
  export interface InboundDetailsPageParams {
    /** 入库单据号列表，多个用英文逗号分隔 */
    inBoundDocNumberList?: string;
    /** 源单据类型代码列表，多个用英文逗号分隔 */
    origDocTypeCodeList?: string;
    /** 源单据号列表，多个用英文逗号分隔 */
    origDocNumberList?: string;
    /** 申请人ID列表，多个用英文逗号分隔 */
    applyUserList?: string;
    /** 执行仓管员ID列表，多个用英文逗号分隔 */
    executorUserList?: string;

    /** 物料ID列表，多个用英文逗号分隔 */
    materialIdList?: string;

    /** 物料编号列表，多个用英文逗号分隔 */
    materialCodeList?: string;

    /** 物料名称*/
    materialName?: string;

    /** 是否补录 */
    isRectify?: boolean;
    /** 申请起始时间，时间格式：yyyy-MM-dd HH:mm */
    applyStartTime?: string;
    /** 申请终止时间，时间格式：yyyy-MM-dd HH:mm */
    applyEndTime?: string;
    /** 执行入库起始时间，时间格式：yyyy-MM-dd HH:mm */
    executorStartTime?: string;
    /** 执行入库终止时间，时间格式：yyyy-MM-dd HH:mm */
    executorEndTime?: string;
    /** 单据状态值列表，字典wmInDocStatus */
    docStatusList?: string;
    /** 当前页，默认第1页 */
    pageNum?: number;
    /** 分页数，默认每一页默认10条 */
    pageSize?: number;
  }

  /** 入库单据申请明细查询参数 */
  export interface InboundApplyDetailsPageParams {
    /** 入库单据编号列表，精确查询 */
    inBoundDocNumberList?: string;
    /** 源单据类型标识列表，精确查询，根据查询来源单据配置列表接口的出参docCode字段。 */
    origDocTypeCodeList?: string;
    /** 源单据编号列表，精确查询 */
    origDocNumberList?: string;
    /** 申请人ID列表，精确查询 */
    applyUserList?: string;
    /** 物料ID列表，精确查询 */
    materialIdList?: string;
    /** 物料编号列表，精确查询 */
    materialCodeList?: string;
    /** 物料名称，模糊查询，支持查询物料名称、别名、规格 */
    materialName?: string;
    /** 物料属性列表，精确查询，字典baseMaterialAttribute */
    materialAttributeList?: string;
    /** 物料大类列表，精确查询，字典baseMaterialType */
    materialTypeList?: string;
    /** 物料细类列表，精确查询 */
    materialCategoryList?: string;
    /** 是否标准物料，true-标准物料, false-非标准物料 */
    isStandard?: boolean;
    /** 是否补录，不填则展示全部，true-补录，false-非补录 */
    isRectify?: boolean;
    /** 申请起始时间，时间格式：yyyy-MM-dd HH:mm */
    applyStartTime?: string;
    /** 申请终止时间，时间格式：yyyy-MM-dd HH:mm */
    applyEndTime?: string;
    /** 单据状态值列表，字典wmInDocStatus */
    docStatusList?: string;
  }

  export interface InboundActualDetailsPageParams {
    inBoundDocNumberList?: string;
    origDocTypeCodeList?: string;
    origDocNumberList?: string;
    applyUserList?: string;
    executorUserList?: string;
    materialIdList?: string;
    materialCodeList?: string;
    materialName?: string;
    materialAttributeList?: string;
    materialTypeList?: string;
    materialCategoryList?: string;
    isStandard?: boolean;
    warehouseIdList?: string;
    warehouseName?: string;
    locationIdList?: string;
    locationName?: string;
    batchNumber?: string;
    isRectify?: boolean;
    applyStartTime?: string;
    applyEndTime?: string;
    executorStartTime?: string;
    executorEndTime?: string;
  }

  /** 入库单据明细记录 */
  export interface InboundDetailsRecord {
    /* 入库单据ID */
    inBoundDocId: number;

    /* 入库单据编号 */
    inBoundDocNumber: string;

    /* 源单据类型标识 */
    origDocTypeCode: string;

    /* 源单据类型名称 */
    origDocTypeName: string;

    /* 源单据ID */
    origDocId: number;

    /* 源单据编号 */
    origDocNumber: string;

    /* 申请人ID */
    applyUser: number;

    /* 申请人姓名 */
    applyUserName: string;

    /* 申请人部门ID */
    applyUserDeptId: number;

    /* 申请人部门名称 */
    applyUserDeptName: string;

    /* 执行仓管员ID */
    executorUser: number;

    /* 执行仓管员姓名 */
    executorUserName: string;

    /* 执行仓管员部门ID */
    executorUserDeptId: number;

    /* 执行仓管员部门名称 */
    executorUserDeptName: string;

    /* 仓管员确认方式值，枚举WmInOutManagerConfirmMethodEnums */
    executorConfirmMethod: string;

    /* 仓管员确认方式标签，枚举WmInOutManagerConfirmMethodEnums */
    executorConfirmMethodLabel: string;

    /* 是否补录 */
    isRectify: boolean;

    /* 提交时间，时间格式：yyyy-MM-dd HH:mm */
    applyTime: Record<string, unknown>;

    /* 执行入库时间，时间格式：yyyy-MM-dd HH:mm */
    executorTime: Record<string, unknown>;

    /* 是否自动完成入库 */
    isAutoIo: boolean;

    /* 单据状态值，字典wmInDocStatus */
    docStatus: string;

    /* 单据状态标签，字典wmInDocStatus */
    docStatusLabel: string;

    /* 物料ID */
    materialId: number;

    /* 物料编号 */
    materialCode: string;

    /* 物料名称 */
    materialName: string;

    /* 物料图片ID */
    pictureFileId: number;

    /* 基本单位值，字典baseMaterialUnit */
    baseUnit: string;

    /* 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;

    /* 物料属性值，字典baseMaterialAttribute */
    materialAttribute: string;

    /* 物料属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;

    /* 物料大类值，字典baseMaterialType */
    materialType: string;

    /* 物料大类标签，字典baseMaterialType */
    materialTypeLabel: string;

    /* 物料细类 */
    materialCategory: string;
    /* 物料细类名称 */
    materialCategoryName: string;

    /* 申请总数 */
    applyQuantitySum: number;

    /* 实际总数 */
    actualQuantitySum: number;

    /* 申请明细列表 */
    applyItemList: InBoundDocApi.ApplyItem[];

    /* 实际明细列表 */
    actualItemList: InBoundDocApi.actualItem[];
  }

  /** 入库单据明细分页响应 */
  export interface InboundDetailsPageRes {
    /** 是否成功 */
    success: boolean;
    /** 状态码 */
    code: number;
    /** 数据 */
    data: {
      /** 当前页 */
      pageNum: number;
      /** 分页条数 */
      pages: number;
      /** 分页数 */
      pageSize: number;
      /** 数据 */
      records: InboundDetailsRecord[];
      /** 总条数 */
      total: number;
    };
    /** 消息 */
    msg: string;
  }
}
/**
 * 查询入库单据明细分页列表
 */
export async function getInboundDetailsPage(
  params: InboundDetailsApi.InboundDetailsPageParams,
) {
  return requestClient.post<InboundDetailsApi.InboundDetailsPageRes>(
    `${warehousePath}/wm/inBound/item/getInBoundItemPage`,
    { ...params },
  );
}

/**
 * 查询入库单据申请明细分页列表
 */
export async function getInBoundDetailPage(
  params: InboundDetailsApi.InboundApplyDetailsPageParams,
) {
  return requestClient.post<InboundDetailsApi.InboundDetailsPageRes>(
    `${warehousePath}/wm/inBound/apply/getInBoundDetailPage`,
    { ...params },
  );
}

/** 导出入库单据申请明细列表*/
export async function exportInBoundDetailPage(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inBound/apply/exportInBoundDetailPage`,
    {
      ...params,
    },
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/**
 * 查询入库单据申请明细分页列表
 */
export async function getInBoundItemPage(
  params: InboundDetailsApi.InboundApplyDetailsPageParams,
) {
  return requestClient.post<InboundDetailsApi.InboundDetailsPageRes>(
    `${warehousePath}/wm/inBound/actual/getInBoundItemPage`,
    { ...params },
  );
}

/** 导出入库单据申请明细列表*/
export async function exportInBoundAlreadyDetailPage(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inBound/actual/exportInBoundDetailPage`,
    {
      ...params,
    },
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
