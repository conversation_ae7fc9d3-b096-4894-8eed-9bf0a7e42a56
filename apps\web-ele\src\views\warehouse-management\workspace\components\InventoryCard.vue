<script lang="ts">
import { computed, defineComponent, onMounted, ref } from 'vue';

import { CountTo } from '@vben/common-ui';

import { getInventoryMaterialNum } from '#/api/warehouse-management';
import PanelCard from '#/components/panel-card/Index.vue';
import { formatNumber } from '#/utils/data-processing-utils';
import { WS } from '#/utils/socket/common-socketio';

export default defineComponent({
  components: { PanelCard, CountTo },
  props: {
    activeId: {
      type: String,
      default: '',
    },
  },
  emits: ['cardClick'],
  setup(props, { emit }) {
    const wsType = ['wm.inventory.change'];

    const isActive = computed(() => props.activeId === 'inventory');

    const loading = ref(true);

    const matKindCnt = ref(0); // 种类
    const quantitySum = ref<number | string>(0); // 数量

    const fetchInventoryData = async () => {
      try {
        const response = await getInventoryMaterialNum();
        matKindCnt.value = response.matKindCnt || 0;
        quantitySum.value = formatNumber(response.quantitySum) || 0;
      } catch (error) {
        console.error('获取库存数据失败:', error);
      } finally {
        loading.value = false;
      }
    };

    const handleCardClick = () => {
      emit('cardClick', {
        name: 'inventory',
        attrs: {
          wrapperClass:
            'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2',
        },
      });
    };

    onMounted(() => {
      WS.on(wsType, fetchInventoryData);

      fetchInventoryData();
    });

    return {
      isActive,
      matKindCnt,
      quantitySum,
      loading,
      handleCardClick,
    };
  },
});
</script>

<template>
  <PanelCard title="库存情况">
    <template #default>
      <div
        class="hover:bg-primary-100 bg-primary-50 grid cursor-pointer grid-cols-2 gap-4 rounded-lg px-4 py-2 text-black"
        @click="handleCardClick"
      >
        <div class="flex h-6 items-end">
          <span class="text-sm">现有库存</span>
          <span class="mx-1 -mb-0.5 text-xl font-bold">
            <CountTo
              v-loading="loading"
              :start-val="0"
              :end-val="matKindCnt"
              :duration="1500"
              separator=""
            />
          </span>
          <span class="text-sm">种物料</span>
        </div>
        <div class="flex h-6 items-end justify-end">
          <span class="mr-1 text-sm">数量合计</span>
          <span class="mb-[1px]" v-loading="loading">
            {{ quantitySum }}
          </span>
        </div>
      </div>
    </template>
  </PanelCard>
</template>
