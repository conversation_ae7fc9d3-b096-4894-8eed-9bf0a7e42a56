<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import FormToInbound from './FormToInbound.vue';

/** 共享数据 */
const data = ref();

const formToInboundRef = ref<InstanceType<typeof FormToInbound>>();

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  onClosed() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
    }
  },
});

/** 刷新列表 */
const refreshList = () => {
  data.value?.refreshList();
  modalApi.setState({ loading: false }).close();
};

const execInboundHandle = () => {
  modalApi.setState({ loading: true });
  formToInboundRef.value?.execInboundHandle();
};

const inboundSuccess = () => {
  refreshList();
};

const inboundError = () => {
  modalApi.setState({ loading: false });
};
</script>

<template>
  <Modal>
    <FormToInbound
      ref="formToInboundRef"
      :in-bound-doc-id="data.inBoundDocId"
      :in-bound-doc-number="data.inBoundDocNumber"
      :doc-status="data.docStatus"
      @inbound-success="inboundSuccess"
      @inbound-error="inboundError"
    />

    <template #footer>
      <ElButton type="info" @click="modalApi.close()"> 取消 </ElButton>
      <ElButton
        type="primary"
        @click="execInboundHandle"
        v-access:code="['wm:inbound:exec']"
        v-if="data.docStatus === '00'"
      >
        确认入库
      </ElButton>
    </template>
  </Modal>
</template>
