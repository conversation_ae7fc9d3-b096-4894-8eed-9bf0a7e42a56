<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse, isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage, ElTooltip } from 'element-plus';

import {
  exportPendingInBoundDoc,
  getInBoundDocPage,
} from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';

import CloseBound from '../components/close-bound/form-edit/index.vue';
import { useColumns, useGridFormSchema } from './data';
import Form from './modules/Form.vue';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      applyEndTime?: string;
      applyStartTime?: string;
      applyUserList?: string;
      inBoundDocNumberList?: string;
      isRectify?: unknown;
      origDocNumberList?: string;
      origDocTypeCodeList?: string;
    }>,
    default: () => ({}),
  },
});

const modalFormRef = ref<InstanceType<typeof Form>>();
const inBoundDocId = ref<string>('');
const inBoundDocNumber = ref<string>('');
const origDocTypeCode = ref<string>('');

/** 提交时间 */
const applyTime = ref({
  // 开始时间
  applyStartTime: props.params?.applyStartTime || '',
  // 结束时间
  applyEndTime: props.params?.applyEndTime || '',
});

/** 入库弹窗 */
const [FormModal, formModalApi] = useVbenModal({
  confirmText: '确认入库',
  destroyOnClose: true,
  onBeforeClose: () => {
    inBoundDocId.value = '';
    inBoundDocNumber.value = '';
    return true;
  },
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

/** 关闭出入库弹窗 */
const [CloseModal, closeModalApi] = useVbenModal({
  confirmText: '取消入库',
  destroyOnClose: true,
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
  onBeforeClose: () => {
    inBoundDocId.value = '';
    inBoundDocNumber.value = '';
    return true;
  },
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      applyTime.value = {
        applyStartTime: '',
        applyEndTime: '',
      };

      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[75px]',
    },
    schema: useGridFormSchema(),
    collapsed: isEmpty(props.attr?.collapsed) ? false : props.attr?.collapsed,
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? false
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    checkboxConfig: {
      highlight: true,
    },
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          params.applyStartTime = applyTime.value.applyStartTime;
          params.applyEndTime = applyTime.value.applyEndTime;

          if (params.origDocTypeCodeList) {
            params.origDocTypeCodeList = params.origDocTypeCodeList.join(',');
          }

          if (params.applyUserList) {
            params.applyUserList = params.applyUserList.join(',');
          }

          return await getInBoundDocPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      keyField: 'supplierId',
    },
    // showOverflow: false,
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    inBoundDocNumberList: props.params?.inBoundDocNumberList?.split(',') || [],
    applyUserList: props.params?.applyUserList?.split(',') || [],
    origDocNumberList: props.params?.origDocNumberList?.split(',') || [],
    origDocTypeCodeList: props.params?.origDocTypeCodeList?.split(',') || [],
    isRectify: isEmpty(props.params?.isRectify) ? '' : props.params?.isRectify,
  });
});

// 取消入库
async function onCancelInbound(row: RowType) {
  inBoundDocId.value = row.inBoundDocId;
  inBoundDocNumber.value = row.inBoundDocNumber;
  origDocTypeCode.value = row.origDocTypeCode;

  closeModalApi
    .setState({
      title: `取消入库`,
    })
    .open();
}

function onActionClick(e: OnActionClickParams<RowType>) {
  switch (e.code) {
    case 'close': {
      onCancelInbound(e.row);
      break;
    }
    case 'inbound': {
      openInboundModal(e.row);
      break;
    }
  }
}

function openInboundModal(row: RowType) {
  inBoundDocId.value = row.inBoundDocId;
  inBoundDocNumber.value = row.inBoundDocNumber;
  formModalApi
    .setState({
      title: `确认入库`,
    })
    .open();
}

function closeInboundModal() {
  formModalApi.close();
}

/** 数据导出 */
async function exportPendingInBoundDocHandle() {
  try {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    formValues.applyStartTime = applyTime.value.applyStartTime;
    formValues.applyEndTime = applyTime.value.applyEndTime;
    const response = await exportPendingInBoundDoc(formValues);
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  }
}

function onInboundSuccess() {
  closeInboundModal();
  gridApi.query();
}

function onInboundLoading(loading: boolean) {
  formModalApi.setState({ loading });
}

function onCloseBoundLoading(loading: boolean) {
  closeModalApi.setState({ loading });
}

function onCloseBoundSuccess() {
  closeModalApi.close();
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="w-10/12">
      <Form
        ref="modalFormRef"
        :in-bound-doc-id="inBoundDocId"
        :in-bound-doc-number="inBoundDocNumber"
        @inbound-success="onInboundSuccess"
        @inbound-loading="onInboundLoading"
        @handle-cancel="formModalApi.close()"
      />
    </FormModal>

    <CloseModal class="w-3/5">
      <CloseBound
        :in-out-bound-doc-id="inBoundDocId"
        :in-out-bound-doc-number="inBoundDocNumber"
        :orig-doc-type-code="origDocTypeCode"
        @close-bound-loading="onCloseBoundLoading"
        @close-bound-success="onCloseBoundSuccess"
        @handle-cancel="closeModalApi.close()"
      />
    </CloseModal>
    <Grid>
      <template #form-applyTime>
        <ElDatePicker
          v-model="applyTime.applyStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, applyTime.applyEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="applyTime.applyEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, applyTime.applyStartTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            circle
            @click="exportPendingInBoundDocHandle"
            v-access:code="'wm:inbound:export'"
          >
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
