<script setup lang="ts">
import type { PropType } from 'vue';

import type { InBoundDocApi } from '#/api/warehouse-management';

import { ref, watch } from 'vue';

import Info from '../components/Info.vue';
import WarehouseItem from './warehouse-item/index.vue';

const props = defineProps({
  // 物料信息（包含仓库、库位信息）
  materialItemData: {
    type: Object as PropType<InBoundDocApi.InBoundItem>,
    default: () => ({}),
  },
  // 源单据类型标识
  origDocTypeCode: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['handleMaterialCode']);

export interface LocationItem {
  locationId: string;
  locationCode: string;
  locationName: string;
  unitPrice: number;
  batchNumber: string;
  actualQuantity: number;
}

export interface WarehouseGroup {
  warehouseId: string;
  warehouseCode: string;
  warehouseName: string;
  locationList: LocationItem[];
}

// 合并后每个仓库有效的数据（过滤出物料信息中，仓库在可入库仓库列表中的数据）
const warehouseItemList = ref<WarehouseGroup[]>([]);
// 处理子项数据，合并相同仓库的数据
function initWarehouseData(actualItemList: InBoundDocApi.ActualItem[]): {
  ItemList: WarehouseGroup[];
} {
  // 使用 Map 来按 warehouseId 分组
  const warehouseMap = new Map<string, WarehouseGroup>();

  actualItemList.forEach((item) => {
    const {
      actualQuantity,

      /* 批次号 */
      batchNumber,

      /* 库位编号 */
      locationCode,

      /* 库位id */
      locationId,

      /* 库位名称 */
      locationName,

      /* 均价（单价），默认不可见 */
      unitPrice,

      /* 仓库编号 */
      warehouseCode,

      /* 仓库id */
      warehouseId,

      /* 仓库名称 */
      warehouseName,
    } = item;

    // 如果这个仓库ID还没有记录，创建新记录
    if (!warehouseMap.has(warehouseId)) {
      warehouseMap.set(warehouseId, {
        warehouseId,
        warehouseCode,
        warehouseName,
        locationList: [],
      });
    }

    // 获取当前仓库记录
    const warehouse = warehouseMap.get(warehouseId)!;

    // 添加库位信息
    warehouse.locationList.push({
      locationId,
      locationCode,
      locationName,
      unitPrice,
      batchNumber,
      actualQuantity,
    });
  });

  return {
    // 将 Map 转换为数组
    ItemList: [...warehouseMap.values()],
  };
}
watch(
  () => props.materialItemData,
  async (newVal: InBoundDocApi.InBoundItem) => {
    const { ItemList } = initWarehouseData(newVal.actualItemList);
    warehouseItemList.value = ItemList;
  },
  { immediate: true },
);

const handleMaterialCode = () => {
  emits('handleMaterialCode', props.materialItemData.materialId);
};
</script>
<template>
  <div class="rounded-lg bg-white">
    <Info
      :material-item-data="materialItemData"
      @handle-material-code="handleMaterialCode"
    />

    <div class="mt-4">
      <template v-for="item in warehouseItemList" :key="item.warehouseId">
        <WarehouseItem :warehouse-item-data="item" />
      </template>
    </div>
  </div>
</template>
