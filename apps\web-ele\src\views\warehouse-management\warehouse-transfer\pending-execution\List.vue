<script></script>
<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage, ElMessageBox } from 'element-plus';

import {
  execTransferDoc,
  getTransferDocPage,
} from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/Form.vue';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      executorUserList?: string;
      submitEndTime?: string;
      submitStartTime?: string;
      submitUserList?: string;
      transferDocNumberList?: string;
      warehouseIdList?: string;
    }>,
    default: () => ({}),
  },
});

const transferDocId = ref<string>('');
const transferDocNumber = ref<string>('');

const submitTime = ref({
  // 开始时间
  submitStartTime: props.params?.submitStartTime || '',
  // 结束时间
  submitEndTime: props.params?.submitEndTime || '',
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      submitTime.value = {
        submitStartTime: '',
        submitEndTime: '',
      };

      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[75px]',
    },
    schema: useGridFormSchema(),
    collapsed: isEmpty(props.attr?.collapsed) ? false : props.attr?.collapsed,
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? false
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    checkboxConfig: {
      highlight: true,
    },
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            docStatusList: '30',
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          params.submitStartTime = submitTime.value.submitStartTime;
          params.submitEndTime = submitTime.value.submitEndTime;

          if (params.warehouseIdList) {
            params.warehouseIdList = params.warehouseIdList.join(',');
          }

          if (params.submitUserList) {
            params.submitUserList = params.submitUserList.join(',');
          }

          if (params.executorUserList) {
            params.executorUserList = params.executorUserList.join(',');
          }

          return await getTransferDocPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      keyField: 'transferDocId',
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    transferDocNumberList:
      props.params?.transferDocNumberList?.split(',') || [],
    warehouseIdList: props.params?.warehouseIdList?.split(',') || [],
    submitStartTime: props.params?.submitStartTime || '',
    submitEndTime: props.params?.submitEndTime || '',
    submitUserList: props.params?.submitUserList?.split(',') || [],
    executorUserList: props.params?.executorUserList?.split(',') || [],
  });
});

const [FormModal, formModalApi] = useVbenModal({
  confirmText: '新增调拨',
  destroyOnClose: true,
  onBeforeClose: () => {
    return true;
  },
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

function onTransferLoading(loading: boolean) {
  formModalApi.setState({ loading });
}

function onTransferSuccess() {
  formModalApi.close();
  gridApi.query();
}

const onView = (row: RowType) => {
  transferDocId.value = row.transferDocId;
  transferDocNumber.value = row.transferDocNumber;
  formModalApi
    .setState({
      title: '调拨详情',
    })
    .open();
};

async function onConfirm(row: RowType) {
  if (
    await ElMessageBox.confirm('确定调拨吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    await execTransferDoc(row.transferDocId);
    ElMessage.success('调拨成功');
    gridApi.query();
  }
}

function onActionClick(e: OnActionClickParams<RowType>) {
  switch (e.code) {
    case 'confirm': {
      onConfirm(e.row);
      break;
    }
    case 'view': {
      onView(e.row);
      break;
    }
  }
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="w-8/12">
      <Form
        :transfer-doc-id="transferDocId"
        :transfer-doc-number="transferDocNumber"
        @handle-cancel="formModalApi.close()"
        @exec-success="onTransferSuccess"
        @transfer-loading="onTransferLoading"
      />
    </FormModal>
    <Grid>
      <template #form-submitTime>
        <ElDatePicker
          v-model="submitTime.submitStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, submitTime.submitEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="submitTime.submitEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                submitTime.submitStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
    </Grid>
  </Page>
</template>
