<script setup lang="ts">
import type { StaffInfoType } from '#/api/common/staff';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getStaffInfo } from '#/api/common/staff';
import { getInOutReqDocDetail } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  /** 其它出入库申请单id */
  inOutReqDocId: {
    type: String,
    default: '',
  },
  /** 其它出入库申请单编号*/
  inOutReqDocNumber: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
/** 文件上传ref*/
const fileRef = ref();
/** 当前登录用户的员工信息 */
const staffData = ref<StaffInfoType>();
/** 调整信息表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass:
    'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3',
});
/** 校验表单 */
const validateForm = async () => {
  const verification = await formApi.validate();
  // 等待文件上传完成
  const serialNumber: any = await formApi?.getFieldComponentRef('serialNumber');
  const isCompleted = await serialNumber?.getCompleteStatus();
  if (!isCompleted) {
    ElMessage.warning('请等待附件上传完成');
    return;
  }
  return verification.valid;
};
/** 获取表单数据 */
const getFormData = async () => {
  const data = await formApi.getValues();
  return data;
};
/** 根据其它出入库申请单编号id获取数据 */
const getData = async () => {
  try {
    loading.value = true;
    const data = await getInOutReqDocDetail(
      props.inOutReqDocId,
      props.inOutReqDocNumber,
    );
    // 设置表单schema
    formApi.setState({ schema: useFormSchema(data.docStatus === '80') });
    // 赋值
    formApi.setValues(data);
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
onMounted(async () => {
  try {
    // 是编辑
    if (props.inOutReqDocId || props.inOutReqDocNumber) {
      getData();
    }
    // 是新增 设置使用人为当前账号
    else {
      staffData.value = await getStaffInfo();
      formApi.setValues({
        materialUser: staffData.value?.staffId,
      });
    }
  } catch {
    // ElMessage.error('获取当前账号失败');
  }
});
defineExpose({
  getFormData,
  validateForm,
  fileRef,
  formApi,
});
</script>
<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>其它出库申请信息</span>
    </template>
    <Form />
  </FormCard>
</template>
