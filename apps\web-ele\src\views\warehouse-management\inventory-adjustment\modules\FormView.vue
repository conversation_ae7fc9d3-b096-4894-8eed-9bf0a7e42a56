<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage, ElMessageBox, ElScrollbar } from 'element-plus';

import {
  closeInvcAdjustDoc,
  delInvcAdjustDoc,
  execInvcAdjustDoc,
  getInvcAdjustDoc,
  submitInvcAdjustDoc,
} from '#/api/warehouse-management';
import ApprovalTimeline from '#/components/approval-timeline/Index.vue';
import FormCard from '#/components/form-card/Index.vue';

import AdjustFormView from './adjust-form/form-view/index.vue';
import MaterialFormView from './material-form/form-view/index.vue';

/** 共享数据 */
const sharedData = ref();
const loading = ref(false);
/** 获取库存调整单详细信息 */
const getData = async () => {
  try {
    loading.value = true;
    const res = await getInvcAdjustDoc(
      sharedData.value.invcAdjustDocId,
      sharedData.value.invcAdjustDocNumber,
    );
    sharedData.value.processInstanceId = res?.processInstanceId;
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};
const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      sharedData.value = modalApi.getData<Record<string, any>>();
      if (!sharedData.value.processInstanceId) {
        getData();
      }
    }
  },
});
/** 刷新列表 */
const refreshList = () => {
  sharedData.value.refreshList();
  modalApi.close();
};
/** 确认执行 */
const confirmExecute = async (invcAdjustDocId: string) => {
  try {
    await ElMessageBox.confirm('确定执行吗？', '提示', {
      type: 'warning',
    });
    await execInvcAdjustDoc(invcAdjustDocId);
    ElMessage.success('执行成功');
    refreshList();
  } catch (error) {
    console.error(error);
  }
};
/** 取消单据 */
const cancelDoc = async (invcAdjustDocId: string) => {
  try {
    await ElMessageBox.confirm('确定取消吗？', '提示', {
      type: 'warning',
    });
    await closeInvcAdjustDoc(invcAdjustDocId);
    ElMessage.success('取消成功');
    refreshList();
  } catch (error) {
    console.error(error);
  }
};
/** 删除单据 */
const deleteDoc = async (invcAdjustDocId: string) => {
  try {
    await ElMessageBox.confirm('确定删除吗？', '提示', {
      type: 'warning',
    });
    await delInvcAdjustDoc(invcAdjustDocId);
    ElMessage.success('删除成功');
    refreshList();
  } catch (error) {
    console.error(error);
  }
};
/** 提交单据 （直接提交）*/
const submitDoc = async (invcAdjustDocId: string) => {
  try {
    await ElMessageBox.confirm('确定提交吗？', '提示', {
      type: 'warning',
    });

    // 获取单据数据
    const data = await getInvcAdjustDoc(invcAdjustDocId, '', true);
    await submitInvcAdjustDoc(data);
    ElMessage.success('提交成功');
    refreshList();
  } catch (error) {
    console.error(error);
  }
};
defineExpose({
  loading,
});
</script>
<template>
  <Modal>
    <div v-loading="loading">
      <AdjustFormView
        :invc-adjust-doc-number="sharedData.invcAdjustDocNumber"
        :invc-adjust-doc-id="sharedData.invcAdjustDocId"
      />
      <MaterialFormView
        :invc-adjust-doc-id="sharedData.invcAdjustDocId"
        :invc-adjust-doc-number="sharedData.invcAdjustDocNumber"
        :doc-status="sharedData.docStatus"
      />
      <FormCard :is-footer="false" v-if="sharedData.processInstanceId">
        <template #title>
          <span>审核流程</span>
        </template>
        <ElScrollbar>
          <ApprovalTimeline
            :process-instance-id="sharedData.processInstanceId"
          />
        </ElScrollbar>
      </FormCard>
    </div>
    <template #center-footer>
      <ElButton
        v-if="sharedData.docStatus === '30'"
        type="primary"
        @click="confirmExecute(sharedData.invcAdjustDocId)"
        v-access:code="'wm:inventory:adjust:exec'"
      >
        确认执行
      </ElButton>
      <ElButton
        v-if="sharedData.docStatus === '30'"
        type="danger"
        @click="cancelDoc(sharedData.invcAdjustDocId)"
        v-access:code="'wm:inventory:adjust:close'"
      >
        取消单据
      </ElButton>
      <ElButton
        v-if="sharedData.docStatus === '00'"
        type="primary"
        @click="submitDoc(sharedData.invcAdjustDocId)"
        v-access:code="'wm:inventory:adjust:submit'"
      >
        提交单据
      </ElButton>
      <ElButton
        v-if="sharedData.docStatus === '00'"
        type="danger"
        @click="deleteDoc(sharedData.invcAdjustDocId)"
      >
        删除单据
      </ElButton>
    </template>
  </Modal>
</template>
