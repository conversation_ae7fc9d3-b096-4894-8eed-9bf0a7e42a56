<script setup lang="ts">
import type { VbenFormSchema } from '@girant/adapter';

import type {
  TransferQueryApi,
  WarehouseInfoApi,
} from '#/api/warehouse-management';

import { h, onMounted, ref } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
import { useVbenForm, z } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import {
  getTransferDocDetail,
  getWarehouseList,
} from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';
import RemarkOptionSelect from '#/components/remark-option-select/index.vue';

const props = defineProps({
  transferDocId: {
    type: String,
    default: '',
  },
  transferDocNumber: {
    type: String,
    default: '',
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['changeWarehouseId']);

const transferDocDetail = ref<TransferQueryApi.GetTransferDocDetailResponse>(
  {} as TransferQueryApi.GetTransferDocDetailResponse,
);

const schema: VbenFormSchema[] = [
  {
    component: () => {
      return h('span', null, '系统自动生成');
    },
    fieldName: 'transferDocNumber',
    label: '调拨单号',
  },
  {
    component: 'ApiSelect',
    componentProps: {
      filterable: true,
      placeholder: '请选择仓库',
      afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
        const warehouseList = data.map((item) => ({
          label: item.warehouseName,
          value: item.warehouseId,
        }));
        return warehouseList;
      },
      api: () => {
        return getWarehouseList({
          isLock: false,
          isEnable: true,
          limitDocs: 'WM0070',
          limitDocType: '10',
        });
      },
      onChange: (value: string) => {
        emits('changeWarehouseId', value);
      },
    },
    fieldName: 'warehouseId',
    formItemClass: 'col-span-1',
    label: '所属仓库',
    rules: 'selectRequired',
  },
  {
    component: 'Input',
    fieldName: 'remarkOptionList',
    label: '调拨原因',
    formItemClass: 'col-span-full',
    rules: 'selectRequired',
  },

  {
    component: 'Textarea',
    componentProps: {
      autosize: { minRows: 3 },
      maxlength: 500,
      placeholder: '请输入',
      showWordLimit: true,
    },
    dependencies: {
      rules(values: any) {
        if (values.remark) {
          return z.string().max(500, { message: '长度不超过500' });
        }
        return null;
      },
      triggerFields: ['remark'],
    },
    fieldName: 'remark',
    formItemClass: 'col-span-full',
    label: '调拨原因说明',
  },
  {
    component: 'Upload',
    fieldName: 'serialNumber',
    formItemClass: 'col-span-full',
    label: '附件',
  },
];

/** 附件*/
const uploadSerialNumberRef = ref();

const [Form, formApi] = useVbenForm({
  schema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});

const getFormData = async (): Promise<
  false | Omit<TransferQueryApi.SubmitTransferDocParams, 'transferItemList'>
> => {
  const validateRes = await formApi.validate();
  if (!validateRes.valid) {
    return false;
  }
  const data = await formApi.getValues();

  return data as Omit<
    TransferQueryApi.SubmitTransferDocParams,
    'transferItemList'
  >;
};

const filesSubmitSuccess = (field: string, res: any) => {
  formApi.setFieldValue(field, res.serialNumber);
};

// 获取详情
const getTransferDocDetailHandle = async () => {
  try {
    const transferDocDetailRes = await getTransferDocDetail({
      transferDocId: props.transferDocId,
      transferDocNumber: props.transferDocNumber,
      isQueryItem: false,
    });
    transferDocDetail.value = transferDocDetailRes;

    const formatRes = {
      ...transferDocDetailRes,
      remarkOptionList:
        transferDocDetailRes?.remarkOptionList?.map(
          (item: any) => item.optionId,
        ) || [],
    };
    return formatRes;
  } catch {
    ElMessage.error('获取调拨单据失败');
    return {} as TransferQueryApi.GetTransferDocDetailResponse;
  }
};

onMounted(async () => {
  if (props.isEdit) {
    if (props.transferDocId || props.transferDocNumber) {
      await getTransferDocDetailHandle();
      formApi.setValues(transferDocDetail.value);
      // emits('changeWarehouseId', transferDocDetail.value.warehouseId);
    } else {
      ElMessage.error('调拨单号或调拨单ID不能为空');
    }
  }
});

const handleRemarkOptionListChange = (_: any, isDescRequired: boolean) => {
  formApi.updateSchema([
    {
      rules: isDescRequired ? 'required' : '',
      fieldName: 'remark',
    },
  ]);
};

defineExpose({
  getFormData,
});
</script>

<template>
  <IconFont
    v-if="transferDocDetail.docStatus === '20'"
    name="shenhebutongguo"
    :size="150"
    class="absolute right-20 top-14 z-40 !text-red-500"
  />
  <FormCard :is-footer="false" title="调拨信息">
    <Form>
      <template #remarkOptionList="{ modelValue }">
        <RemarkOptionSelect
          :model-value="modelValue"
          doc-code="WM0061"
          doc-field-code="remark"
          @update:model-value="
            (optionIds) => {
              formApi.setFieldValue('remarkOptionList', optionIds);
            }
          "
          @change="handleRemarkOptionListChange"
        />
      </template>
      <template #serialNumber="row">
        <div class="w-full">
          <UploadFiles
            ref="uploadSerialNumberRef"
            mode="editMode"
            :show-operat-button="false"
            :show-table="uploadSerialNumberRef?.fileList?.length > 0"
            :show-thumbnail="false"
            :auto-upload="true"
            :serial-number="row.value"
            @files-submit-success="filesSubmitSuccess('serialNumber', $event)"
          >
            <template #trigger>
              <div class="text-center">
                <i class="icon-[bx--folder] mx-auto h-12 w-12"></i>
                <p class="mt-2">点击或将文件拖拽到这里上传</p>
                <p class="text-xs text-gray-500">
                  支持格式：.rar .zip .doc .docx .pdf .jpg...
                </p>
              </div>
            </template>
          </UploadFiles>
        </div>
      </template>
    </Form>
  </FormCard>
</template>
