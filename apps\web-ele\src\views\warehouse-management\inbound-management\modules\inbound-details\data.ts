import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import { h, markRaw } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElInputTag, ElTag } from 'element-plus';

import {
  getDictItemList,
  getDocStatusInfo,
  getEnumByName,
  getMaterialCategoryTree,
  getOriginalDocConfigList,
} from '#/api';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

const WMORIGINAL_DOCTYPE_ENUMKEY_LIST = new Set(['IN', 'IN_AND_OUT']);

// 查询参数类型
export interface SearchParams {
  inBoundDocNumberList: string;
  applyUserList: string;
  origDocTypeCodeList: string[];
  origDocNumberList: string;
  applyTime: string[];
  docStatusList: string[];
  isStandard: boolean;
  materialName: string;
  materialCodeList: string[];
  materialAttributeList: string[];
  materialTypeList: string[];
  materialCategoryList: string[];
  warehouseName: string;
  locationName: string;
  batchNumber: string;
  closeTime: string[];
}

// 表格数据类型
export interface RowType {
  applyTime: string;
  applyUser: string;
  applyUserDeptId: string;
  applyUserDeptName: string;
  applyUserName: string;
  inBoundDocId: string;
  inBoundDocNumber: string;
  isRectify: boolean;
  origDocId: string;
  origDocNumber: string;
  origDocTypeCode: string;
  origDocTypeName: string;
  pictureFileId: string;
  materialCode: string;
  materialName: string;
  materialAttributeLabel: string;
  applyQuantitySum: number;
  baseUnitLabel: string;
  actualQuantitySum: number;
  executorUserName: string;
  docStatusLabel: string;
  materialSpecs: string;
  materialCategoryName: string;
  docStatus: string;
  materialTypeLabel: string;
  materialCategoryLabel: string;
}

/** 查询 */
export function useGridFormSchema(
  docStatusListParams: string[],
): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'inBoundDocNumberList',
      label: '入库单号',
    },
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'origDocNumberList',
      label: '申请单号',
    },

    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入物料名称',
      },
      fieldName: 'materialName',
      label: '物料名称',
    },

    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'materialCodeList',
      label: '物料编号',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const res = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));

          return res;
        },
        api: () => {
          return getDictItemList('baseMaterialAttribute');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'materialAttributeList',
      label: '物料属性',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const res = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));

          return res;
        },
        api: () => {
          return getDictItemList('baseMaterialType');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'materialTypeList',
      label: '物料大类',
    },

    {
      component: 'ApiTreeSelect',
      componentProps: {
        afterFetch: (data: any) => {
          // 转换函数
          function convertDeptData(data: any) {
            return {
              label: data.categoryName,
              value: data.categoryCode,
              children: data.children
                ? data.children.map((child: any) => convertDeptData(child))
                : [],
            };
          }
          // 执行转换
          const convertedData = data.map((data: any) => convertDeptData(data));
          return convertedData;
        },
        api: () => {
          return getMaterialCategoryTree();
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        filterable: true,
        maxCollapseTags: 1,
        multiple: true,
        showCheckbox: true,
      },
      defaultValue: [],
      fieldName: 'materialCategoryList',
      formItemClass: '',
      label: '物料细类',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入仓库名称',
      },
      fieldName: 'warehouseName',
      label: '仓库名称',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入库位名称',
      },
      fieldName: 'locationName',
      label: '库位名称',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入批次号',
      },
      fieldName: 'batchNumber',
      label: '批次号',
    },

    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '标准', value: true },
          { label: '非标准', value: false },
        ],
      },
      defaultValue: '',
      fieldName: 'isStandard',
      label: '是否标准物料',
    },

    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'applyUserList',
      label: '申请人',
      modelPropName: 'value',
      formItemClass: 'col-span-1',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const workStatusTypeList = data.map((item: any) => ({
            label: item.docStatusLabel,
            value: item.docStatusKey,
          }));
          workStatusTypeList.push({ label: '全部', value: '' });
          return workStatusTypeList;
        },
        api: () => {
          return getDocStatusInfo('WM0010');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: docStatusListParams,
      fieldName: 'docStatusList',
      formItemClass: docStatusListParams.length > 0 ? 'hidden' : '',
      label: '入库单状态',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: async () => {
          const EnumRes = await getEnumByName('WmDocTypeEnums');

          const list = EnumRes.filter((item: any) => {
            return WMORIGINAL_DOCTYPE_ENUMKEY_LIST.has(item.enumKey);
          }).map((item: any) => item.enumValue);

          const OriginalDocConfigRes = await getOriginalDocConfigList({
            originalDocTypeList: list.join(','),
          });

          return OriginalDocConfigRes.map((item: any) => ({
            label: item.docName,
            value: item.docCode,
          }));
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'origDocTypeCodeList',
      label: '入库类型',
    },

    {
      component: 'Input',
      fieldName: 'applyTime',
      formItemClass: 'col-span-2',
      label: '提交时间',
    },

    {
      component: 'Input',
      fieldName: 'closeTime',
      formItemClass: 'col-span-2',
      label: '关闭时间',
    },
  ];
}

/** 判断tag类型 */
const getTagType = (type: string) => {
  switch (type) {
    // 取消审核中
    case 'cancelAudit': {
      return 'warning';
    }
    // 已关闭
    case 'closed': {
      return 'info';
    }

    // 待入库
    case 'pending': {
      return 'primary';
    }
    // 已入库
    case 'stocked': {
      return 'success';
    }
    default: {
      return 'info';
    }
  }
};

/** 表格 */
export function useColumns<T = RowType>(
  onActionClick: OnActionClickFn<T>,
  docStatusListParams: string[],
): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 40,
    },

    {
      field: 'materialCode',
      slots: {
        default: ({ row }) => {
          return h(
            'div',
            {
              class: 'inline-flex px-2',
            },
            [
              h(
                'div',
                { class: 'w-[45px] mr-1' },
                row.isStandard
                  ? ''
                  : h(ElTag, { type: 'danger' }, { default: () => '非标' }),
              ),
              h('span', { class: 'flex-1' }, row.materialCode),
            ],
          );
        },
      },
      title: '物料编号',
      minWidth: 220,
      align: 'left',
      headerAlign: 'center',
    },

    {
      slots: {
        default: ({ row }) => {
          return h(
            ElTag,
            {
              type: getTagType(row.docStatus),
            },
            {
              default: () => row.docStatusLabel,
            },
          );
        },
      },
      field: 'docStatusLabel',
      title: '入库单状态',
      visible: docStatusListParams.length === 0,
      width: 120,
    },

    {
      field: 'docStatus',
      title: '入库单状态值',
      visible: false,
    },

    {
      field: 'materialName',
      title: '物料名称',
      width: 150,
    },

    {
      slots: {
        default: ({ row }) =>
          h(ImageViewer, {
            imgId: row.pictureFileId,
            imgCss: 'h-[45px]',
          }),
      },
      field: 'pictureFileId',
      title: '物料图片',
      width: 150,
    },

    {
      field: 'materialSpecs',
      title: '具体规格',
      minWidth: 230,
    },

    {
      field: 'applyQuantitySum',
      title: '入库数量',
      width: 100,
    },

    {
      field: 'baseUnitLabel',
      title: '单位',
      width: 100,
    },

    {
      field: 'materialAttributeLabel',
      title: '物料属性',
      width: 100,
      visible: false,
    },

    {
      field: 'materialTypeLabel',
      title: '物料大类',
      width: 100,
      visible: false,
    },
    {
      field: 'materialCategoryName',
      title: '物料细类',
      width: 100,
      visible: false,
    },

    {
      slots: {
        default: ({ row }) => {
          return h(
            'div',
            {
              class: 'inline-flex px-2',
            },
            [
              h(
                'div',
                { class: 'w-[45px] mr-1' },
                row.isRectify
                  ? h(ElTag, { type: 'primary' }, { default: () => '补录' })
                  : '',
              ),
              h('span', { class: 'flex-1' }, row.inBoundDocNumber),
            ],
          );
        },
      },
      field: 'inBoundDocNumber',
      title: '入库单号',
      minWidth: 250,
      align: 'left',
      headerAlign: 'center',
    },

    {
      field: 'origDocTypeName',
      minWidth: 150,
      title: '入库类型',
    },
    {
      field: 'origDocNumber',
      minWidth: 180,
      title: '申请单号',
    },

    {
      field: 'applyUserName',
      title: '申请人',
      width: 'auto',
    },

    {
      field: 'applyTime',
      title: '提交时间',
      width: 150,
    },

    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'inBoundDocNumber',
          nameTitle: '操作',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'view',
            label: '查看',
            type: 'info',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 100,
    },
  ];
}
