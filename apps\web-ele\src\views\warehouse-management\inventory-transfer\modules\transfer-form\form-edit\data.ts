import type { VbenFormSchema } from '@girant/adapter';

import type { Ref } from 'vue';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';

import { getActiveWarehouseList } from '#/api';

/** 获取仓库列表 */
export const getWarehouseList = async (docCode: string) => {
  const res = await getActiveWarehouseList({
    limitDocType: docCode,
  });
  // 提取出仓库id和仓库名称
  const options = res?.map((item) => ({
    label: item.warehouseName,
    value: item.warehouseId,
    disabled: false,
  }));

  return options;
};

const filterWarehousesList = (options: any[], filterId: string) => {
  return options.map((item) => {
    // 如果当前选项的value等于目标ID，则禁用它
    if (item.value === filterId) {
      return {
        ...item,
        disabled: true,
      };
    }
    // 否则保持原有状态
    return item;
  });
};

/** 调拨单信息 */
export function useFormSchema(
  warehousesList: Ref<any[]>,
  oldWarehouseChange: (warehouseId: string) => Promise<void>,
  targetWarehouseChange: (warehouseId: string) => Promise<void>,
): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'transferDocNumber',
      label: '单据编号',
      componentProps: {
        disabled: true,
        placeholder: '系统默认自动生成',
      },
    },
    {
      component: 'Input',
      fieldName: 'docCode',
      label: '调拨类型',
      rules: 'selectRequired',
    },
    {
      component: 'Select',
      componentProps: {
        maxCollapseTags: 1,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        options: [],
        placeholder: '请选择仓库',
      },
      dependencies: {
        async componentProps(values) {
          return values.docCode
            ? {
                options: filterWarehousesList(
                  warehousesList.value,
                  values.targetWarehouseId,
                ),
                onChange: oldWarehouseChange,
              }
            : {
                disabled: true,
              };
        },

        triggerFields: ['docCode', 'targetWarehouseId'],
      },
      defaultValue: '',
      fieldName: 'oldWarehouseId',
      formItemClass: 'col-span-1',
      label: '调出仓库',
      rules: 'selectRequired',
    },
    {
      component: 'Select',
      componentProps: {
        maxCollapseTags: 1,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        options: [],
        placeholder: '请选择仓库',
      },
      dependencies: {
        async componentProps(values) {
          return values.docCode
            ? {
                options: filterWarehousesList(
                  warehousesList.value,
                  values.oldWarehouseId,
                ),
                onChange: targetWarehouseChange,
              }
            : {
                disabled: true,
              };
        },

        triggerFields: ['docCode', 'oldWarehouseId'],
      },
      defaultValue: '',
      fieldName: 'targetWarehouseId',
      formItemClass: 'col-span-1',
      label: '调入仓库',
      rules: 'selectRequired',
    },
    {
      component: 'Input',
      fieldName: 'remarkOptionList',
      label: '调拨原因',
      formItemClass: 'col-span-full items-start',
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '调拨原因说明',
      formItemClass: 'col-span-full items-start',
      componentProps: {
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
    },
    {
      component: h(UploadFiles, {
        mode: 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      label: '附件',
      formItemClass: 'col-span-full items-start',
    },
  ];
}
