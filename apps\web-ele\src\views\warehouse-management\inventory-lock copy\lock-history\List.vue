<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { InventoryLock } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useAccess } from '@vben/access';
import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElButton, ElDatePicker, ElMessage, ElTooltip } from 'element-plus';

import { exportInvcBlock, getInvcBlockPage } from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';

import Form from '../modules/Form.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});
const { hasAccessByCodes } = useAccess();
const isView = ref(false);
/** 物料id */
const materialId = ref<string>('');
const formRef = ref<InstanceType<typeof Form>>();
/** 库存锁库ID*/
const blockId = ref('');
/** 锁定时间 */
const blockTime = ref({
  blockStartTime: props.params?.blockStartTime,
  blockEndTime: props.params?.blockEndTime,
});
/** 解锁时间 */
const unblockTime = ref({
  unblockStartTime: props.params?.unblockStartTime,
  unblockEndTime: props.params?.unblockEndTime,
});
const exportLoading = ref(false);
/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onConfirm: () => {
    formRef.value?.onSubmit();
  },
  showCancelButton: true,
  showConfirmButton: hasAccessByCodes(['wm:inventory:lock:lock']),
});
/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    blockTime.value = {
      blockStartTime: '',
      blockEndTime: '',
    };
    unblockTime.value = {
      unblockStartTime: '',
      unblockEndTime: '',
    };
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};
/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    handleReset,
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || true,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getInvcBlockPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            ...blockTime.value,
            ...unblockTime.value,
            isValid: false,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<InventoryLock.InvcBlockPage>,
});

/** 查看 */
const onView = (row: any) => {
  materialId.value = row.materialId;
  blockId.value = row.blockId;
  isView.value = true;
  formModalApi
    .setState({
      showConfirmButton: false,
      title: '查看',
    })
    .open();
};
/** 新增 */
const onAdd = () => {
  isView.value = false;
  blockId.value = '';
  formModalApi
    .setState({
      showConfirmButton: hasAccessByCodes(['wm:inventory:lock:lock']),
      title: '新增',
    })
    .open();
};
/** 导出 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportInvcBlock({
      ...formValues,
      ...blockTime.value,
      ...unblockTime.value,
      isValid: false,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};

/** 提交成功 */
const submitSuccess = () => {
  // 重新获取数据
  gridApi.query();
  formModalApi.close();
};
onMounted(async () => {
  await gridApi.formApi.setValues({
    materialCodeList: props.params?.materialCodeList?.split(',') || [],
    warehouseIdList: props.params?.warehouseIdList?.split(',') || [],
    blockUserList: props.params?.blockUserList?.split(',') || [],
    unblockUserList: props.params?.unblockUserList?.split(',') || [],
  });
});
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <FormModal class="h-full w-8/12">
      <Form
        ref="formRef"
        :is-view="isView"
        :block-id="blockId"
        :material-id="materialId"
        :is-valid="false"
        @submit-success="submitSuccess"
      />
    </FormModal>
    <Grid>
      <template #toolbar-actions>
        <ElButton
          type="primary"
          @click="onAdd"
          v-access:code="'wm:inventory:lock:lock'"
        >
          新增
        </ElButton>
      </template>
      <template #form-blockTime>
        <ElDatePicker
          v-model="blockTime.blockStartTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, blockTime.blockEndTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="blockTime.blockEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, blockTime.blockStartTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #form-unblockTime>
        <ElDatePicker
          v-model="unblockTime.unblockStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(
                time,
                unblockTime.unblockEndTime || new Date('2099-12-31'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="unblockTime.unblockEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                unblockTime.unblockStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            :loading="exportLoading"
            circle
            @click="exportHandle"
            v-access:code="'wm:inventory:lock:export'"
          >
            <template #icon>
              <IconFont name="xiazai" />
            </template>
          </ElButton>
        </ElTooltip>
      </template>
      <template #CellOperation="{ row }">
        <ElButton link size="small" @click="onView(row)" type="info">
          查看
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
