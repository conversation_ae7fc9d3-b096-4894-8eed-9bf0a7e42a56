<script setup lang="ts">
import { computed, defineAsyncComponent, markRaw, ref } from 'vue';

const props = defineProps({
  batchnumDocId: {
    type: String,
    default: '',
  },
  batchnumDocNumber: {
    type: String,
    default: '',
  },
  docCode: {
    type: String,
    default: '',
  },
  isView: {
    type: Boolean,
    default: false,
  },
  docStatus: {
    type: String,
    default: '',
  },
});

const emits = defineEmits([
  'handleCancel',
  'submitSuccess',
  'execSuccess',
  'saveSuccess',
  'batchNumberLoading',
  'closeSuccess',
]);

const From = computed(() => {
  switch (props.isView) {
    case false: {
      return markRaw(
        defineAsyncComponent(() => import('../../components/FormEdit.vue')),
      );
    }
    case true: {
      return markRaw(
        defineAsyncComponent(() => import('../../components/FormView.vue')),
      );
    }
    default: {
      return markRaw(
        defineAsyncComponent(() => import('../../components/FormEdit.vue')),
      );
    }
  }
});

const handleCancel = () => {
  emits('handleCancel');
};

const FromRef = ref();

const submitHandle = async () => {
  try {
    emits('batchNumberLoading', true);
    const result = await FromRef.value?.submitHandle();
    if (result) {
      emits('submitSuccess');
    }
  } catch {
    emits('batchNumberLoading', false);
  }
};

const saveHandle = async () => {
  try {
    emits('batchNumberLoading', true);
    const result = await FromRef.value?.saveHandle();
    if (result) {
      emits('saveSuccess');
    }
  } catch {
    emits('batchNumberLoading', false);
  }
};

const handleExec = async () => {
  try {
    emits('batchNumberLoading', true);
    const result = await FromRef.value?.execHandle();
    if (result) {
      emits('execSuccess');
    }
  } catch {
    emits('batchNumberLoading', false);
  }
};
</script>

<template>
  <div class="relative mb-12 h-full">
    <From
      ref="FromRef"
      :batchnum-doc-id="batchnumDocId"
      :batchnum-doc-number="batchnumDocNumber"
      :doc-code="docCode"
    />

    <div
      class="fixed bottom-0 right-0 z-10 h-[50px] w-full rounded-b-lg border-t bg-white p-2 text-right"
    >
      <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
      <ElButton
        type="primary"
        @click="saveHandle"
        v-if="!isView"
        v-access:code="'wm:batchnum:submit'"
      >
        暂存
      </ElButton>
      <ElButton
        type="primary"
        @click="submitHandle"
        v-if="!isView"
        v-access:code="'wm:batchnum:submit'"
      >
        提交
      </ElButton>
      <ElButton
        type="primary"
        @click="handleExec"
        v-if="docStatus === '30'"
        v-access:code="'wm:batchnum:exec'"
      >
        执行
      </ElButton>
    </div>
  </div>
</template>
