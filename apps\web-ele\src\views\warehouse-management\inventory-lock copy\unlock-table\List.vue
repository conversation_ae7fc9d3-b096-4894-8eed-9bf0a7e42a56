<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { InventoryLock } from '#/api/warehouse-management';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElButton, ElMessage, ElMessageBox } from 'element-plus';

import { getInvcBlockPage, unlock } from '#/api/warehouse-management';

import { useColumns } from './data';

const props = defineProps({
  /** 物料id */
  materialId: {
    type: String,
    default: '',
  },
  /** 物料编号 */
  materialCode: {
    type: String,
    default: '',
  },
  /** 仓库id */
  warehouseId: {
    type: String,
    default: '',
  },
  /** 仓库编号 */
  warehouseCode: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['unlockSuccessState']);

/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const res = await getInvcBlockPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            materialIdList: props.materialId,
            materialCode: props.materialCode,
            warehouseIdList: props.warehouseId,
            warehouseCodeList: props.warehouseCode,
            isValid: true,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<InventoryLock.InvcBlockPage>,
});
/** 提示框 */
const confirm = (content: string, title: string) => {
  return new Promise((resolve) => {
    ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
      .then(() => {
        resolve(true);
      })
      .catch(() => {});
  });
};
/** 提交解锁 */
const submitUnlock = async (blockId: string) => {
  try {
    await confirm('确认解锁吗？', '提示');
    await unlock(blockId);
    ElMessage.success('解锁成功');
    emits('unlockSuccessState', true);
    gridApi.query();
  } catch {
    ElMessage.error('解锁失败');
    emits('unlockSuccessState', false);
  }
};
</script>

<template>
  <Grid>
    <template #CellOperation="{ row }">
      <ElButton
        link
        size="small"
        type="primary"
        @click="submitUnlock(row.blockId)"
        v-access:code="'wm:inventory:lock:unlock'"
      >
        解锁
      </ElButton>
    </template>
  </Grid>
</template>
