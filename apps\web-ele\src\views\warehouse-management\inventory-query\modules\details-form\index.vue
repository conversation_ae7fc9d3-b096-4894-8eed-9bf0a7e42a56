<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { InventoryQueryApi } from '#/api';

import { onMounted, ref } from 'vue';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getInvcDetailByMaterIdAndWareId } from '#/api';
import FormCard from '#/components/form-card/Index.vue';

import { useColumns } from './data';

const props = defineProps({
  /** 仓库id */
  warehouseId: {
    type: String,
    default: '',
  },
  /** 物料id */
  materialId: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
const data = ref<InventoryQueryApi.InventoryDetailQuery>();
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    border: true,
    columns: useColumns(),
    data: [],
    showFooter: true,
    pagerConfig: {
      enabled: false,
    },
    maxHeight: '500',
    sortConfig: {
      multiple: true,
    },
    footerData: [],
  } as VxeTableGridOptions,
});

/** 获取库存明细 */
const getData = async () => {
  try {
    loading.value = true;
    data.value = await getInvcDetailByMaterIdAndWareId(
      props.materialId,
      props.warehouseId,
    );
    // 处理仓库名称和单位
    data.value?.invcDetailList?.forEach((item) => {
      item.warehouseName = data.value?.warehouseName;
      item.baseUnitLabel = data.value?.baseUnitLabel;
    });
    // 设置表格数据
    gridApi.setGridOptions({
      data: data.value?.invcDetailList,
      footerData: [
        {
          inventory: `合计:${data.value?.inventory || 0}`,
          unitPrice: `均价:${data.value?.unitPrice || 0}`,
        },
      ],
    });
    // 设置表格底部数据
  } catch {
    ElMessage.error('获取库存明细失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  if (props.materialId && props.warehouseId) {
    getData();
  }
});
defineExpose({
  gridApi,
  Grid,
});
</script>
<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>库存明细</span>
    </template>
    <Grid>
      <template #footer_inventory="{ row }">
        <span style="color: blue">{{ row.inventory }}</span>
      </template>
    </Grid>
  </FormCard>
</template>
