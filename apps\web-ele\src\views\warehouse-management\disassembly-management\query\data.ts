import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import type { WarehouseInfoApi } from '#/api';
import type { dictItemListType } from '#/api/common';

import { h, markRaw } from 'vue';

import { ElInputTag } from 'element-plus';

import { getEnableWarehouseList } from '#/api';
import { getDictItemList } from '#/api/common';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';
/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'assemblyDocNumberList',
      label: '单据编号',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.EnableWarehouse[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getEnableWarehouseList();
        },
      },
      fieldName: 'warehouseIdList',
      formItemClass: 'col-span-1',
      label: '所属仓库',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          // 过滤待提交 value=00
          const filterList = warehouseList.filter(
            (item) => item.value !== '00',
          );
          return filterList;
        },
        api: () => {
          return getDictItemList('wmAssemblyDocStatus');
        },
        multiple: true,
        maxCollapseTags: 2,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'docStatusList',
      label: '单据状态',
      formItemClass: 'col-span-1',
    },
    {
      component: h(DeptStaffTree, {
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'submitUserList',
      label: '申请人',
      formItemClass: 'col-span-1',
    },
    {
      component: h(DeptStaffTree, {
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'executorUserList',
      label: '执行人',
      formItemClass: 'col-span-1',
    },
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'productMaterialCodeList',
      label: '成品编号',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'productMaterialName',
      label: '成品名',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'submitTime',
      label: '提交时间',
      formItemClass: 'col-span-2',
    },
    {
      component: 'Input',
      fieldName: 'finishTime',
      label: '完成时间',
      formItemClass: 'col-span-2',
    },
  ];
}

/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      title: '单据编号',
      field: 'assemblyDocNumber',
      minWidth: 235,
    },
    {
      slots: {
        default: 'docStatusLabel',
      },
      title: '单据状态',
      field: 'docStatusLabel',
      minWidth: 110,
    },
    {
      slots: {
        default: 'productMaterialName',
      },
      title: '成品',
      field: 'productMaterialName',
      minWidth: 235,
    },
    {
      title: '成品规格',
      field: 'materialSpecs',
      minWidth: 260,
    },
    {
      slots: {
        default: 'quantity',
      },
      title: '数量',
      field: 'quantity',
      minWidth: 75,
    },
    {
      title: '申请人',
      field: 'submitUserName',
      minWidth: 150,
    },
    {
      title: '提交时间',
      field: 'submitTime',
      minWidth: 150,
    },
    {
      title: '执行人',
      field: 'executorUserName',
      minWidth: 150,
    },
    {
      title: '完成时间',
      field: 'finishTime',
      minWidth: 150,
    },
    {
      align: 'center',
      slots: {
        default: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      minWidth: 185,
      title: '操作',
    },
  ];
}
