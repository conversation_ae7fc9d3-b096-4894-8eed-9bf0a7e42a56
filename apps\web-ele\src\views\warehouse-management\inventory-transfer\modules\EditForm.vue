<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElButton, ElMessage, ElMessageBox } from 'element-plus';

import {
  delWareTransferDoc,
  saveOrModWareTransferDoc,
  submitWareTransferDoc,
} from '#/api';

import MaterialFormEdit from '../components/material-edit-form/index.vue';
import TransferFormEdit from '../components/transfer-edit-form/index.vue';

const loading = ref(false);
/** 共享数据 */
const sharedData = ref();
/** 调拨信息ref*/
const transferFormRef = ref();
/** 原料明细ref*/
const materialFormRef = ref();
/** 调出仓库 */
const oldWarehouseId = ref();
/** 调入仓库 */
const targetWarehouseId = ref();

const [Modal, modalApi] = useVbenModal({
  confirmText: '提交',
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    onSubmit();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      sharedData.value = modalApi.getData<Record<string, any>>();
    }
  },
});

/** 刷新列表 */
const refreshList = () => {
  sharedData.value.refreshList();
  modalApi.close();
};

/** 监听调出仓库变化 */
const oldWarehouseChange = async (warehouseId: string) => {
  oldWarehouseId.value = warehouseId;
};

/** 监听调入仓库变化 */
const targetWarehouseChange = async (warehouseId: string) => {
  targetWarehouseId.value = warehouseId;
};

const validateForm = async () => {
  // 校验调整信息表单 // 校验物料信息表单
  const [verification, verification2] = await Promise.all([
    transferFormRef.value?.validateForm(),
    materialFormRef.value?.validateForm(),
  ]);

  // 获取表单数据
  const data = await getFormData();
  if (data.transferItemList.length === 0) {
    ElMessage.error('请填写明细信息');
    return false;
  }
  if (!verification || !verification2) {
    ElMessage.error('请填写完整表单');
    return false;
  }

  // 检查物料是否重复
  const seenIds = new Set();
  for (const item of data.transferItemList) {
    // 检查当前materialId是否已存在
    if (seenIds.has(item.materialId)) {
      // 发现重复，返回true和重复的ID
      ElMessage.error('物料存在重复，请确保所有物料唯一');
      return false;
    }
    // 将当前ID添加到集合中
    seenIds.add(item.materialId);
  }

  return true;
};

/** 获取表单数据 */
const getFormData = async () => {
  const [data, data2] = await Promise.all([
    transferFormRef.value?.getFormData(),
    materialFormRef.value?.getFormData(),
  ]);
  return {
    ...data,
    transferItemList: data2,
  };
};

/** 公共弹窗 */
const dialog = async (content: string, title: string) => {
  try {
    await ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    });
    return true;
  } catch {
    return false;
  }
};

/** 提交表单 新增*/
const onSubmit = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    const isUpdate = !!sharedData.value.transferDocId;
    if (await dialog('确定提交单据吗？', '提示')) {
      loading.value = true;
      await submitWareTransferDoc({
        transferDocId: isUpdate ? sharedData.value.transferDocId : '',
        ...data,
      });
    }
    ElMessage.success(isUpdate ? '提交成功' : '新增成功');
    refreshList();
  } catch (error) {
    console.error(error);
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};

/** 提交暂存 */
const onSave = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    const isUpdate = !!sharedData.value.transferDocId;
    if (await dialog('确定提交暂存吗？', '提示')) {
      loading.value = true;
      await saveOrModWareTransferDoc({
        transferDocId: isUpdate ? sharedData.value.transferDocId : '',
        ...data,
      });
    }
    ElMessage.success('提交暂存成功');
    refreshList();
  } catch (error) {
    console.error(error);
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};

/** 删除单据 */
const deleteDoc = async () => {
  try {
    if (await dialog('确定删除单据吗？', '提示')) {
      loading.value = true;
      await delWareTransferDoc(sharedData.value.transferDocId);
    }
    ElMessage.success('删除成功');
    refreshList();
  } catch {
    ElMessage.error('删除失败');
  } finally {
    loading.value = false;
  }
};

defineExpose({
  onSubmit,
  onSave,
  validateForm,
  deleteDoc,
  loading,
});
</script>

<template>
  <Modal>
    <div v-loading="loading">
      <TransferFormEdit
        ref="transferFormRef"
        :transfer-doc-id="sharedData.transferDocId"
        :transfer-doc-number="sharedData.transferDocNumber"
        @change-old-warehouse="oldWarehouseChange"
        @change-target-warehouse="targetWarehouseChange"
      />

      <MaterialFormEdit
        ref="materialFormRef"
        :transfer-doc-id="sharedData.transferDocId"
        :transfer-doc-number="sharedData.transferDocNumber"
        :old-warehouse-id="oldWarehouseId"
        :target-warehouse-id="targetWarehouseId"
      />
    </div>

    <template #center-footer>
      <ElButton
        v-if="sharedData.transferDocId"
        @click="deleteDoc"
        type="danger"
      >
        删除
      </ElButton>
      <ElButton type="primary" @click="onSave()"> 暂存 </ElButton>
    </template>
  </Modal>
</template>
