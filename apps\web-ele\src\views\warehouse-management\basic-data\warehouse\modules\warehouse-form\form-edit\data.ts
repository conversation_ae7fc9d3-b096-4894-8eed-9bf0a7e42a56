import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

export type mapType = {
  label: string;
  value: string;
};
/** 基础资料 编辑 */
export function useFormSchema(isEdit: boolean): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'warehouseCode',
      label: '仓库编号',
      componentProps: {
        clearable: true,
        maxlength: 128,
        placeholder: '为空时系统自动生成',
        disabled: isEdit,
      },
    },
    {
      component: 'Input',
      fieldName: 'warehouseName',
      label: '仓库名称',
      componentProps: {
        clearable: true,
      },
      rules: 'required',
    },
    {
      component: h(DeptStaffTree, {
        clearable: true,
        showAllLevels: false,
      }),
      fieldName: 'managerUserId',
      modelPropName: 'value',
      label: '仓库负责人',
    },
    {
      component: 'Input',
      fieldName: 'locationDesc',
      label: '所在位置',
      componentProps: {
        clearable: true,
      },
      formItemClass: 'col-span-2',
      rules: 'required',
    },
    {
      component: 'Switch',
      componentProps: {
        class: 'w-auto',
      },
      fieldName: 'isEnable',
      label: '是否启用',
      defaultValue: true,
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      formItemClass: 'col-span-full items-start',
      componentProps: {
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
    },
  ];
}

/** 仓库策略编辑*/
export function useFormSchema2(): VbenFormSchema[] {
  return [
    {
      component: 'CheckboxGroup',
      fieldName: 'warehouseStrategy',
      label: '',
      componentProps: {
        options: [
          {
            label: '参与安全库存预警',
            value: 'isSafetyStockWarn',
          },
          {
            label: '参与呆滞分析',
            value: 'isObsoleteAnalysis',
          },
        ],
      },
      labelWidth: 30,
    },
    {
      component: 'Input',
      fieldName: 'limitMaterialTypes',
      label: '物料类型限制',
    },
    {
      component: 'Input',
      fieldName: 'inOutStrategy',
      label: '物料库存策略',
    },
    {
      component: 'Input',
      fieldName: 'limitDocType',
      label: '操作限制单据类型',
      defaultValue: '00',
    },
    {
      component: 'div',
      fieldName: 'limitDocs',
      formItemClass: 'items-baseline',
      wrapperClass: 'min-h-[22px]',
      label: '操作限制单据',
      dependencies: {
        if(values) {
          return !(values.limitDocType === '00');
        },
        triggerFields: ['limitDocType'],
      },
    },
  ];
}
