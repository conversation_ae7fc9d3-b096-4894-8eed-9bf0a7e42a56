<script setup lang="ts">
import type { TransferApi } from '#/api';

import { onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getWareTransferDocDetail } from '#/api';
import FormCard from '#/components/form-card/Index.vue';
import MaterialForm from '#/views/warehouse-management/basic-data/material/modules/material-form/index.vue';

import MaterialViewField from '../../../components/material-view-field/index.vue';
import { openModal } from '../../method';
import { useColumns } from './data';

const props = defineProps({
  /** 库存调拨单据ID */
  transferDocId: {
    type: String,
    default: '',
  },
  /** 库存调拨单据编号 */
  transferDocNumber: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
/** 库存调拨单数据 */
const data = ref<TransferApi.TransferDocDetail>();
/** 所选物料Id */
const productMaterialId = ref();

onMounted(() => {
  if (props.transferDocId || props.transferDocNumber) {
    getData();
  }
});

/**  获取数据*/
const getData = async () => {
  try {
    loading.value = true;
    const res = await getWareTransferDocDetail(
      props.transferDocId,
      props.transferDocNumber,
      true,
    );
    data.value = res;
    // 设置表单内容
    gridApi.setGridOptions({
      data: data.value?.transferItemList,
    });
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

/** 原料明细表单 */
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useColumns(),
    data: [],
    minHeight: 150,
    border: true,
    pagerConfig: {
      enabled: false,
    },
  },
});

/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  confirmText: '提交',
  showCancelButton: true,
  showConfirmButton: true,
});

/** 显示物料详情信息 */
const showProductMaterial = (row: any) => {
  productMaterialId.value = row.materialId;
  openModal(formModalApi, false, '原料详情');
};
</script>

<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>调拨明细</span>
    </template>

    <FormModal class="h-full w-10/12">
      <MaterialForm :material-id="productMaterialId" />
    </FormModal>

    <Grid>
      <template #materialName="{ row }">
        <MaterialViewField
          :transfer-doc-detail="data"
          :material-id="row.materialId"
          :material-name="row.materialName"
          :material-code="row.materialCode"
          :picture-file-id="row.pictureFileId"
          :transfer-quantity="row.transferQuantity"
          @click="showProductMaterial(row)"
        />
      </template>
    </Grid>
  </FormCard>
</template>
