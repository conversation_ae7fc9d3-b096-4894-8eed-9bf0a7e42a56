import type { VxeTableGridOptions } from '@girant/adapter';

import { h } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';

/** 出库申请明细 */
export function useColumns(docStatus: string): VxeTableGridOptions['columns'] {
  const oldData = [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      title: '物料',
      field: 'materialName',
      minWidth: 100,
    },
    {
      slots: {
        default: ({ row }: { row: any }) =>
          h(ImageViewer, {
            imgId: row.pictureFileId,
            imgCss: 'h-[50px]',
          }),
      },
      title: '图片',
      field: 'pictureFileId',
      width: 90,
    },
    {
      title: '规格型号',
      field: 'materialSpecs',
      minWidth: 100,
    },
    {
      title: '基本单位',
      field: 'baseUnitLabel',
      width: 65,
    },
    {
      title: '申请出库数量',
      field: 'applyQuantitySum',
      minWidth: 100,
    },
    {
      slots: {
        default: 'actualQuantitySum',
      },
      title: '实际出库数量',
      field: 'actualQuantitySum',
      minWidth: 100,
    },
  ];
  // 过滤实际入库数量
  let newData = oldData;
  if (docStatus !== '30') {
    newData = oldData.filter((item) => item.field !== 'actualQuantitySum');
  }
  return newData;
}
