<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse, isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import {
  exportOutBoundApplyDetailPage,
  getOutBoundApplyItemPage,
} from '#/api/warehouse-management/index';
import { isAfter, isBefore } from '#/utils/dateUtils';

import PrepForm from '../../material-pending/modules/Form.vue';
import FormToOutbound from '../FormToOutbound.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      applyEndTime?: string;
      applyStartTime?: string;
      applyUserList?: string;
      closeEndTime?: string;
      closeStartTime?: string;
      collectorUserList?: string;
      docStatusList?: string;
      isProxyExec?: unknown;
      isStandard?: unknown;
      materialAttributeList?: string;
      materialCategoryList?: string;
      materialCodeList?: string;
      materialName?: string;
      materialTypeList?: string;
      materialUserList?: string;
      origDocNumberList?: string;
      origDocTypeCodeList?: string;
      outBoundDocNumberList?: string;
    }>,
    default: () => ({}),
  },
});

const router = useRouter();

const docStatusList = computed(() => {
  if (props.params?.docStatusList) {
    return props.params.docStatusList.split(',');
  }

  const queryDocStatusList = router.currentRoute.value.query?.docStatusList;
  if (queryDocStatusList) {
    if (Array.isArray(queryDocStatusList)) {
      return queryDocStatusList.filter(
        (item): item is string => typeof item === 'string',
      );
    }
    if (typeof queryDocStatusList === 'string') {
      return queryDocStatusList.split(',');
    }
  }

  return [];
});

const FormToOutboundRef = ref<InstanceType<typeof FormToOutbound>>();
const outBoundDocId = ref<string>('');
const outBoundDocNumber = ref<string>('');
const docStatus = ref<string>('');

/** 提交时间 */
const applyTime = ref({
  // 开始时间
  startTime: props.params?.applyStartTime || '',
  // 结束时间
  endTime: props.params?.applyEndTime || '',
});

/** 关闭时间 */
const closeTime = ref({
  // 开始时间
  startTime: props.params?.closeStartTime || '',
  // 结束时间
  endTime: props.params?.closeEndTime || '',
});

/** 出库弹窗 */
const [FormModal, formModalApi] = useVbenModal({
  confirmText: '已出库详情',
  destroyOnClose: true,
  onBeforeClose: () => {
    outBoundDocId.value = '';
    outBoundDocNumber.value = '';
    return true;
  },
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

/** 备料弹窗 */
const [PrepModal, prepModalApi] = useVbenModal({
  confirmText: '确认备料',
  destroyOnClose: true,
  onBeforeClose: () => {
    outBoundDocId.value = '';
    outBoundDocNumber.value = '';
    return true;
  },

  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      applyTime.value = {
        startTime: '',
        endTime: '',
      };
      closeTime.value = {
        startTime: '',
        endTime: '',
      };
      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[75px]',
    },
    collapsed: isEmpty(props.attr?.collapsed) ? true : props.attr?.collapsed,
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? true
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows || 2,
    schema: useGridFormSchema(docStatusList.value),
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    checkboxConfig: {
      highlight: true,
    },
    columns: useColumns(onActionClick, docStatusList.value),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          params.applyStartTime = applyTime.value.startTime;
          params.applyEndTime = applyTime.value.endTime;

          params.closeStartTime = closeTime.value.startTime;
          params.closeEndTime = closeTime.value.endTime;

          return await getOutBoundApplyItemPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },

    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    outBoundDocNumberList:
      props.params?.outBoundDocNumberList?.split(',') || [],
    origDocNumberList: props.params?.origDocNumberList?.split(',') || [],
    materialName: props.params?.materialName || '',
    materialCodeList: props.params?.materialCodeList?.split(',') || [],
    applyUserList: props.params?.applyUserList?.split(',') || [],
    materialUserList: props.params?.materialUserList?.split(',') || [],
    collectorUserList: props.params?.collectorUserList?.split(',') || [],
    origDocTypeCodeList: props.params?.origDocTypeCodeList?.split(',') || [],
    isStandard: isEmpty(props.params?.isStandard)
      ? ''
      : props.params?.isStandard,
    materialAttributeList:
      props.params?.materialAttributeList?.split(',') || [],
    materialTypeList: props.params?.materialTypeList?.split(',') || [],
    materialCategoryList: props.params?.materialCategoryList?.split(',') || [],
    isProxyExec: isEmpty(props.params?.isProxyExec)
      ? ''
      : props.params?.isProxyExec,
  });
});

function onActionClick(e: OnActionClickParams<RowType>) {
  switch (e.code) {
    case 'view': {
      onView(e.row);
      break;
    }
  }
}
function onView(row: RowType) {
  docStatus.value = row.docStatus;
  outBoundDocId.value = row.outBoundDocId;
  outBoundDocNumber.value = row.outBoundDocNumber;
  switch (row.docStatus) {
    case '00': {
      formModalApi
        .setState({
          title: '确认出库',
        })
        .open();
      break;
    }
    case '10': {
      formModalApi
        .setState({
          title: '已出库详情',
          showCancelButton: true,
        })
        .open();
      break;
    }

    default: {
      formModalApi
        .setState({
          title: '已关闭详情',
          showCancelButton: true,
        })
        .open();
      break;
    }
  }
}

const openPrepModal = (docId: string, docNumber: string) => {
  formModalApi.close();

  outBoundDocId.value = docId;
  outBoundDocNumber.value = docNumber;
  prepModalApi
    .setState({
      title: `确认备料`,
    })
    .open();
};

function onOutboundSuccess() {
  formModalApi.close();
  gridApi.query();
}

function onOutboundLoading(loading: boolean) {
  formModalApi.setState({ loading });
}

/** 数据导出 */
async function exportOutBoundApplyDetailPageHandle() {
  try {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    formValues.applyStartTime = applyTime.value.startTime;
    formValues.applyEndTime = applyTime.value.endTime;
    formValues.closeStartTime = closeTime.value.startTime;
    formValues.closeEndTime = closeTime.value.endTime;
    const response = await exportOutBoundApplyDetailPage(formValues);
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  }
}

function onPrepSuccess() {
  prepModalApi.close();
  gridApi.query();
}

function onPrepLoading(loading: boolean) {
  prepModalApi.setState({ loading });
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="w-10/12">
      <FormToOutbound
        ref="FormToOutboundRef"
        :doc-status="docStatus"
        :out-bound-doc-id="outBoundDocId"
        :out-bound-doc-number="outBoundDocNumber"
        @bound-success="onOutboundSuccess"
        @bound-loading="onOutboundLoading"
        @open-prep-modal="openPrepModal"
        @handle-cancel="formModalApi.close()"
      />
    </FormModal>

    <PrepModal class="w-10/12">
      <PrepForm
        :out-bound-doc-id="outBoundDocId"
        :out-bound-doc-number="outBoundDocNumber"
        @bound-success="onPrepSuccess"
        @bound-loading="onPrepLoading"
      />
    </PrepModal>

    <Grid>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            circle
            @click="exportOutBoundApplyDetailPageHandle"
            v-access:code="'wm:outbound:export:list:item'"
          >
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>

      <template #form-applyTime>
        <ElDatePicker
          v-model="applyTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, applyTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="applyTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, applyTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #form-closeTime>
        <ElDatePicker
          v-model="closeTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, closeTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="closeTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, closeTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
    </Grid>
  </Page>
</template>
