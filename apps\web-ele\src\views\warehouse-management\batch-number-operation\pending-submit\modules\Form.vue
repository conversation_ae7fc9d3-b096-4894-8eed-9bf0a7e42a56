<script setup lang="ts">
import { ref } from 'vue';

import FormToBatchNumberOperation from '../../components/FormToBatchNumberOperation.vue';

defineProps({
  batchnumDocId: {
    type: String,
    default: '',
  },
  batchnumDocNumber: {
    type: String,
    default: '',
  },
  docCode: {
    type: String,
    default: '',
  },
  isView: {
    type: Boolean,
    default: false,
  },
  docStatus: {
    type: String,
    default: '',
  },
  materialId: {
    type: String,
    default: '',
  },
  warehouseId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits([
  'handleCancel',
  'submitSuccess',
  'saveSuccess',
  'batchNumberLoading',
]);

const handleCancel = () => {
  emits('handleCancel');
};

const FormToBatchNumberOperationRef =
  ref<InstanceType<typeof FormToBatchNumberOperation>>();

const submitHandle = async () => {
  try {
    emits('batchNumberLoading', true);
    const result = await FormToBatchNumberOperationRef.value?.submitHandle();
    emits('batchNumberLoading', false);
    if (result) {
      emits('submitSuccess');
    }
  } catch {
    emits('batchNumberLoading', false);
  }
};

const saveHandle = async () => {
  try {
    emits('batchNumberLoading', true);
    const result = await FormToBatchNumberOperationRef.value?.saveHandle();
    emits('batchNumberLoading', false);
    if (result) {
      emits('saveSuccess');
    }
  } catch {
    emits('batchNumberLoading', false);
  }
};
</script>

<template>
  <div class="h-full">
    <FormToBatchNumberOperation
      ref="FormToBatchNumberOperationRef"
      :batchnum-doc-id="batchnumDocId"
      :batchnum-doc-number="batchnumDocNumber"
      :doc-code="docCode"
      :doc-status="docStatus"
      :is-view="isView"
      :material-id="materialId"
      :warehouse-id="warehouseId"
    >
      <template #btn-group>
        <div class="flex items-center justify-end">
          <ElButton type="primary" @click="saveHandle"> 暂存 </ElButton>
          <ElButton type="primary" @click="submitHandle"> 提交 </ElButton>
          <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
        </div>
      </template>
    </FormToBatchNumberOperation>
  </div>
</template>
