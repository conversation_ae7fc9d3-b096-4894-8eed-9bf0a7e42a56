<script setup lang="ts">
import type { VbenFormProps, VbenFormSchema } from '@girant/adapter';

import type { BatchNumberOperationQueryApi } from '#/api/warehouse-management';
import type { LocationABatchNumberItem } from '#/views/warehouse-management/warehouse-transfer/components/LocationAbatchNumber.vue';

import { computed, h, nextTick, onMounted, ref, watch } from 'vue';

import { isEmpty } from '@vben/utils';

import { DynamicForm } from '@girant-web/dynamic-table-component';
import { useVbenForm, z } from '@girant/adapter';
import { ElButton, ElMessage } from 'element-plus';

import { getBatchnumDocDetail } from '#/api/warehouse-management';
import {
  generateBatchNumber,
  getLocationList,
} from '#/api/warehouse-management/index';
import IconFont from '#/components/IconFont/IconFont.vue';
import { add } from '#/utils/numberUtils';
import LocationAbatchNumber from '#/views/warehouse-management/warehouse-transfer/components/LocationAbatchNumber.vue';

import BatchNumberAlert from '../BatchNumberAlert.vue';
import Box from '../box.vue';

const props = defineProps({
  warehouseId: {
    type: String,
    default: '',
  },
  materialId: {
    type: String,
    default: '',
  },
  batchnumDocId: {
    type: String,
    default: '',
  },
  batchnumDocNumber: {
    type: String,
    default: '',
  },
  isView: {
    type: Boolean,
    default: false,
  },
});

const dynamicFormRef = ref<InstanceType<typeof DynamicForm>>();

const currentItemQuantity = ref<number>(0);

// 生成批次号
const generateBatchNumberForMaterialId = async () => {
  try {
    const batchNumber = await generateBatchNumber(props.materialId);
    return batchNumber;
  } catch {
    return '';
  }
};

const getAfterFormData = async () => {
  const formValuesPromises = await dynamicFormRef.value.getAllFormValues();
  const formDataList = await Promise.all(formValuesPromises);
  return formDataList;
};

// 拆分后数据变化后的操作
const afterFormDataChange = async () => {
  currentItemQuantity.value = 0;

  const afterFormData = await getAfterFormData();

  afterFormData.forEach((item: any) => {
    currentItemQuantity.value = add(
      currentItemQuantity.value,
      item.itemQuantity,
    );
  });
};

const locationListOptions = ref<{ label: string; value: string }[]>([]);

const getLocationListHandler = async () => {
  const locationListRes = await getLocationList({
    warehouseId: props.warehouseId,
    isLock: false,
    isPrepMaterial: true,
    isEnable: true,
  });

  locationListOptions.value = locationListRes.map((item) => ({
    label: item.locationName,
    value: item.locationId,
  }));
  return locationListRes;
};

watch(
  () => props.warehouseId,
  async (warehouseId) => {
    if (!warehouseId) {
      return;
    }

    await getLocationListHandler();
  },
  {
    immediate: true,
  },
);

const schema: VbenFormSchema[] = [
  {
    component: h(LocationAbatchNumber),

    componentProps: (_, dynamicFormApi) => {
      return {
        warehouseId: props.warehouseId,
        materialId: props.materialId,
        disabled: props.warehouseId === '',
        onChange: async (value: LocationABatchNumberItem) => {
          // 修改合并前数据
          dynamicFormApi.setValues({
            locationId: value.locationId,
            itemBatchNumber: value.batchNumber,
            inventory: value.inventory || 0,
          });
          // await beforeFormDataChange();
        },
      };
    },
    fieldName: 'locationAbatchNumber',
    label: '库位批次',
    labelWidth: 70,
    formItemClass: 'col-span-2',
    rules: z.string().min(1, '请选择库位批次'),
  },

  {
    component: 'Input',
    fieldName: 'locationId',
    label: '库位ID',
    formItemClass: 'hidden',
    labelWidth: 50,
  },
  {
    component: 'Input',
    fieldName: 'itemBatchNumber',
    formItemClass: 'hidden',
    label: '批次号',
    labelWidth: 60,
  },
  {
    component: 'Input',
    fieldName: 'inventory',
    formItemClass: 'hidden',
    label: '库存量',
    labelWidth: 60,
  },
  {
    component: 'InputNumber',
    fieldName: 'itemQuantity',
    label: '数量',
    componentProps: {
      min: 0,
      precision: 3,
      controlsPosition: 'right',
      onChange: () => {
        // beforeFormDataChange(false);
      },
    },
    dependencies: {
      triggerFields: ['locationAbatchNumber', 'inventory'],
      componentProps: (values) => {
        return {
          disabled:
            !values.locationAbatchNumber ||
            values.inventory === undefined ||
            values.inventory === 0,
          max: values.inventory,
        };
      },
    },
    defaultValue: 0,
    labelWidth: 60,
    rules: z.number().positive({ message: '数字必须大于0' }),
  },
];

/** 拆分前表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});

/** 拆分后表单 */
const formOptions: VbenFormProps = {
  schema: [
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        allowClear: true,
        filterOption: true,
        options: computed(() => locationListOptions.value),
        placeholder: '请选择',
        showSearch: true,
        disabled: computed(() => props.warehouseId === ''),
      },
      fieldName: 'locationId',
      label: '库位',
      rules: z.string().min(1, '请选择库位'),
      labelWidth: 60,
    },
    {
      component: 'Input',
      fieldName: 'mergeBatchNumber',
      label: '批次号',
      componentProps: {
        placeholder: '为空系统自动生成',
        clearable: true,
      },
      dependencies: {
        triggerFields: ['locationId'],
        componentProps: (values) => {
          return {
            disabled: !values.locationId,
          };
        },
      },
      renderComponentContent: (values: any) => {
        return {
          suffix: () => {
            return h('div', {}, [
              h(
                ElButton,
                {
                  link: true,
                  size: 'small',
                  disabled: !values.locationId,
                  onClick: async (e: MouseEvent) => {
                    e.stopPropagation();
                    try {
                      const batchNumber =
                        await generateBatchNumberForMaterialId();
                      values.mergeBatchNumber = batchNumber;
                    } catch {
                      ElMessage.error('生成批次号失败');
                    }
                  },
                },
                () => h(IconFont, { name: 'bianji', class: 'iconfont' }),
              ),
              h(BatchNumberAlert, {
                class: 'absolute right-[-30px] top-0',
                materialId: props.materialId,
                locationId: values.locationId,
                batchNumber: values.mergeBatchNumber,
              }),
            ]);
          },
        };
      },
      labelWidth: 60,
      // 选择第一个是div的子元素设置overflow-visible
      formItemClass: 'mr-10 col-span-2 [&>div]:!overflow-visible',
    },
    {
      component: 'InputNumber',
      fieldName: 'itemQuantity',
      label: '数量',
      componentProps: {
        min: 0,
        precision: 3,
        controlsPosition: 'right',
        onChange: () => {
          afterFormDataChange();
        },
      },
      dependencies: {
        triggerFields: ['locationId'],
        componentProps: (values) => {
          return {
            disabled: !values.locationId,
          };
        },
      },
      defaultValue: 0,
      labelWidth: 60,
      rules: z.number().positive({ message: '数字必须大于0' }),
    },
  ],
  wrapperClass: 'grid-cols-4 ',
};

/** ------------------拆分前数据--------------------- */
const validateBeforeFormData = async (): Promise<boolean> => {
  const validateRes = await formApi.validate();
  if (!validateRes.valid) {
    throw new Error('请检查拆分前数据');
  }
  return validateRes.valid;
};

const getBeforeFormData = async () => {
  const formValues = await formApi.getValues();

  if (formValues.itemQuantity < currentItemQuantity.value) {
    throw new Error('拆分数量不能大于拆分前数量');
  }
  return formValues;
};

/** ------------------拆分后数据--------------------- */
const validateAfterFormData = async (): Promise<boolean> => {
  const validateRes = await dynamicFormRef.value.validateAllForms(false);
  if (!validateRes) {
    throw new Error('请检查拆分后数据');
  }
  return validateRes;
};

const getSplitForm = async (): Promise<{
  afterFormData: Record<string, any>;
  beforeFormData: Record<string, any>;
}> => {
  const resList = await Promise.all([
    validateBeforeFormData(),
    validateAfterFormData(),
  ]);

  const res = resList.every((item) => item === true);

  if (res) {
    const beforeFormData = await getBeforeFormData();
    const afterFormData = await getAfterFormData();

    return {
      beforeFormData,
      afterFormData,
    };
  }
  return {
    beforeFormData: {},
    afterFormData: [],
  };
};

const loading = ref(false);

const batchnumDocDetail =
  ref<BatchNumberOperationQueryApi.GetBatchnumDocDetailResponse>();

const getBatchnumDocDetailHandle = async () => {
  try {
    loading.value = true;
    const res = await getBatchnumDocDetail({
      batchnumDocId: props.batchnumDocId,
      batchnumDocNumber: props.batchnumDocNumber,
      isQueryItem: true,
    });

    batchnumDocDetail.value = res;

    const formatData = {
      ...res,
      locationAbatchNumber: `${res.locationId}#${res.mergeBatchNumber}`,
      batchnumItemList: res.batchnumItemList.map((item: any) => ({
        ...item,
        mergeBatchNumber: item.itemBatchNumber,
      })),
    };
    return formatData;
  } catch {
    ElMessage.error('获取批次号处理单据详情失败');
    return null;
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  if (
    !props.isView &&
    isEmpty(props.batchnumDocId) &&
    isEmpty(props.batchnumDocNumber)
  ) {
    return;
  }
  if (isEmpty(props.batchnumDocId) && isEmpty(props.batchnumDocNumber)) {
    ElMessage.error('批次号处理单据ID或编号不能为空');
    return;
  }

  const res = await getBatchnumDocDetailHandle();
  if (!res) {
    return;
  }

  nextTick(() => {
    formApi.setValues({
      locationAbatchNumber: res.locationAbatchNumber,
      itemQuantity: res.mergeQuantity,
    });
    dynamicFormRef.value.removeAllForms();
    res.batchnumItemList.forEach((item: any) => {
      dynamicFormRef.value.initForm(item);
    });
    currentItemQuantity.value = res.mergeQuantity;
  });
});

defineExpose({
  getSplitForm,
});
</script>

<template>
  <div v-loading="loading">
    <Box title="拆分前">
      <Form />
    </Box>
    <div class="flex items-center justify-center">
      <IconFont
        name="icon-arrow-bottom2"
        :size="15"
        class="!text-primary-500 my-2"
      />
    </div>
    <Box
      :title="
        currentItemQuantity === 0
          ? '拆分后'
          : `拆分后 (当前数量: ${currentItemQuantity})`
      "
    >
      <DynamicForm
        :form-data="[]"
        ref="dynamicFormRef"
        :form-options="formOptions"
      />
    </Box>
  </div>
</template>
