<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse, isEmpty } from '@vben/utils';

import { ImageViewer } from '@girant-web/img-view-component';
import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import {
  exportOutBoundDetailPage,
  getOutBoundActualItemPage,
} from '#/api/warehouse-management/index';
import { createDisabledDate } from '#/views/warehouse-management/inventory-lock/modules/method';

import Form from '../outbound-already/modules/Form.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      applyEndTime?: string;
      applyStartTime?: string;
      applyUserList?: string;
      batchNumber?: string;
      collectorUserList?: string;
      executorEndTime?: string;
      executorStartTime?: string;
      isProxyExec?: unknown;
      isStandard?: unknown;
      locationName?: string;
      materialAttributeList?: string;
      materialCategoryList?: string;
      materialCodeList?: string;
      materialName?: string;
      materialTypeList?: string;
      materialUserList?: string;
      origDocNumberList?: string;
      origDocTypeCodeList?: string;
      outBoundDocNumberList?: string;
      warehouseName?: string;
    }>,
    default: () => ({}),
  },
});

const outBoundDocId = ref<string>('');
const outBoundDocNumber = ref<string>('');

/** 提交时间 */
const applyTime = ref({
  // 开始时间
  startTime: props.params?.applyStartTime || '',
  // 结束时间
  endTime: props.params?.applyEndTime || '',
});

/** 出库时间 */
const executorTime = ref({
  // 开始时间
  startTime: props.params?.executorStartTime || '',
  // 结束时间
  endTime: props.params?.executorEndTime || '',
});

/** 出库弹窗 */
const [FormModal, formModalApi] = useVbenModal({
  confirmText: '已出库详情',
  destroyOnClose: true,
  onBeforeClose: () => {
    outBoundDocId.value = '';
    outBoundDocNumber.value = '';
    return true;
  },
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      applyTime.value = {
        startTime: '',
        endTime: '',
      };
      executorTime.value = {
        startTime: '',
        endTime: '',
      };

      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[75px]',
    },
    collapsed: isEmpty(props.attr?.collapsed) ? true : props.attr?.collapsed,
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? true
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows || 2,
    schema: useGridFormSchema(),
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    checkboxConfig: {
      highlight: true,
    },
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          params.applyStartTime = applyTime.value.startTime;
          params.applyEndTime = applyTime.value.endTime;
          params.executorStartTime = executorTime.value.startTime;
          params.executorEndTime = executorTime.value.endTime;

          return await getOutBoundActualItemPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },

    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    outBoundDocNumberList:
      props.params?.outBoundDocNumberList?.split(',') || [],
    origDocNumberList: props.params?.origDocNumberList?.split(',') || [],
    materialCodeList: props.params?.materialCodeList?.split(',') || [],
    materialName: props.params?.materialName || '',
    applyUserList: props.params?.applyUserList?.split(',') || [],
    materialUserList: props.params?.materialUserList?.split(',') || [],
    collectorUserList: props.params?.collectorUserList?.split(',') || [],
    origDocTypeCodeList: props.params?.origDocTypeCodeList?.split(',') || [],
    isStandard: isEmpty(props.params?.isStandard)
      ? ''
      : props.params?.isStandard,
    materialAttributeList:
      props.params?.materialAttributeList?.split(',') || [],
    materialTypeList: props.params?.materialTypeList?.split(',') || [],
    materialCategoryList: props.params?.materialCategoryList?.split(',') || [],
    isProxyExec: isEmpty(props.params?.isProxyExec)
      ? ''
      : props.params?.isProxyExec,
    warehouseName: props.params?.warehouseName || '',
    locationName: props.params?.locationName || '',
    batchNumber: props.params?.batchNumber || '',
  });
});

function onActionClick(e: OnActionClickParams<RowType>) {
  switch (e.code) {
    case 'view': {
      onView(e.row);
      break;
    }
  }
}
function onView(row: RowType) {
  outBoundDocId.value = row.outBoundDocId;
  outBoundDocNumber.value = row.outBoundDocNumber;

  formModalApi
    .setState({
      title: '已出库详情',
    })
    .open();
}

function onOutboundLoading(loading: boolean) {
  formModalApi.setState({ loading });
}

/** 数据导出 */
async function exportOutBoundDetailPageHandle() {
  try {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    formValues.applyStartTime = applyTime.value.startTime;
    formValues.applyEndTime = applyTime.value.endTime;
    formValues.executorStartTime = executorTime.value.startTime;
    formValues.executorEndTime = executorTime.value.endTime;
    const response = await exportOutBoundDetailPage(formValues);
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  }
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="w-10/12">
      <Form
        :out-bound-doc-id="outBoundDocId"
        :out-bound-doc-number="outBoundDocNumber"
        @bound-loading="onOutboundLoading"
        @handle-cancel="formModalApi.close()"
      />
    </FormModal>

    <Grid>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton circle @click="exportOutBoundDetailPageHandle">
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>

      <template #form-applyTime>
        <ElDatePicker
          v-model="applyTime.startTime"
          :disabled-date="createDisabledDate(false, applyTime)"
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="applyTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="createDisabledDate(true, applyTime)"
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #form-executorTime>
        <ElDatePicker
          v-model="executorTime.startTime"
          :disabled-date="createDisabledDate(false, executorTime)"
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="executorTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="createDisabledDate(true, executorTime)"
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #pictureFileId="{ row }">
        <ImageViewer
          v-if="row.pictureFileId"
          :src="row.pictureFileId"
          class="h-[45px] max-w-[110px] text-[30px]"
        />
      </template>
    </Grid>
  </Page>
</template>
