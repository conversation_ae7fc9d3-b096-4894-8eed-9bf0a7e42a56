<script setup lang="ts">
import type {
  InBoundDocApi,
  WarehouseListForMaterialListApi,
} from '#/api/warehouse-management';

import { computed, onMounted, ref } from 'vue';

import { ElMessage } from 'element-plus';

import {
  getActiveWarehouseListByMaterialList,
  getInBoundDocDetail,
} from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';
import TriangleCard from '#/components/triangle-card/Index.vue';
import MaterialsItem from '#/views/warehouse-management/inbound-management/components/materials-item/index.vue';

const props = defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
});

const inBoundData = ref<InBoundDocApi.InBoundDocDetail>(
  {} as InBoundDocApi.InBoundDocDetail,
);

const docStatus = computed(() => inBoundData.value.docStatus);

// const itemList = computed(() => inBoundData.value.inBoundItemList);
const itemList = ref<InBoundDocApi.InBoundItem[]>([]);

const allWarehouseListForMaterialList = ref<
  WarehouseListForMaterialListApi.WarehouseListForMaterialList[]
>([]);

/** 获取数据 */
const getInBoundDocDetailHandle = async () => {
  try {
    const inBoundRes = await getInBoundDocDetail({
      inBoundDocId: props.inBoundDocId,
      inBoundDocNumber: props.inBoundDocNumber,
      isQueryItem: true,
    });
    inBoundData.value = inBoundRes || ({} as InBoundDocApi.InBoundDocDetail);
    return inBoundRes;
  } catch {
    ElMessage.error('获取入库单据失败');
  }
};

onMounted(async () => {
  const inBoundRes = await getInBoundDocDetailHandle();

  itemList.value = inBoundRes?.inBoundItemList || [];

  if (itemList?.value?.length > 0) {
    const params = {
      materialIdList: itemList.value.map((item) => item.materialId).join(','),
      docTypeCode: inBoundRes?.origDocTypeCode,
    };
    // 批量获取物料可入库的仓库列表
    const WForMListRes = await getActiveWarehouseListByMaterialList(params);

    allWarehouseListForMaterialList.value = WForMListRes;
  }
});

// 所有表格数据是否都填入数量与申请数量一致
const isApplyQuantitySumEqual = computed(() => {
  return materialsItemRef.value.every(
    (item) => item?.MaterialsItemRef?.isApplyQuantitySumEqual,
  );
});

const materialsItemRef = ref<InstanceType<typeof MaterialsItem>[]>([]);
// 获取所有表单数据
const getAllFormData = async () => {
  if (!isApplyQuantitySumEqual.value) {
    ElMessage.error('填入数量与申请数量不一致');
    return false;
  }
  const allFormData = await Promise.all(
    materialsItemRef.value.map(async (item) => {
      const subData = await item?.MaterialsItemRef?.getSubData();
      return subData;
    }),
  );

  if (allFormData.includes(false)) {
    return false;
  }

  const submitData = allFormData.flat().map((item: any) => {
    const data = {
      ...item,
      actualQuantity: item?.applyQuantity || 0,
    };

    delete data.applyQuantity;

    return data;
  });

  const mergeData = {
    inBoundDocId: props.inBoundDocId,
    actualItemList: submitData,
  };

  return mergeData;
};

defineExpose({
  getAllFormData,
});
</script>

<template>
  <FormCard :is-footer="false">
    <template #title>
      <span>入库明细</span>
    </template>

    <template #titleMore>
      <span class="text-sm text-gray-500">
        共
        <span class="text-primary-500">
          {{ inBoundData?.inBoundItemList?.length || 0 }}
        </span>
        种物料
      </span>
    </template>
    <template #default>
      <template
        v-for="(materialItem, index) in itemList"
        :key="materialItem.materialId"
      >
        <TriangleCard :number="index + 1" title="" class="mb-5">
          <template #content>
            <MaterialsItem
              ref="materialsItemRef"
              :material-item-data="materialItem"
              :orig-doc-type-code="inBoundData.origDocTypeCode"
              :doc-status="docStatus"
              :warehouse-list-for-material-list="
                allWarehouseListForMaterialList.find(
                  (item) => item.materialId === materialItem.materialId,
                )?.warehouseList || []
              "
              :warehouse-list-for-material="
                allWarehouseListForMaterialList.find(
                  (item) => item.materialId === materialItem.materialId,
                )
              "
            />
          </template>
        </TriangleCard>
      </template>
    </template>
  </FormCard>
</template>
