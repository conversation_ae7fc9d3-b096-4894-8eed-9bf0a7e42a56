<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';

import { CountTo } from '@vben/common-ui';

import {
  getOutApplyItemNum,
  getOutBoundDocNum,
} from '#/api/warehouse-management';
import { WS } from '#/utils/socket/common-socketio';

export default defineComponent({
  components: { CountTo },
  emits: ['cardClick'],
  setup(_props, { emit }) {
    const wsType = [
      'wm.outbound.docstatus.rescind.passed',
      'wm.outbound.docstatus.rescind.checking',
      'wm.outbound.docstatus.rescind.reject',
    ];

    const DOC_STATUS = 'cancelAudit';
    const NumLoading = ref(true);
    const CountLoading = ref(true);
    const pendingStorageNum = ref(0); // 待审核 单
    const pendingStorageCount = ref(0); // 待审核 项

    const fetchPendingStorageCount = async () => {
      try {
        const response = await getOutBoundDocNum({
          docStatusList: DOC_STATUS,
        });
        pendingStorageNum.value = response.outBoundDocNum || 0;
      } catch (error) {
        console.error('获取取消出库待审核数据失败:', error);
      } finally {
        NumLoading.value = false;
      }
    };

    const fetchActualItemNum = async () => {
      try {
        const response = await getOutApplyItemNum({
          docStatusList: DOC_STATUS,
        });
        pendingStorageCount.value = response.applyItemNum || 0;
      } catch (error) {
        console.error('获取取消出库待审核项数失败:', error);
      } finally {
        CountLoading.value = false;
      }
    };

    const handleCardClick = () => {
      emit('cardClick', {
        name: 'cancel-outbound-approve',
        params: { docStatusList: DOC_STATUS },
        attrs: {
          wrapperClass:
            'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4',
          collapsed: true,
          collapsedRows: 2,
          showCollapseButton: true,
        },
      });
    };
    const fetchData = async () => {
      await fetchPendingStorageCount();
      await fetchActualItemNum();
    };
    onMounted(() => {
      WS.on(wsType, fetchData);
      fetchData();
    });

    return {
      pendingStorageNum,
      pendingStorageCount,
      NumLoading,
      CountLoading,
      handleCardClick,
    };
  },
});
</script>

<template>
  <div
    class="hover:bg-primary-100 bg-primary-50 cursor-pointer rounded-lg p-2 text-center text-black"
    @click="handleCardClick"
  >
    <div class="flex h-8 items-end justify-center text-sm">
      <span class="mb-0.5">取消出库待审核</span>
      <div class="mr-4 flex h-8 items-end justify-center">
        <span class="mx-1 text-xl font-bold">
          <CountTo
            v-loading="NumLoading"
            :start-val="0"
            :end-val="pendingStorageNum"
            :duration="1500"
            separator=""
          />
        </span>
        <span class="mb-0.5">单</span>
      </div>
      <div class="flex h-8 items-end justify-center">
        <span class="mx-1 text-xl font-bold">
          <CountTo
            v-loading="CountLoading"
            :start-val="0"
            :end-val="pendingStorageCount"
            :duration="1500"
            separator=""
          />
        </span>
        <span class="mb-0.5">项</span>
      </div>
    </div>
  </div>
</template>
