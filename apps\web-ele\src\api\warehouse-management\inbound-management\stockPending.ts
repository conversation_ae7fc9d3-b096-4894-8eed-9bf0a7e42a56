import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { baseDataPath, warehousePath } from '../../path';

// -------------------------------------------------员工查询-------------------------------------------------
export interface BaseDataStaff {
  [key: string]: any;
  records: Array<{ [key: string]: any }>;
  total: number;
}
/**
 * 查询员工信息分页列表
 * @param {string} staffName
 * @param {number} pageNum 当前页，默认第1页
 * @param {number} pageSize 分页数，默认每一页默认10条
 * @returns
 */

export async function getStaffPageList(params: Recordable<any>) {
  return requestClient.post<BaseDataStaff>(
    `${baseDataPath}/base/staff/getStaffPage`,
    { ...params },
  );
}

// -------------------------------------------------仓库查询-------------------------------------------------
export namespace WarehouseListForMaterialListApi {
  /** 查询可入库的仓库列表参数 */
  export interface WarehouseListForMaterialListParams {
    /** 物料ID列表 */
    materialIdList: string;
    /** 仓库ID列表 */
    warehouseIdList?: string;
    /** 单据类型标识 */
    docTypeCode?: string;
  }

  export interface warehouseItem {
    /* 可用量 */
    availableInventory: number;

    /* 库存量 */
    inventory: number;

    /* 仓库编号 */
    warehouseCode: string;

    /* 仓库ID */
    warehouseId: string;

    /* 仓库名称 */
    warehouseName: string;
  }

  /** 查询可入库的仓库列表响应 */
  export interface WarehouseListForMaterialList {
    /* 物料ID */
    materialId: string;

    /* 物料编号 */
    materialCode: string;

    /* 物料名称 */
    materialName: string;

    /* 默认仓库ID */
    mainWarehouseId: string;

    /* 默认库位ID */
    mainLocationId: string;

    /* */
    warehouseList: warehouseItem[];
  }
}

/**
 * 查询多个物料可使用的仓库列表
 */
export async function getWarehouseListForMaterialList(
  params: WarehouseListForMaterialListApi.WarehouseListForMaterialListParams,
) {
  return requestClient.post<
    WarehouseListForMaterialListApi.WarehouseListForMaterialList[]
  >(`${warehousePath}/wm/inventory/getWarehouseListForMaterialList`, {
    ...params,
  });
}

/**
 * 查询多个物料可使用的仓库列表
 */
export async function getActiveWarehouseListByMaterialList(
  params: WarehouseListForMaterialListApi.WarehouseListForMaterialListParams,
) {
  return requestClient.post<
    WarehouseListForMaterialListApi.WarehouseListForMaterialList[]
  >(`${warehousePath}/wm/inventory/getActiveWarehouseListByMaterialList`, {
    ...params,
  });
}

// -------------------------------------------------待入库查询-------------------------------------------------
/** 待入库单据接口 */
export namespace InBoundDocApi {
  /** 入库单据查询参数 */
  export interface InBoundDocPageParams {
    /** 入库单据号列表，多个用英文逗号分隔 */
    inBoundDocNumberList?: string;
    /** 源单据类型代码列表，多个用英文逗号分隔 */
    origDocTypeCodeList?: string;
    /** 源单据号列表，多个用英文逗号分隔 */
    origDocNumberList?: string;
    /** 申请人ID列表，多个用英文逗号分隔 */
    applyUserList?: string;
    /** 是否补录 */
    isRectify?: boolean;
    /** 申请起始时间，时间格式：yyyy-MM-dd HH:mm */
    applyStartTime?: string;
    /** 申请终止时间，时间格式：yyyy-MM-dd HH:mm */
    applyEndTime?: string;
    /** 当前页，默认第1页 */
    pageNum?: number;
    /** 分页数，默认每一页默认10条 */
    pageSize?: number;
  }

  /** 入库单据记录 */
  export interface InBoundDocRecord {
    /** 提交时间，时间格式：yyyy-MM-dd HH:mm */
    applyTime: Record<string, unknown>;
    /** 申请人ID */
    applyUser: string;
    /** 申请人部门ID */
    applyUserDeptId: string;
    /** 申请人部门名称 */
    applyUserDeptName: string;
    /** 申请人姓名 */
    applyUserName: string;
    /** 入库单据ID */
    inBoundDocId: string;
    /** 入库单据编号 */
    inBoundDocNumber: string;
    /** 是否补录 */
    isRectify: boolean;
    /** 源单据ID */
    origDocId: string;
    /** 源单据编号 */
    origDocNumber: string;
    /** 源单据类型标识 */
    origDocTypeCode: string;
    /** 源单据类型名称 */
    origDocTypeName: string;
  }

  /** 入库单据分页响应 */
  export interface InBoundDocPageRes {
    /** 是否成功 */
    success: boolean;
    /** 状态码 */
    code: number;
    /** 数据 */
    data: {
      /** 当前页 */
      pageNum: number;
      /** 分页条数 */
      pages: number;
      /** 分页数 */
      pageSize: number;
      /** 数据 */
      records: InBoundDocRecord[];
      /** 总条数 */
      total: number;
    };
    /** 消息 */
    msg: string;
  }

  /* 共有的子项数据 */
  export interface CommonItem {
    /* 批次号 */
    batchNumber: string;

    /* 库位编号 */
    locationCode: string;

    /* 库位id */
    locationId: string;

    /* 库位名称 */
    locationName: string;

    /* 均价（单价），默认不可见 */
    unitPrice: number;

    /* 仓库编号 */
    warehouseCode: string;

    /* 仓库id */
    warehouseId: string;

    /* 仓库名称 */
    warehouseName: string;
  }

  /** 申请明细子项 */
  export interface ApplyItem extends CommonItem {
    /* 申请数量 */
    applyQuantity: number;
  }

  /* 实际明细子项 */
  export interface ActualItem extends CommonItem {
    /* 实际数量 */
    actualQuantity: number;
  }

  /** 入库单据子项 */
  export interface InBoundItem {
    /* 实际总数 */
    actualQuantitySum: string;

    /* 申请明细列表 */
    applyItemList: ApplyItem[];

    /* 实际明细列表 */
    actualItemList: ActualItem[];

    /* 申请总数 */
    applyQuantitySum: number;

    /* 基本单位值，字典baseMaterialUnit */
    baseUnit: string;

    /* 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;

    /* 物料属性值，字典baseMaterialAttribute */
    materialAttribute: string;

    /* 物料属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;

    /* 物料细类 */
    materialCategory: string;

    /* 物料编号 */
    materialCode: string;

    /* 物料ID */
    materialId: string;

    /* 物料名称 */
    materialName: string;

    /* 物料大类值，字典baseMaterialType */
    materialType: string;

    /* 物料大类标签，字典baseMaterialType */
    materialTypeLabel: string;

    /* 物料图片ID */
    pictureFileId: string;

    /* 是否标准物料 */
    isStandard: boolean;
  }

  /** 入库单据详情 */
  export interface InBoundDocDetail {
    /* 入库单据ID */
    inBoundDocId: string;

    /* 入库单据编号 */
    inBoundDocNumber: string;

    /* 源单据类型标识 */
    origDocTypeCode: string;

    /* 源单据类型名称 */
    origDocTypeName: string;

    /* 源单据ID */
    origDocId: string;

    /* 源单据编号 */
    origDocNumber: string;

    /* 申请人ID */
    applyUser: string;

    /* 申请人姓名 */
    applyUserName: string;

    /* 申请人部门ID */
    applyUserDeptId: string;

    /* 申请人部门名称 */
    applyUserDeptName: string;

    /* 执行仓管员ID */
    executorUser: string;

    /* 执行仓管员姓名 */
    executorUserName: string;

    /* 执行仓管员部门ID */
    executorUserDeptId: string;

    /* 执行仓管员部门名称 */
    executorUserDeptName: string;

    /* 关闭人ID */
    closeUser: string;

    /* 关闭人姓名 */
    closeUserName: string;

    /* 关闭人部门ID */
    closeUserDeptId: string;

    /* 关闭人部门名称 */
    closeUserDeptName: string;

    /* 仓管员确认方式值，枚举WmInOutManagerConfirmMethodEnums */
    executorConfirmMethod: string;

    /* 仓管员确认方式标签，枚举WmInOutManagerConfirmMethodEnums */
    executorConfirmMethodLabel: string;

    /* 是否补录 */
    isRectify: boolean;

    /* 提交时间，时间格式：yyyy-MM-dd HH:mm */
    applyTime: Record<string, unknown>;

    /* 执行入库时间，时间格式：yyyy-MM-dd HH:mm */
    executorTime: Record<string, unknown>;

    /* 关闭时间，时间格式：yyyy-MM-dd HH:mm */
    closeTime: Record<string, unknown>;

    /* 是否自动完成入库 */
    isAutoIo: boolean;

    /* 单据状态值，字典wmInDocStatus */
    docStatus: string;

    /* 单据状态标签，字典wmInDocStatus */
    docStatusLabel: string;

    /* 备注 */
    remark: string;

    /* 附件流水号 */
    serialNumber: string;

    /* 入库单据子项列表 */
    inBoundItemList: InBoundItem[];

    /* 取消原因选项列表 */
    remarkOptionList: {
      /* 选项ID */
      optionId: string;
      /* 选项名称 */
      optionName: string;
    }[];
  }

  /** 入库单据详情请求 */
  export interface InBoundDocDetailParams {
    /** 入库单据id */
    inBoundDocId: string;
    /** 入库单据编号 */
    inBoundDocNumber?: string;
    /** 是否查询子项，默认不查询，true-查询，false-不查询 */
    isQueryItem?: boolean;
  }

  export interface InBoundDocDetailByInCancelDocParams {
    /** 入库取消申请单据id */
    inCancelDocId: string;
    /** 入库取消申请单据编号 */
    inCancelDocNumber?: string;
    /** 是否查询子项，默认不查询，true-查询，false-不查询 */
    isQueryItem?: boolean;
  }

  /** 入库单据详情响应 */
  export interface InBoundDocDetailRes {
    /* */
    success: boolean;

    /* */
    code: number;

    /* */
    data: InBoundDocDetail;

    /* */
    msg: string;
  }

  /* 实际明细列表 */
  export interface actualItem {
    /* 入库数量 */
    actualQuantity: number;

    /* 批次号 */
    batchNumber?: string;

    /* 库位ID */
    locationId: Record<string, unknown>;

    /* 物料ID */
    materialId: Record<string, unknown>;

    /* 均价（单价） */
    unitPrice?: number;

    /* 仓库ID */
    warehouseId: Record<string, unknown>;
  }

  /** 执行入库 */
  export interface execInbound {
    /** 入库单据id */
    inBoundDocId: string;
    /* 实际明细列表 */
    actualItemList?: actualItem[];
  }
}

/**
 * 查询待入库单据信息分页列表
 */
export async function getInBoundDocPage(
  params: InBoundDocApi.InBoundDocPageParams,
) {
  return requestClient.post<InBoundDocApi.InBoundDocPageRes>(
    `${warehousePath}/wm/inBound/pending/getInBoundDocPage`,
    { ...params },
  );
}

/**
 * 获取入库单据详细信息
 */
export function getInBoundDocDetail(
  params: InBoundDocApi.InBoundDocDetailParams,
) {
  const { inBoundDocId, inBoundDocNumber, isQueryItem = false } = params;
  return requestClient.get<InBoundDocApi.InBoundDocDetail>(
    `${warehousePath}/wm/inBound/getInBoundDocDetail?inBoundDocId=${inBoundDocId}&inBoundDocNumber=${inBoundDocNumber}&isQueryItem=${isQueryItem}`,
  );
}

/**
 * 根据入库取消申请单据获取入库单据详细信息
 */
export function getInBoundDocDetailByInCancelDoc(
  params: InBoundDocApi.InBoundDocDetailByInCancelDocParams,
) {
  const { inCancelDocId, inCancelDocNumber, isQueryItem = false } = params;
  return requestClient.get<InBoundDocApi.InBoundDocDetail>(
    `${warehousePath}/wm/inBound/getInBoundDocDetailByInCancelDoc?inCancelDocId=${inCancelDocId}&inCancelDocNumber=${inCancelDocNumber}&isQueryItem=${isQueryItem}`,
  );
}

/**
 * 根据源单据获取入库单据详细信息
 */
export function getInBoundDocDetailByOrigDoc(
  origDocId: string,
  origDocNumber?: string,
  isQueryItem?: boolean,
) {
  return requestClient.get<InBoundDocApi.InBoundDocDetail>(
    `${warehousePath}/wm/inBound/getInBoundDocDetailByOrigDoc`,
    {
      params: {
        origDocId,
        origDocNumber,
        isQueryItem,
      },
    },
  );
}

/**
 * 执行入库单据
 */
export async function execInBoundDoc(params: InBoundDocApi.execInbound) {
  return requestClient.post(
    `${warehousePath}/wm/inBound/execInBoundDoc`,
    {
      ...params,
    },
    {
      headers: { 'Content-Type-json': 'application/json' },
    },
  );
}

/**
 * 关闭入库单据
 */
export function closeInbound(inBoundDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/inBound/closeInBoundDoc/${inBoundDocId}`,
  );
}

/** 导出待入库单据列表*/
export async function exportPendingInBoundDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inBound/pending/exportPendingInBoundDoc`,
    {
      ...params,
    },
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

// -------------------------------------------------生成批次号-------------------------------------------------

/**
 * 根据物料id或物料编号生成批次号
 */
export function generateBatchNumber(materialId: string) {
  return requestClient.get<string>(
    `${baseDataPath}/base/material/generate/batchNumber?materialId=${materialId}`,
  );
}
