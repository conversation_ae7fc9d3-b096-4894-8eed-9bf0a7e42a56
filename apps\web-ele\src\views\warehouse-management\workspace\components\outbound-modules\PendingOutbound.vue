<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';

import { CountTo } from '@vben/common-ui';

import {
  getOutApplyItemNum,
  getOutBoundDocNum,
} from '#/api/warehouse-management';
import { WS } from '#/utils/socket/common-socketio';

export default defineComponent({
  components: { CountTo },
  emits: ['cardClick'],
  setup(_props, { emit }) {
    const wsType = [
      'wm.outbound.docstatus.pending.add',
      'wm.outbound.docstatus.finished',
      'wm.outbound.docstatus.cancelAudit',
      'wm.outbound.docstatus.pending.cancelReject',
      'wm.outbound.docstatus.close',
      'wm.outbound.export.pending',
      'wm.outbound.export.finished',
    ];

    const DOC_STATUS = 'pending';

    const NumLoading = ref(true);
    const CountLoading = ref(true);
    const pendingOutboundNum = ref(0); // 待确认出库 单
    const pendingOutboundCount = ref(0); // 待确认出库 项

    const fetchPendingStorageCount = async () => {
      try {
        const response = await getOutApplyItemNum({
          docStatusList: DOC_STATUS,
        });
        pendingOutboundCount.value = response.applyItemNum || 0;
      } catch (error) {
        console.error('获取待确认出库项数失败:', error);
      } finally {
        NumLoading.value = false;
      }
    };

    const fetchOutBoundDocNum = async () => {
      try {
        const response = await getOutBoundDocNum({ docStatusList: DOC_STATUS });
        pendingOutboundNum.value = response.outBoundDocNum || 0;
      } catch (error) {
        console.error('获取待确认出库单据数据失败:', error);
      } finally {
        CountLoading.value = false;
      }
    };

    const handleCardClick = () => {
      emit('cardClick', {
        name: 'pending-outbound',
        attrs: {
          wrapperClass:
            'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4',
          collapsed: true,
          collapsedRows: 2,
          showCollapseButton: true,
        },
      });
    };

    const fetchData = async () => {
      await fetchPendingStorageCount();
      await fetchOutBoundDocNum();
    };

    onMounted(async () => {
      await WS.on(wsType, fetchData);
      fetchData();
    });

    return {
      pendingOutboundCount,
      pendingOutboundNum,
      NumLoading,
      CountLoading,
      handleCardClick,
    };
  },
});
</script>

<template>
  <div
    class="hover:bg-primary-100 bg-primary-50 cursor-pointer rounded-lg p-2 text-center text-black"
    @click="handleCardClick"
  >
    <div class="flex h-8 items-end justify-center">
      <span class="mb-1">待确认出库</span>
      <div class="mr-4 flex h-8 items-end justify-center">
        <span class="text-primary mx-1 text-3xl font-bold">
          <CountTo
            v-loading="NumLoading"
            :start-val="0"
            :end-val="pendingOutboundNum"
            :duration="1500"
            separator=""
          />
        </span>
        <span class="mb-0.5">单</span>
      </div>
      <div class="flex h-8 items-end justify-center">
        <span class="text-primary mx-1 text-3xl font-bold">
          <CountTo
            v-loading="CountLoading"
            :start-val="0"
            :end-val="pendingOutboundCount"
            :duration="1500"
            separator=""
          />
        </span>
        <span class="mb-1">项</span>
      </div>
    </div>
  </div>
</template>
