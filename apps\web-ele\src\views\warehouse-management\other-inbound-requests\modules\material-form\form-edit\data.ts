import type { OnActionClickParams, VxeGridProps } from '@girant/adapter';

import { h } from 'vue';

import MaterialSelect from '#/components/material-select/Index.vue';

/** 物料信息 */
export const useGridOptions = (
  onActionClick: (e: OnActionClickParams) => void,
): VxeGridProps => {
  return {
    columns: [
      { title: '序号', type: 'seq', width: 50 },
      {
        editRender: {},
        field: 'materialId',
        slots: {
          default: ({ row }) => {
            return row.materialName;
          },
          edit: ({ $table, row }) => {
            return h(MaterialSelect, {
              modelValue: row.materialId,
              onChange: async (materialId, selectedItems) => {
                if (!materialId) {
                  $table.setRow(row, {
                    materialId: undefined,
                    materialSpecs: undefined,
                    baseUnitLabel: undefined,
                  });
                  return;
                }
                const materialDetailRes = selectedItems;
                const rowData = {
                  ...materialDetailRes,
                  materialId: {
                    materialId,
                    materialName: materialDetailRes?.materialName,
                  },
                };
                $table.setRow(row, rowData);
              },
              valueKey: 'materialId',
            });
          },
        },
        title: '物料选择',
        width: 180,
      },
      {
        field: 'materialCode',
        title: '物料编号',
        width: 140,
      },
      {
        field: 'materialSpecs',
        title: '规格型号',
        minWidth: 180,
        showOverflow: false,
      },
      {
        field: 'baseUnitLabel',
        title: '基本单位',
        width: 110,
      },
      {
        slots: {
          default: 'applyQuantity',
        },
        field: 'applyQuantity',
        title: '申请入库数量',
        width: 180,
      },
      {
        align: 'center',
        cellRender: {
          attrs: {
            onClick: onActionClick,
          },
          name: 'CellOperation',
          options: ['delete'],
        },
        title: '操作',
        width: 60,
        fixed: 'right',
      },
    ],
    editRules: {
      materialId: [
        { message: '物料不能为空', required: true, trigger: 'blur' },
      ],
      applyQuantity: [
        { message: '数量不能为空', required: true, trigger: 'blur' },
        {
          validator: ({ cellValue }) => {
            if (cellValue === 0) {
              return new Error('数量不能为0');
            }
          },
          trigger: 'blur',
        },
      ],
    },
    minHeight: 100,
    maxHeight: 600,
    cellConfig: {
      height: 60,
    },
    showOverflow: false,
    virtualYConfig: {
      enabled: true,
      gt: 0,
    },
  };
};
