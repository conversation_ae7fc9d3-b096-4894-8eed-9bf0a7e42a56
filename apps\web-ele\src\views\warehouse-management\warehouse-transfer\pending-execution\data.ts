import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import type { WarehouseInfoApi } from '#/api/warehouse-management';

import { h, markRaw } from 'vue';

import { ElInputTag, ElTag } from 'element-plus';

import { getWarehouseList } from '#/api/warehouse-management';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

// 查询参数类型
export interface SearchParams {
  transferDocNumberList: string;
  warehouseIdList: string;
  submitUserList: string;
  executorUserList: string;
  submitTime: string;
}

// 表格数据类型
export interface RowType {
  transferDocId: string;
  transferDocNumber: string;
  warehouseName: string;
  warehouseId: string;
  submitUserName: string;
  submitTime: string;
  docStatusLabel: string;
  docStatus: string;
}

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'transferDocNumberList',
      label: '单据编号',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getWarehouseList();
        },
      },
      fieldName: 'warehouseIdList',
      formItemClass: 'col-span-1',
      label: '所属仓库',
    },

    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'submitUserList',
      modelPropName: 'value',
      label: '申请人',
      formItemClass: 'col-span-1',
    },

    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'executorUserList',
      modelPropName: 'value',
      label: '执行人',
      formItemClass: 'col-span-1',
    },

    {
      component: 'Input',
      fieldName: 'submitTime',
      formItemClass: 'col-span-2',
      label: '提交时间',
    },
  ];
}

/** 表格 */
export function useColumns<T = RowType>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 40,
    },
    {
      field: 'transferDocNumber',
      title: '单据编号',
      minWidth: 200,
    },
    {
      field: 'transferDocId',
      title: '入库单ID',
      visible: false,
    },

    {
      field: 'warehouseName',
      width: 180,
      title: '所属仓库',
    },
    {
      field: 'submitUserName',
      title: '申请人',
      width: 180,
    },
    {
      field: 'submitTime',
      width: 180,
      title: '提交时间',
    },

    {
      slots: {
        default: ({ row }) => {
          return h(
            ElTag,
            {
              type: 'primary',
            },
            { default: () => row.docStatusLabel },
          );
        },
      },
      title: '单据状态',
      width: 150,
    },

    {
      field: 'docStatus',
      title: '单据状态值',
      visible: false,
    },

    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'transferDocNumber',
          nameTitle: '操作',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'view',
            label: '查看',
            type: 'info',
          },

          {
            code: 'confirm',
            label: '确认调拨',
            type: 'primary',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 200,
    },
  ];
}
