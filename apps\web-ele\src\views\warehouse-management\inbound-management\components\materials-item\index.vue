<script lang="ts" setup>
import type { PropType } from 'vue';

import type { dictItemListType } from '#/api';
import type {
  InBoundDocApi,
  WarehouseListForMaterialListApi,
} from '#/api/warehouse-management';

import { computed, defineAsyncComponent, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { isEmpty } from '@vben/utils';

import MaterialInfo from '#/views/warehouse-management/basic-data/material/modules/Form.vue';

const props = defineProps({
  // 物料信息（包含仓库、库位信息）
  materialItemData: {
    default: () => ({}),
    type: Object as PropType<InBoundDocApi.InBoundItem>,
  },
  // 单据状态
  docStatus: {
    default: '',
    type: String,
  },
  // 单据状态值列表
  wmInDocStatusTypeList: {
    default: () => [],
    type: Array as PropType<dictItemListType[]>,
  },
  // 源单据类型标识
  origDocTypeCode: {
    type: String,
    default: '',
  },
  warehouseListForMaterial: {
    type: Object as PropType<WarehouseListForMaterialListApi.WarehouseListForMaterialList>,
    default: () => ({}),
  },
});

const MaterialsItemRef = ref();

const currentComponent = computed(() => {
  switch (props.docStatus) {
    // 待入库
    case '00': {
      return defineAsyncComponent(
        () => import('./pending-materialsItem/index.vue'),
      );
    }

    // 已入库
    case '10': {
      return defineAsyncComponent(
        () => import('./stocked-materialsItem/index.vue'),
      );
    }

    // 已关闭
    default: {
      return defineAsyncComponent(
        () => import('./closed-materialsItem/index.vue'),
      );
    }
  }
});

const materialId = ref<string>('');
const handleMaterialCode = (id: string) => {
  materialId.value = id; // 物料id
  materialInfoModalApi.open();
};

/** 物料信息查看模式*/
const [MaterialInfoModal, materialInfoModalApi] = useVbenModal({
  showConfirmButton: false,
});

defineExpose({
  MaterialsItemRef,
});
</script>

<template>
  <div>
    <component
      :is="currentComponent"
      v-if="docStatus !== '00' || !isEmpty(warehouseListForMaterial)"
      :material-item-data="materialItemData"
      :orig-doc-type-code="origDocTypeCode"
      :warehouse-list-for-material="warehouseListForMaterial"
      ref="MaterialsItemRef"
      @handle-material-code="handleMaterialCode"
    />

    <MaterialInfoModal class="w-10/12">
      <MaterialInfo :is-view="true" :material-id="materialId" />
    </MaterialInfoModal>
  </div>
</template>
