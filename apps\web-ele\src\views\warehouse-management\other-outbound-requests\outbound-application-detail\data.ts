import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import type { dictItemListType } from '#/api/common';

import { h, markRaw } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElBadge, ElInputTag, ElTag } from 'element-plus';

import { getDictItemList } from '#/api/common';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

import { docStatusDict } from '../config/list';

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'inOutReqDocNumberList',
      label: '单据编号',
      formItemClass: 'col-span-1',
    },
    {
      component: h(DeptStaffTree, {
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'submitUserList',
      label: '申请人',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'submitTime',
      label: '提交时间',
      formItemClass: 'col-span-2',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          // 过滤待提交 value=00
          const filterList = warehouseList.filter(
            (item) => item.value !== '00',
          );
          return filterList;
        },
        api: () => {
          return getDictItemList('wmOutApplyDocStatus');
        },
        multiple: true,
        maxCollapseTags: 2,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'docStatusList',
      label: '单据状态',
      formItemClass: 'col-span-1',
    },
    {
      component: h(DeptStaffTree, {
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'materialUserList',
      label: '使用人',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'finishTime',
      label: '完成时间',
      formItemClass: 'col-span-2',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return warehouseList;
        },
        api: () => {
          return getDictItemList('wmOtherOutReqType');
        },
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'docCodeList',
      label: '出库类型',
      formItemClass: 'col-span-1',
    },
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'materialIdList',
      label: '物料编号',
      formItemClass: 'col-span-1',
    },
    {
      componentProps: {
        clearable: true,
      },
      component: 'Input',
      fieldName: 'materialName',
      label: '物料名称',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return warehouseList;
        },
        api: () => {
          return getDictItemList('baseMaterialAttribute');
        },
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'materialAttributeList',
      label: '物料属性',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return warehouseList;
        },
        api: () => {
          return getDictItemList('baseMaterialType');
        },
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'materialTypeList',
      label: '物料大类',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'materialCategoryList',
      label: '物料细类',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
      fieldName: 'isStandard',
      label: '是否标准物料',
      labelWidth: 100,
    },
  ];
}

/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      slots: {
        default: ({ row }) => {
          return h(
            'div',
            {
              class: 'relative',
            },
            [
              h('span', row.inOutReqDocNumber),
              row.isRectify
                ? h(
                    ElTag,
                    {
                      type: 'primary',
                      class: 'ml-2 absolute top-1/2 right-0 -translate-y-1/2',
                    },
                    { default: () => '补录' },
                  )
                : null,
            ],
          );
        },
      },
      title: '单据编号',
      field: 'inOutReqDocNumber',
      minWidth: 235,
    },
    {
      slots: {
        default: ({ row }) =>
          h(
            ElTag,
            {
              size: 'small',
              type: docStatusDict[row.docStatus],
            },
            {
              default: () => row.docStatusLabel,
            },
          ),
      },
      title: '单据状态',
      field: 'docStatusLabel',
      minWidth: 110,
    },
    {
      slots: {
        default: ({ row }) => {
          return row.docCodeLabel || '/';
        },
      },
      title: '出库类型',
      field: 'docCodeLabel',
      minWidth: 100,
    },
    {
      slots: {
        default: ({ row }) =>
          h(ImageViewer, {
            imgId: row.pictureFileId,
            imgCss: 'h-[50px]',
          }),
      },
      title: '物料图片',
      field: 'pictureFileId',
      minWidth: 90,
    },
    {
      title: '物料编号',
      slots: {
        default: ({ row }) => {
          return h(
            ElBadge,
            {
              value: '非标',
              type: 'primary',
              offset: [-15, -5],
              badgeStyle: 'border-radius:0px',
              hidden: row.isStandard,
              class: 'item',
            },
            {
              default: () => row.materialCode,
            },
          );
        },
      },
      field: 'materialCode',
      minWidth: 150,
    },
    {
      title: '物料名称',
      field: 'materialName',
      minWidth: 120,
    },
    {
      title: '具体规格',
      field: 'materialSpecs',
      minWidth: 200,
    },
    {
      title: '申请出库数量',
      field: 'applyQuantitySum',
      minWidth: 100,
    },
    {
      title: '实际出库数量',
      field: 'actualQuantitySum',
      minWidth: 100,
    },
    {
      title: '单位',
      field: 'baseUnitLabel',
      minWidth: 60,
    },
    {
      title: '物料属性',
      field: 'materialAttributeLabel',
      minWidth: 65,
    },
    {
      title: '物料大类',
      field: 'materialTypeLabel',
      minWidth: 65,
    },
    {
      title: '物料细类',
      field: 'materialCategoryName',
      minWidth: 100,
    },
    {
      slots: {
        default: 'submitUserName',
      },
      title: '申请人',
      field: 'submitUserName',
      minWidth: 150,
    },
    {
      title: '提交时间',
      field: 'submitTime',
      minWidth: 115,
    },
    {
      slots: {
        default: 'materialUserName',
      },
      title: '使用人',
      field: 'materialUserName',
      minWidth: 150,
    },
    {
      title: '完成时间',
      field: 'finishTime',
      minWidth: 115,
    },

    {
      align: 'center',
      slots: {
        default: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      minWidth: 180,
      title: '操作',
    },
  ];
}
