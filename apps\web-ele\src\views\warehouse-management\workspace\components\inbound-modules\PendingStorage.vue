<script lang="ts">
import { computed, defineComponent, onMounted, ref } from 'vue';

import { CountTo } from '@vben/common-ui';

import {
  getInApplyItemNum,
  getInBoundDocNum,
} from '#/api/warehouse-management';
import { WS } from '#/utils/socket/common-socketio';

export default defineComponent({
  components: { CountTo },
  props: {
    activeId: {
      type: String,
      default: '',
    },
  },
  emits: ['cardClick'],
  setup(props, { emit }) {
    const wsType = [
      'wm.inbound.docstatus.pending.add',
      'wm.inbound.docstatus.finished',
      'wm.inbound.docstatus.cancelAudit',
      'wm.inbound.docstatus.pending.cancelReject',
      'wm.inbound.docstatus.close',
      'wm.inbound.export.pending',
      'wm.inbound.export.finished',
    ];

    const DOC_STATUS = 'pending';

    const isActive = computed(() => props.activeId === 'pending-storage');
    const [loading, setLoading] = [
      ref(true),
      (val: boolean) => (loading.value = val),
    ];
    const data = ref([
      { key: 'doc', label: '单', value: 0 },
      { key: 'item', label: '项', value: 0 },
    ]);

    const fetchData = async () => {
      try {
        const [doc, item] = await Promise.all([
          getInBoundDocNum({ docStatusList: DOC_STATUS }),
          getInApplyItemNum({ docStatusList: DOC_STATUS }),
        ]);

        data.value = data.value.map((val) => ({
          ...val,
          value:
            val.key === 'doc' ? doc.inBoundDocNum || 0 : item.applyItemNum || 0,
        }));
      } catch (error) {
        console.error('数据加载失败', error);
      } finally {
        setLoading(false);
      }
    };

    onMounted(async () => {
      await WS.on(wsType, fetchData);

      fetchData();
    });

    return {
      isActive,
      data,
      loading,
      click: () =>
        emit('cardClick', {
          name: 'pending-storage',
          attrs: {
            wrapperClass: 'grid-cols-2...',
            collapsed: true,
            showCollapseButton: true,
          },
        }),
    };
  },
});
</script>

<template>
  <div
    class="bg-primary-50 hover:bg-primary-100 cursor-pointer rounded-lg p-2 text-center transition-all duration-300"
    @click="click"
  >
    <div class="flex h-8 items-end justify-center text-sm">
      <span class="mb-0.5">待确认入库</span>
      <div
        v-for="item in data"
        :key="item.key"
        class="flex h-8 items-end justify-center"
      >
        <span class="text-primary mx-1 text-3xl font-bold">
          <CountTo
            v-loading="loading"
            :start-val="0"
            :end-val="item.value"
            :duration="1500"
          />
        </span>
        <span class="mb-0.5">{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>
