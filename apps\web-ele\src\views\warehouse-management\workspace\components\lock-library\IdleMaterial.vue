<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';

import { CountTo } from '@vben/common-ui';

import { getIdleMaterialNum } from '#/api/warehouse-management';
import { WS } from '#/utils/socket/common-socketio';

export default defineComponent({
  components: { CountTo },
  emits: ['cardClick'],
  setup(_props, { emit }) {
    const wsType = [
      'wm.inventory.obsolete.new',
      'wm.inventory.obsolete.release',
    ];

    const loading = ref(true);
    const IdleTypeCount = ref(0); // 已锁物料（项）
    const IdleCount = ref(0); // 数量合计

    const fetchIdleMaterialData = async () => {
      try {
        const response = await getIdleMaterialNum();
        IdleTypeCount.value = response.obsoleteCatTotal || 0;
        IdleCount.value = response.obsoleteQtyTotal || 0;
      } catch (error) {
        console.error('获取呆料数据失败:', error);
      } finally {
        loading.value = false;
      }
    };

    onMounted(() => {
      WS.on(wsType, fetchIdleMaterialData);
      fetchIdleMaterialData();
    });

    const handleCardClick = () => {
      emit('cardClick', {
        name: 'idle-material',
        attrs: {
          wrapperClass:
            'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2',
        },
      });
    };

    return {
      IdleTypeCount,
      IdleCount,
      loading,
      handleCardClick,
    };
  },
});
</script>
<template>
  <div
    class="hover:bg-primary-100 bg-primary-50 cursor-pointer rounded-lg px-3 py-2 text-black"
    @click="handleCardClick"
  >
    <div class="pb-1 text-sm font-bold">呆料情况</div>
    <div>
      <div class="flex h-6 items-end">
        <span class="ml-1 text-sm">呆滞</span>
        <span class="mx-1 -mb-0.5 text-xl font-bold text-gray-700">
          <CountTo
            v-loading="loading"
            :start-val="0"
            :end-val="IdleTypeCount"
            :duration="1500"
            separator=""
          />
        </span>
        <span class="ml-1 text-sm">项物料</span>
      </div>
      <div class="flex h-6 items-end">
        <span class="ml-1 text-sm">数量合计</span>
        <span class="mx-1 mb-0.5 font-bold text-gray-700">
          <CountTo
            v-loading="loading"
            :start-val="0"
            :end-val="IdleCount"
            :duration="1500"
            separator=""
          />
        </span>
      </div>
    </div>
  </div>
</template>
