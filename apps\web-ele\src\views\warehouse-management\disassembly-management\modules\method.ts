import type { StaffInfoType } from '#/api/common/staff';

import { ElMessage, ElMessageBox } from 'element-plus';

import { closeAssemblyDoc, delAssemblyDoc, execAssemblyDoc } from '#/api';

/** 拆卸单据状态Tag类型*/
export const docStatusDict: { [key: string]: any } = {
  /** 待提交 */
  '00': 'warning',
  /** 待审核 */
  '10': 'primary',
  /** 待领料 */
  '20': 'primary',
  /** "取消审核中" */
  '25': 'warning',
  /** 待入库 */
  '30': 'primary',
  /** 已完成 */
  '40': 'success',
  /** 审核驳回 */
  '80': 'danger',
  /** 已关闭 */
  '90': 'info',
};

/** 拆卸单据状态图片 dictKey*/
export const docStatusIconDict: { [key: string]: any } = {
  /** 已完成 */
  '40': {
    name: 'yi<PERSON><PERSON>',
    color: 'text-lime-500',
  },
  /** 审核驳回 */
  '80': {
    name: 'shenheb<PERSON>nggu<PERSON>',
    color: 'text-red-500',
  },
  /** 已关闭 */
  '90': {
    name: 'yiguanbi',
    color: 'text-slate-500 !text-gray-300',
  },
};

/** 出入库单据状态Tag类型*/
export const boundDocStatusDict: { [key: string]: any } = {
  /** 待提交 */
  '00': 'warning',
  /** 取消审核中 */
  '05': 'warning',
  /** 已出库 */
  '10': 'success',
  /** 已关闭 */
  '99': 'info',
};

/** 出入库取消单据状态Tag类型*/
export const inOutCancelDocStatusDict: { [key: string]: any } = {
  /** 待提交 */
  '00': 'warning',
  /** 待审核 */
  '10': 'primary',
  /** 审核通过 */
  '20': 'success',
  /** 审核驳回 */
  '80': 'danger',
};

/** 提示会话框 */
export const dialog = (content: string, title: string) => {
  return new Promise((resolve) => {
    ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
      .then(() => {
        resolve(true);
      })
      .catch(() => {});
  });
};

/** 打开模态框 */
export const openModal = (
  /** 模态框API */
  formModalApi: any,
  /** 是否显示确认按钮 */
  showConfirmButton: boolean,
  /** 标题 */
  title: string,
) => {
  formModalApi
    .setState({
      showConfirmButton,
      title,
    })
    .open();
};

/** 判断是否显示完成拆卸按钮 */
export const shouldShowAutoIo = (row: any, staffData?: StaffInfoType) => {
  // 无当前员工数据，默认不显示
  if (!staffData) return false;
  // 无拆卸单据数据，默认不显示
  if (!row) return false;
  // 自动出入库未开启，不显示
  if (row.isAutoIo === false) return false;
  // 单据不为待领料，不显示
  if (row.docStatus !== '20') return false;
  // 执行人不为当前用户，不显示
  if (row.executorUser !== staffData.staffId) return false;

  return true;
};

/** 判断是否显示取消按钮 */
export const shouldShowClose = (row: any) => {
  // 无拆卸单据数据，默认不显示
  if (!row) return false;
  // 自动出入库未开启，不显示
  if (row.isAutoIo === false) return false;
  // 单据不为待领料，不显示
  if (row.docStatus !== '20') return false;

  return true;
};

/**
 * 日期禁用函数
 * @param isEnd 是否是结束时间
 */
export const createDisabledDate = (isEnd: boolean, thisTime: any) => {
  return (time: Date) => {
    if (!thisTime.endTime && !isEnd) {
      return false;
    }
    // 是结束时间
    return isEnd
      ? time.getTime() < new Date(thisTime.startTime).getTime()
      : time.getTime() > new Date(thisTime.endTime).getTime();
  };
};

/** 删除单据 */
export const deleteDoc = async (
  assemblyDocId: string,
  formModalApi: any,
  gridApi: any,
) => {
  try {
    if (await dialog('确定删除吗？', '提示')) {
      await delAssemblyDoc(assemblyDocId);
      ElMessage.success('删除成功');
      formModalApi.close();
      gridApi.query();
    }
  } catch {
    ElMessage.error('删除失败');
  }
};

/** 自动完成出入库 */
export const autoExecute = async (
  assemblyDocId: string,
  formModalApi: any,
  gridApi: any,
) => {
  try {
    if (await dialog('确定执行吗？', '提示')) {
      await execAssemblyDoc(assemblyDocId);
      ElMessage.success('执行成功');
      formModalApi.close();
      gridApi.query();
    }
  } catch {
    ElMessage.error('执行失败');
  }
};

/** 关闭拆卸单据 */
export const closeDoc = async (
  assemblyDocId: string,
  formModalApi: any,
  gridApi: any,
) => {
  try {
    if (await dialog('确定取消吗？', '提示')) {
      await closeAssemblyDoc(assemblyDocId);
      ElMessage.success('取消成功');
      formModalApi.close();
      gridApi.query();
    }
  } catch {
    ElMessage.error('取消失败');
  }
};
