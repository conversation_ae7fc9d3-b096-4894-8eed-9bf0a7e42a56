import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';

/** 调整单信息 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'invcAdjustDocNumber',
      label: '单据编号',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'createUserName',
      label: '提交人',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'docStatusLabel',
      label: '单据状态',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      dependencies: {
        show(values) {
          return !!values.executorUserName;
        },
        triggerFields: ['executorUserName'],
      },
      fieldName: 'executorUserName',
      label: '执行人',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'remark',
      label: '调整原因',
      formItemClass: 'col-span-full items-start',
      wrapperClass: 'block',
    },
    {
      component: (props: any) => {
        if (!props.modelValue) {
          return h('div', null, '暂无附件');
        }
        return h(UploadFiles, {
          mode: 'readMode',
          serialNumber: props.modelValue,
          tableProps: {
            maxHeight: '300',
          },
        });
      },
      fieldName: 'serialNumber',
      label: '附件',
      formItemClass: 'col-span-full',
    },
    {
      component: 'Input',
      fieldName: 'documentProcess',
      formItemClass: 'col-span-full',
      label: '单据流程',
    },
  ];
}
