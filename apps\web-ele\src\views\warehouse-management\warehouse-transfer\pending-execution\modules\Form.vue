<script setup lang="ts">
import { ref } from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';

import { execTransferDoc } from '#/api/warehouse-management';

import FormToWarehouseTansfer from '../../components/FormToWarehouseTansfer.vue';

const props = defineProps({
  transferDocId: {
    type: String,
    default: '',
  },
  transferDocNumber: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['handleCancel', 'execSuccess', 'transferLoading']);

const handleCancel = () => {
  emits('handleCancel');
};

const FormToWarehouseTansferRef =
  ref<InstanceType<typeof FormToWarehouseTansfer>>();

const execHandle = async () => {
  await ElMessageBox.confirm('确定确认调拨吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  })
    .then(async () => {
      try {
        emits('transferLoading', true);
        await execTransferDoc(props.transferDocId);
        emits('execSuccess');
      } catch {
        ElMessage.error('执行失败');
      } finally {
        emits('transferLoading', false);
      }
    })
    .catch(() => {});
};
</script>

<template>
  <div class="h-full">
    <FormToWarehouseTansfer
      ref="FormToWarehouseTansferRef"
      :transfer-doc-id="transferDocId"
      :transfer-doc-number="transferDocNumber"
      :is-view="true"
    >
      <template #btn-group>
        <ElButton type="primary" @click="execHandle">确认调拨</ElButton>
        <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
      </template>
    </FormToWarehouseTansfer>
  </div>
</template>
