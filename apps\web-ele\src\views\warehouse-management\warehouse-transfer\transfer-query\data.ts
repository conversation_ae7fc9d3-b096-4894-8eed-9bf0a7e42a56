import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import type { WarehouseInfoApi } from '#/api/warehouse-management';

import { h, markRaw } from 'vue';

import { ElInputTag, ElTag } from 'element-plus';

import { getDictItemList } from '#/api';
import { getWarehouseList } from '#/api/warehouse-management';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

// 查询参数类型
export interface SearchParams {
  transferDocNumberList: string;
  warehouseIdList: string;
  submitUserList: string;
  executorUserList: string;
  submitTime: string;
  finishTime: string;
  docStatusList: string;
}

// 表格数据类型
export interface RowType {
  transferDocId: string;
  transferDocNumber: string;
  warehouseName: string;
  submitUserName: string;
  executorUserName: string;
  finishTime: string;
  submitTime: string;
  docStatusLabel: string;
  docStatus: string;
}

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'transferDocNumberList',
      label: '单据编号',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getWarehouseList();
        },
      },
      fieldName: 'warehouseIdList',
      formItemClass: 'col-span-1',
      label: '所属仓库',
    },

    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'submitUserList',
      modelPropName: 'value',
      label: '申请人',
      formItemClass: 'col-span-1',
    },

    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'executorUserList',
      modelPropName: 'value',
      label: '执行人',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const workStatusTypeList = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return workStatusTypeList;
        },
        api: () => {
          return getDictItemList('publicDocStatus');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'docStatusList',
      label: '单据状态',
    },
    {
      component: 'Input',
      fieldName: 'submitTime',
      formItemClass: 'col-span-2',
      label: '提交时间',
    },

    {
      component: 'Input',
      fieldName: 'finishTime',
      formItemClass: 'col-span-2',
      label: '完成时间',
    },
  ];
}

/** 判断tag类型 */
const getTagType = (type: string) => {
  switch (type) {
    // 待提交
    case '00': {
      return 'primary';
    }
    // 待审核
    case '10': {
      return 'warning';
    }

    // 审核驳回
    case '20': {
      return 'danger';
    }
    // 待执行
    case '30': {
      return 'primary';
    }
    // 已完成
    case '40': {
      return 'success';
    }
    // 已关闭
    case '90': {
      return 'info';
    }

    default: {
      return 'info';
    }
  }
};

/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 40,
    },
    {
      field: 'transferDocNumber',
      title: '单据编号',
      minWidth: 200,
    },
    {
      field: 'transferDocId',
      title: '入库单ID',
      visible: false,
    },
    {
      slots: {
        default: ({ row }) => {
          return h(
            ElTag,
            {
              type: getTagType(row.docStatus),
            },
            { default: () => row.docStatusLabel },
          );
        },
      },
      field: 'docStatusLabel',
      width: 120,
      title: '单据状态',
    },
    {
      field: 'warehouseName',
      width: 200,
      title: '所属仓库',
    },

    {
      field: 'submitUserName',
      width: 180,
      title: '提交人',
    },

    {
      field: 'submitTime',
      width: 150,
      title: '提交时间',
    },

    {
      field: 'executorUserName',
      width: 150,
      title: '执行人',
    },

    {
      field: 'finishTime',
      width: 150,
      title: '完成时间',
    },

    {
      slots: {
        default: 'operation',
      },
      align: 'center',
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 'auto',
      minWidth: 150,
    },
  ];
}
