<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { StaffInfoType } from '#/api/common/staff';

import { computed, onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse, isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElCard,
  ElDatePicker,
  ElMessage,
  ElTag,
  ElTooltip,
} from 'element-plus';

import { exportAssemblyDoc, getAssemblyDocPage } from '#/api';
import { getStaffInfo } from '#/api/common/staff';

import Form from '../modules/Form.vue';
import {
  autoExecute,
  closeDoc,
  createDisabledDate,
  docStatusDict,
  openModal,
  shouldShowAutoIo,
  shouldShowClose,
} from '../modules/method';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});

const formRef = ref<InstanceType<typeof Form>>();
const exportLoading = ref(false);
/** 拆卸申请单编号 */
const assemblyDocNumber = ref('');
/** 拆卸申请单id */
const assemblyDocId = ref('');
/** 审核流程实例ID */
const processInstanceId = ref('');
const isView = ref(false);
/** 当前单据状态 */
const docStatus = ref('');
/** 提交时间 */
const submitTime = ref({
  startTime: props.params?.submitStartTime,
  endTime: props.params?.submitEndTime,
});
/** 拆卸row数据 */
const rowData = ref();
/** 当前登录用户的员工信息 */
const staffData = ref<StaffInfoType>();
/** 判断是否显示完成拆卸按钮 */
const isAutoIo = computed(() => {
  return (row: any) => {
    return shouldShowAutoIo(row, staffData.value);
  };
});

onMounted(async () => {
  /** 初始化搜索条件 */
  await gridApi.formApi.setValues({
    assemblyDocNumberList:
      props.params?.assemblyDocNumberList?.split(',') || [],
    warehouseIdList: props.params?.warehouseIdList?.split(',') || [],
    submitUserList: props.params?.submitUserList?.split(',') || [],
    executorUserList: props.params?.executorUserList?.split(',') || [],
    productMaterialCodeList:
      props.params?.productMaterialCodeList?.split(',') || [],
    productMaterialName: props.params?.productMaterialName || '',
  });
  /** 获取当前员工信息 */
  staffData.value = await getStaffInfo();
});

/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onConfirm: () => {
    formRef.value?.onSubmit();
  },
  confirmText: '提交',
  showCancelButton: true,
  showConfirmButton: true,
});

/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    submitTime.value = {
      startTime: '',
      endTime: '',
    };
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};

/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    handleReset,
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || true,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getAssemblyDocPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            // 待执行条件筛选
            docStatusList: isEmpty(formValues.docStatusList)
              ? ['20', '30']
              : formValues.docStatusList,
            submitStartTime: submitTime.value.startTime,
            submitEndTime: submitTime.value.endTime,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions,
});

/** 新增 */
const onAdd = () => {
  isView.value = false;
  rowData.value = undefined;
  assemblyDocId.value = '';
  assemblyDocNumber.value = '';
  openModal(formModalApi, true, '新增');
};

/** 提交成功 */
const submitSuccess = () => {
  // 重新获取数据
  gridApi.query();
  formModalApi.close();
};

/** 导出数据 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportAssemblyDoc({
      docStatusList: ['20', '30'],
      ...formValues,
      submitStartTime: submitTime.value.startTime,
      submitEndTime: submitTime.value.endTime,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};

/** 查看 */
const onView = (row: any) => {
  isView.value = true;
  rowData.value = row;
  docStatus.value = row.docStatus;
  assemblyDocId.value = row.assemblyDocId;
  assemblyDocNumber.value = row.assemblyDocNumber;
  processInstanceId.value = row.processInstanceId;
  openModal(formModalApi, false, '拆卸单详情');
};

/** 完成拆卸 */
const execute = (assemblyDocId: string) => {
  autoExecute(assemblyDocId, formModalApi, gridApi);
};

/** 关闭单据 */
const close = (assemblyDocId: string) => {
  closeDoc(assemblyDocId, formModalApi, gridApi);
};
</script>

<template>
  <Page auto-content-height>
    <ElCard class="h-full" body-class="h-full !p-[10px]">
      <!-- 模态框 -->
      <FormModal class="h-full w-10/12">
        <Form
          ref="formRef"
          :is-view="isView"
          @submit-success="submitSuccess"
          :assembly-doc-id="assemblyDocId"
          :assembly-doc-number="assemblyDocNumber"
          :process-instance-id="processInstanceId"
        />
        <template #center-footer>
          <ElButton
            v-if="!assemblyDocNumber"
            type="primary"
            @click="formRef?.onSave()"
          >
            暂存
          </ElButton>
          <ElButton
            v-if="isAutoIo(rowData)"
            @click="execute(assemblyDocId)"
            type="primary"
          >
            完成拆卸
          </ElButton>
          <ElButton
            v-if="shouldShowClose(rowData)"
            @click="close(assemblyDocId)"
            type="danger"
          >
            取消单据
          </ElButton>
        </template>
      </FormModal>
      <Grid>
        <template #toolbar-actions>
          <ElButton type="primary" @click="onAdd"> 新增 </ElButton>
        </template>
        <template #form-submitTime>
          <ElDatePicker
            v-model="submitTime.startTime"
            type="datetime"
            placeholder="开始日期"
            :disabled-date="createDisabledDate(false, submitTime)"
            value-format="YYYY-MM-DD HH:mm"
            format="YYYY-MM-DD HH:mm"
            time-format="HH:mm"
            class="!w-full"
          />
          <span class="px-[10px] text-[16px]">-</span>
          <ElDatePicker
            v-model="submitTime.endTime"
            type="datetime"
            placeholder="结束日期"
            :disabled-date="createDisabledDate(true, submitTime)"
            value-format="YYYY-MM-DD HH:mm"
            time-format="HH:mm"
            format="YYYY-MM-DD HH:mm"
            :default-time="new Date(2000, 1, 1, 23, 59)"
            class="!w-full"
          />
        </template>
        <template #productMaterialName="{ row }">
          <span>{{ row.productMaterialName }}</span>
          <span v-if="row.productMaterialCode">
            ({{ row.productMaterialCode }})
          </span>
        </template>
        <template #quantity="{ row }">
          <span>{{ row.quantity }}</span>
          <span>{{ row.baseUnitLabel }}</span>
        </template>
        <template #docStatusLabel="{ row }">
          <ElTag size="small" :type="docStatusDict[row.docStatus]">
            {{ row.docStatusLabel }}
          </ElTag>
        </template>
        <template #CellOperation="{ row }">
          <div>
            <ElButton link size="small" @click="onView(row)" type="info">
              查看
            </ElButton>
            <ElButton
              v-if="isAutoIo(row)"
              link
              size="small"
              @click="execute(row.assemblyDocId)"
              type="primary"
            >
              完成拆卸
            </ElButton>
            <ElButton
              v-if="shouldShowClose(row)"
              link
              size="small"
              @click="close(row.assemblyDocId)"
              type="danger"
            >
              取消单据
            </ElButton>
          </div>
        </template>
        <template #toolbar-tools>
          <ElTooltip
            class="box-item"
            effect="light"
            content="导出数据"
            placement="top-start"
          >
            <ElButton :loading="exportLoading" circle @click="exportHandle">
              <template #icon><IconFont name="xiazai" /></template>
            </ElButton>
          </ElTooltip>
        </template>
      </Grid>
    </ElCard>
  </Page>
</template>
