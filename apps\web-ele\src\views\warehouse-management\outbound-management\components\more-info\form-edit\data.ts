import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
import { z } from '@girant/adapter';

/** 表单 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Textarea',
      componentProps: {
        autosize: { minRows: 3 },
        maxlength: 500,
        placeholder: '请输入',
        showWordLimit: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.remark) {
            return z.string().max(500, { message: '备注长度不超过500' });
          }
          return null;
        },
        triggerFields: ['remark'],
      },
      fieldName: 'remark',
      formItemClass: 'mr-6',
      label: '备注',
    },
    {
      component: h(UploadFiles, {
        mode: 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      formItemClass: 'mr-6',
      label: '附件',
    },
  ];
}
