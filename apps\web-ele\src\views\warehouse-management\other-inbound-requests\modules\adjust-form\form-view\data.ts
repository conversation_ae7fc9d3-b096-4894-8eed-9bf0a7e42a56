import type { VbenFormSchema } from '@girant/adapter';

import type { InBound } from '#/api/warehouse-management';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';

/** 出库单信息 */
export function useFormSchema(
  formData: InBound.InOutBoundReqDocDetail,
): VbenFormSchema[] {
  const data = [
    {
      component: 'Input',
      fieldName: 'inOutReqDocNumber',
      label: '单据编号',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'docCodeLabel',
      label: '入库类型',
    },
    {
      component: (props: any) => {
        return h(
          'div',
          null,
          props.modelValue
            ? `${props.modelValue}(${formData?.submitUserDeptName || '未知部门'})`
            : '/',
        );
      },
      fieldName: 'submitUserName',
      label: '申请人',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'submitTime',
      label: '提交时间',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'remark',
      label: '备注说明',
      formItemClass: 'col-span-full',
    },
    {
      component: (props: any) => {
        if (!props.modelValue) {
          return h('div', null, '暂无附件');
        }
        return h(UploadFiles, {
          mode: 'readMode',
          serialNumber: props.modelValue,
          tableProps: {
            maxHeight: '300',
          },
        });
      },
      fieldName: 'serialNumber',
      label: '附件',
      formItemClass: 'col-span-full',
    },
    {
      component: 'Input',
      fieldName: 'documentProcess',
      formItemClass: 'col-span-full',
      label: '单据流程',
    },
  ];
  if (formData.docStatus === '30') {
    // 找到"入库类型"字段的索引
    const docTypeLabelIndex = data.findIndex(
      (item) => item.fieldName === 'docCodeLabel',
    );

    if (docTypeLabelIndex !== -1) {
      // 在"入库类型"之后插入两个新字段
      data.splice(docTypeLabelIndex + 1, 0, {
        component: (props: any) => {
          return h('div', null, props.modelValue || '/');
        },
        fieldName: 'finishTime',
        label: '入库时间',
      });
    }
  }
  return data;
}
