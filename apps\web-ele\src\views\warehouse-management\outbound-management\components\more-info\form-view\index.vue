<script setup lang="ts">
import { useVbenForm } from '@girant/adapter';

import { useFormSchema } from './data';

/** 表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' }, labelWidth: 60 },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});

const loadMoreInfoData = (data: any) => {
  formApi.setValues(data);
};

defineExpose({
  formApi,
  loadMoreInfoData,
});
</script>

<template>
  <Form />
</template>
