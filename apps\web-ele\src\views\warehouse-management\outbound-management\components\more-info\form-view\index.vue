<script setup lang="ts">
import { ref } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
import { useVbenForm } from '@girant/adapter';

import { useFormSchema } from './data';

/** 附件*/
const uploadSerialNumberRef = ref();

/** 表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' }, labelWidth: 60 },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});

const loadMoreInfoData = (data: any) => {
  formApi.setValues(data);
};

defineExpose({
  formApi,
  loadMoreInfoData,
});
</script>

<template>
  <Form>
    <template #serialNumber="row">
      <div class="w-full">
        <UploadFiles
          mode="readMode"
          ref="uploadSerialNumberRef"
          :show-operat-button="false"
          :show-table="uploadSerialNumberRef?.fileList?.length > 0"
          :serial-number="row.value"
        />
      </div>
    </template>
  </Form>
</template>
