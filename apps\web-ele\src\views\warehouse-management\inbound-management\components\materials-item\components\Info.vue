<script setup lang="ts">
import type { InBoundDocApi } from '#/api/warehouse-management';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElButton, ElTag } from 'element-plus';

const props = defineProps<{
  materialItemData: InBoundDocApi.InBoundItem;
}>();

const emits = defineEmits(['handleMaterialCode']);

const handleMaterialCode = () => {
  emits('handleMaterialCode', props.materialItemData.materialCode);
};
</script>

<template>
  <div class="flex pt-2">
    <div class="ml-10 w-[50px]" v-if="materialItemData.pictureFileId">
      <ImageViewer
        :img-id="materialItemData.pictureFileId"
        img-css="h-[50px] rounded-md"
        img-fit="cover"
      />
    </div>
    <div class="ml-4 flex flex-1 justify-between">
      <div class="name space-x-2">
        <span class="text-primary-500 text-lg font-medium">
          {{ materialItemData.materialName }}
          <ElTag v-if="!materialItemData.isStandard" type="danger" size="small">
            非标
          </ElTag>
        </span>

        <ElButton
          type="info"
          link
          class="underline"
          @click="handleMaterialCode"
        >
          {{ materialItemData.materialCode }}
        </ElButton>

        <span class="text-sm text-gray-500">
          规格型号： {{ materialItemData.materialCategory }}
        </span>
      </div>
      <div class="text-sm text-gray-600">
        <slot name="extra" :material-item-data="materialItemData"></slot>
        <template v-if="!$slots.extra">
          <span>入库合计：</span>
          <span class="font-medium">{{
            materialItemData.applyQuantitySum
          }}</span>
          <span class="text-gray-500">
            ({{ materialItemData.baseUnitLabel }})
          </span>
        </template>
      </div>
    </div>
  </div>
</template>
