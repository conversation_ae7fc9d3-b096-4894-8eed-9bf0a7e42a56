<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import CloseBound from '../../components/close-bound/form-view/index.vue';
import InboundInfo from './inbound-info/index.vue';
import MaterialsInfo from './materials-info/index.vue';

defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
});

const emits = defineEmits(['handleCancel']);

const inOutCancelDocId = ref<string>('');
const inOutCancelDocNumber = ref<string>('');

/** 关闭出入库弹窗 */
const [CloseModal, closeModalApi] = useVbenModal({
  confirmText: '查看取消出库',
  destroyOnClose: true,
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

const handleViewInOutCancelDoc = (data: {
  inOutCancelDocId: string;
  inOutCancelDocNumber: string;
}) => {
  closeModalApi.open();
  inOutCancelDocId.value = data.inOutCancelDocId;
  inOutCancelDocNumber.value = data.inOutCancelDocNumber;
};

const handleCancel = () => {
  emits('handleCancel');
};
</script>

<template>
  <div class="relative mb-6 h-full">
    <CloseModal class="w-3/5">
      <CloseBound :in-out-cancel-doc-id="inOutCancelDocId" />
    </CloseModal>

    <InboundInfo
      :in-bound-doc-id="inBoundDocId"
      @view-in-out-cancel-doc="handleViewInOutCancelDoc"
    />
    <MaterialsInfo :in-bound-doc-id="inBoundDocId" />

    <div
      class="fixed bottom-0 right-0 z-10 h-[50px] w-full rounded-b-lg border-t bg-white p-2 text-right"
    >
      <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
    </div>
  </div>
</template>
