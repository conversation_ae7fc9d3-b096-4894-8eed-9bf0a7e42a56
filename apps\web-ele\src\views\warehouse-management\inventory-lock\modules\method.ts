import { ElMessageBox } from 'element-plus';

/** 提示框 */
export const confirm = (content: string, title: string) => {
  return new Promise((resolve) => {
    ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
      .then(() => {
        resolve(true);
      })
      .catch(() => {});
  });
};

/** 打开模态框 */
export const openModal = (
  /** 模态框API */
  formModalApi: any,
  /** 是否显示确认按钮 */
  showConfirmButton: boolean,
  /** 标题 */
  title: string,
) => {
  formModalApi
    .setState({
      showConfirmButton,
      title,
    })
    .open();
};
/**
 * 日期禁用函数
 * @param isEnd 是否是结束时间
 */
export const createDisabledDate = (isEnd: boolean, thisTime: any) => {
  return (time: Date) => {
    if (!thisTime.endTime && !isEnd) {
      return false;
    }
    // 是结束时间
    return isEnd
      ? time.getTime() < new Date(thisTime.startTime).getTime()
      : time.getTime() > new Date(thisTime.endTime).getTime();
  };
};
