<script setup lang="ts">
import { ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage, ElMessageBox } from 'element-plus';

import { lock } from '#/api/warehouse-management';
import RemarkOptionSelect from '#/components/remark-option-select/index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  /** 物料库存明细*/
  inventoryData: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(['submitSuccess']);
const loading = ref(false);

/** 提交表单 */
const onSubmit = async (values: Record<string, any>) => {
  try {
    // 等待文件上传完成
    const serialNumber: any =
      await formApi?.getFieldComponentRef('serialNumber');
    const isCompleted = await serialNumber?.getCompleteStatus();
    if (!isCompleted) {
      ElMessage.warning('请等待附件上传完成');
      return;
    }
    // 检查是否有传入物料库存明细 使用传入的仓库和物料
    if (props.inventoryData.materialId && props.inventoryData.warehouseId) {
      values.materialId = props.inventoryData.materialId;
      values.warehouseId = props.inventoryData.warehouseId;
    }

    await ElMessageBox.confirm('确认提交吗？', '提示', {
      type: 'warning',
    });
    loading.value = true;
    await lock(values);
    ElMessage.success('提交成功');
    emit('submitSuccess');
  } catch (error: any) {
    ElMessage.error(error.msg || '提交失败');
  } finally {
    loading.value = false;
  }
};

/** 锁库表单 */
const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    labelClass: 'w-[100px]',
  },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});
/** 锁库原因说明 控制remark是否必填 */
const handleRemarkOptionListChange = (_: any, isDescRequired: boolean) => {
  formApi.updateSchema([
    {
      rules: isDescRequired ? 'required' : '',
      fieldName: 'remark',
    },
  ]);
};

defineExpose({
  Form,
  formApi,
});
</script>

<template>
  <Form v-loading="loading">
    <template #remarkOptionList="{ modelValue }">
      <RemarkOptionSelect
        :model-value="modelValue"
        doc-code="WM0061"
        doc-field-code="remark"
        @update:model-value="
          (optionIds) => {
            formApi.setFieldValue('remarkOptionList', optionIds);
          }
        "
        @change="handleRemarkOptionListChange"
      />
    </template>
  </Form>
</template>
