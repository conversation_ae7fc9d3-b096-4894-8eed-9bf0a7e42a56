<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';

import { getOutBoundDocPage } from '#/api/warehouse-management/index';
import { isAfter, isBefore } from '#/utils/dateUtils';
import { WS } from '#/utils/socket/common-socketio';

import CloseBound from '../components/close-bound/form-edit/index.vue';
import FormToOutbound from '../components/FormToOutbound.vue';
import PrepForm from '../material-pending/modules/Form.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      applyEndTime?: string;
      applyStartTime?: string;
      applyUserList?: string;
      closeEndTime?: string;
      closeStartTime?: string;
      collectorUserList?: string;
      docStatusList?: string;
      executorEndTime?: string;
      executorStartTime?: string;
      isProxyExec?: unknown;
      isRectify?: unknown;
      materialUser?: string;
      origDocNumberList?: string;
      origDocTypeCodeList?: string;
      outBoundDocNumberList?: string;
      preparationStateList?: string;
    }>,
    default: () => ({}),
  },
});
const wsType = [
  'wm.outbound.docstatus.rescind.passed',
  'wm.outbound.docstatus.rescind.checking',
  'wm.outbound.docstatus.rescind.reject',
];
const FormToOutboundRef = ref<InstanceType<typeof FormToOutbound>>();
const outboundDocId = ref<string>('');
const outboundDocNumber = ref<string>('');
const docStatus = ref<string>('');
const origDocTypeCode = ref<string>('');

/** 提交时间 */
const applyTime = ref({
  // 开始时间
  startTime: props.params?.applyStartTime || '',
  // 结束时间
  endTime: props.params?.applyEndTime || '',
});

/** 出库时间 */
const executorTime = ref({
  // 开始时间
  startTime: props.params?.executorStartTime || '',
  // 结束时间
  endTime: props.params?.executorEndTime || '',
});

/** 关闭时间 */
const closeTime = ref({
  // 开始时间
  startTime: props.params?.closeStartTime || '',
  // 结束时间
  endTime: props.params?.closeEndTime || '',
});

/** 出库弹窗 */
const [FormModal, formModalApi] = useVbenModal({
  confirmText: '已出库详情',
  destroyOnClose: true,
  onBeforeClose: () => {
    outboundDocId.value = '';
    outboundDocNumber.value = '';
    return true;
  },
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

/** 关闭出库弹窗 */
const [CloseModal, closeModalApi] = useVbenModal({
  confirmText: '取消出库',
  destroyOnClose: true,
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
  onBeforeClose: () => {
    outboundDocId.value = '';
    outboundDocNumber.value = '';
    return true;
  },
});

/** 备料弹窗 */
const [PrepModal, prepModalApi] = useVbenModal({
  confirmText: '确认备料',
  destroyOnClose: true,
  onBeforeClose: () => {
    outboundDocId.value = '';
    outboundDocNumber.value = '';
    return true;
  },

  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      applyTime.value = {
        startTime: '',
        endTime: '',
      };
      executorTime.value = {
        startTime: '',
        endTime: '',
      };
      closeTime.value = {
        startTime: '',
        endTime: '',
      };
      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[80px]',
    },
    collapsed: isEmpty(props.attr?.collapsed) ? true : props.attr?.collapsed,
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? true
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows || 2,
    schema: useGridFormSchema(),
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    checkboxConfig: {
      highlight: true,
    },
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          params.applyStartTime = applyTime.value.startTime;
          params.applyEndTime = applyTime.value.endTime;
          params.executorStartTime = executorTime.value.startTime;
          params.executorEndTime = executorTime.value.endTime;
          params.closeStartTime = closeTime.value.startTime;
          params.closeEndTime = closeTime.value.endTime;

          return await getOutBoundDocPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      keyField: 'supplierId',
    },
    // showOverflow: false,
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    applyUserList: props.params?.applyUserList?.split(',') || [],
    materialUser: props.params?.materialUser || '',
    origDocNumberList: props.params?.origDocNumberList?.split(',') || [],
    origDocTypeCodeList: props.params?.origDocTypeCodeList?.split(',') || [],
    outBoundDocNumberList:
      props.params?.outBoundDocNumberList?.split(',') || [],
    preparationStateList: props.params?.preparationStateList?.split(',') || [],
    isRectify: isEmpty(props.params?.isRectify) ? '' : props.params?.isRectify,
    docStatusList: props.params?.docStatusList?.split(',') || [],
    collectorUserList: props.params?.collectorUserList?.split(',') || [],
    isProxyExec: isEmpty(props.params?.isProxyExec)
      ? ''
      : props.params?.isProxyExec,
  });
  WS.on(wsType, gridApi.query);
});

function onView(row: RowType) {
  docStatus.value = row.docStatus;
  outboundDocId.value = row.outBoundDocId;
  outboundDocNumber.value = row.outBoundDocNumber;
  switch (row.docStatus) {
    case 'collected': {
      formModalApi
        .setState({
          title: '已出库详情',
          showCancelButton: true,
        })
        .open();
      break;
    }
    case 'pending': {
      formModalApi
        .setState({
          title: '确认出库',
        })
        .open();
      break;
    }

    default: {
      formModalApi
        .setState({
          title: '已关闭详情',
          showCancelButton: true,
        })
        .open();
      break;
    }
  }
}

// 取消出库
async function onCancelOutbound(row: RowType) {
  outboundDocId.value = row.outBoundDocId;
  outboundDocNumber.value = row.outBoundDocNumber;
  origDocTypeCode.value = row.origDocTypeCode;
  closeModalApi
    .setState({
      title: `取消出库`,
    })
    .open();
}

const openPrepModal = (docId: string, docNumber: string) => {
  formModalApi.close();

  outboundDocId.value = docId;
  outboundDocNumber.value = docNumber;
  prepModalApi
    .setState({
      title: `确认备料`,
    })
    .open();
};

function onOutboundSuccess() {
  formModalApi.close();
  gridApi.query();
}

function onOutboundLoading(loading: boolean) {
  formModalApi.setState({ loading });
}

function onCloseBoundLoading(loading: boolean) {
  closeModalApi.setState({ loading });
}

function onCloseBoundSuccess() {
  closeModalApi.close();
  gridApi.query();
}

function onPrepSuccess() {
  prepModalApi.close();
  gridApi.query();
}

function onPrepLoading(loading: boolean) {
  prepModalApi.setState({ loading });
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="w-10/12">
      <FormToOutbound
        ref="FormToOutboundRef"
        :doc-status="docStatus"
        :out-bound-doc-id="outboundDocId"
        :out-bound-doc-number="outboundDocNumber"
        @bound-success="onOutboundSuccess"
        @bound-loading="onOutboundLoading"
        @open-prep-modal="openPrepModal"
        @handle-cancel="formModalApi.close()"
      />
    </FormModal>

    <CloseModal class="w-3/5">
      <CloseBound
        :in-out-bound-doc-id="outboundDocId"
        :in-out-bound-doc-number="outboundDocNumber"
        :orig-doc-type-code="origDocTypeCode"
        @close-bound-loading="onCloseBoundLoading"
        @close-bound-success="onCloseBoundSuccess"
        @handle-cancel="closeModalApi.close()"
      />
    </CloseModal>

    <PrepModal class="w-10/12">
      <PrepForm
        :out-bound-doc-id="outboundDocId"
        :out-bound-doc-number="outboundDocNumber"
        @bound-success="onPrepSuccess"
        @bound-loading="onPrepLoading"
      />
    </PrepModal>

    <Grid>
      <template #form-applyTime>
        <ElDatePicker
          v-model="applyTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, applyTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="applyTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, applyTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #form-executorTime>
        <ElDatePicker
          v-model="executorTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, executorTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="executorTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, executorTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #form-closeTime>
        <ElDatePicker
          v-model="closeTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, closeTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="closeTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, closeTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #action="{ row }">
        <ElButton link type="info" @click="onView(row)">查看</ElButton>
        <ElButton
          v-if="
            row.docStatus !== 'cancelAudit' &&
            row.docStatus !== 'closed' &&
            row.docStatus !== 'collected'
          "
          link
          type="danger"
          @click="onCancelOutbound(row)"
          v-access:code="'wm:inoutbound:cancel:submit'"
        >
          取消出库
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
