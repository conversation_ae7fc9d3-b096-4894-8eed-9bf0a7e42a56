<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { InventoryAdjustment } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElDatePicker,
  ElMessage,
  ElTag,
  ElTooltip,
} from 'element-plus';

import {
  exportMyDraftDocPage,
  getMyDraftDocPage,
} from '#/api/warehouse-management';

import Form from '../modules/Form.vue';
import {
  deleteDoc,
  docStatusDict,
  openModal,
  submitDoc,
} from '../modules/method';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});
const loading = ref(false);
const formRef = ref<InstanceType<typeof Form>>();
/** 当前单据状态 */
const docStatus = ref('');
/** 库存调整单据id */
const invcAdjustDocId = ref('');
/** 库存调整单据编号 */
const invcAdjustDocNumber = ref('');
/** 审核流程实例ID */
const processInstanceId = ref('');

/** 最后修改时间 */
const modifyTime = ref({
  modifyStartTime: props.params?.modifyStartTime,
  modifyEndTime: props.params?.modifyEndTime,
});
const exportLoading = ref(false);
const isView = ref(false);
/** 是否显示暂存 */
const isShowSave = ref(false);

/**
 * 日期禁用函数
 * 开始时间不能大于结束时间
 * @param isEnd 是否是结束时间
 */
const createDisabledDate = (isEnd: boolean) => {
  return (time: Date) => {
    if (!modifyTime.value.modifyEndTime && !isEnd) {
      return false;
    }
    // 是结束时间
    return isEnd
      ? time.getTime() < new Date(modifyTime.value.modifyStartTime).getTime()
      : time.getTime() > new Date(modifyTime.value.modifyEndTime).getTime();
  };
};

/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    modifyTime.value = {
      modifyStartTime: '',
      modifyEndTime: '',
    };
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};
/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onConfirm: () => {
    formRef.value?.onSubmit();
  },
  cancelText: '关闭',
  showCancelButton: true,
  showConfirmButton: true,
});
// 表格
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    handleReset,
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || false,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getMyDraftDocPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            ...modifyTime.value,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<InventoryAdjustment.MyDraftDocPage>,
});

/** 查看 */
const onView = (row: any) => {
  isView.value = true;
  invcAdjustDocId.value = row.invcAdjustDocId;
  docStatus.value = row.docStatus;
  invcAdjustDocNumber.value = row.invcAdjustDocNumber;
  isShowSave.value = false;
  processInstanceId.value = row.processInstanceId;
  openModal(formModalApi, false, '调整单据详情');
};
/** 新增 */
const onAdd = () => {
  isView.value = false;
  isShowSave.value = true;
  invcAdjustDocId.value = '';
  invcAdjustDocNumber.value = '';
  docStatus.value = '';
  openModal(formModalApi, true, '新增');
};

/** 导出 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportMyDraftDocPage({
      ...formValues,
      ...modifyTime.value,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};

/** 提交成功 */
const submitSuccess = () => {
  // 重新获取数据
  gridApi.query();
  formModalApi.close();
};

/** 再次提交 */
const resubmit = async (id: string) => {
  docStatus.value = '';
  await formModalApi.close();
  isView.value = false;
  invcAdjustDocId.value = id;
  isShowSave.value = true;
  openModal(formModalApi, true, '再次提交');
};
/** 提交单据 */
const submit = async (id: string) => {
  await formModalApi.close();
  isView.value = false;
  invcAdjustDocId.value = id;
  isShowSave.value = true;
  docStatus.value = '';
  openModal(formModalApi, true, '提交单据');
};
onMounted(async () => {
  await gridApi.formApi.setValues({
    invcAdjustDocNumberList:
      props.params?.invcAdjustDocNumberList?.split(',') || [],
    docStatusList: props.params?.docStatusList?.split(',') || [],
  });
});
defineExpose({
  Grid,
  gridApi,
  formModalApi,
  formRef,
  resubmit,
});
</script>
<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <FormModal class="h-full w-10/12">
      <Form
        v-loading="loading"
        ref="formRef"
        :is-view="isView"
        @submit-success="submitSuccess"
        :invc-adjust-doc-id="invcAdjustDocId"
        :doc-status="docStatus"
        :process-instance-id="processInstanceId"
        :invc-adjust-doc-number="invcAdjustDocNumber"
      />
      <template #center-footer>
        <ElButton v-if="isShowSave" type="primary" @click="formRef?.onSave()">
          暂存
        </ElButton>
        <ElButton
          v-if="docStatus === '00'"
          type="primary"
          @click="submitDoc(invcAdjustDocId, formModalApi, gridApi)"
        >
          提交单据
        </ElButton>
        <ElButton
          v-if="docStatus === '00'"
          type="danger"
          @click="deleteDoc(invcAdjustDocId, formModalApi, gridApi)"
        >
          删除单据
        </ElButton>
        <ElButton
          v-if="docStatus === '20'"
          type="danger"
          @click="resubmit(invcAdjustDocId)"
        >
          再次提交
        </ElButton>
      </template>
    </FormModal>
    <Grid>
      <template #toolbar-actions>
        <ElButton type="primary" @click="onAdd"> 新增 </ElButton>
      </template>
      <template #form-modifyTime>
        <ElDatePicker
          v-model="modifyTime.modifyStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="createDisabledDate(false)"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="modifyTime.modifyEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="createDisabledDate(true)"
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #docStatusLabel="{ row }">
        <ElTag size="small" :type="docStatusDict[row.docStatus]">
          {{ row.docStatusLabel }}
        </ElTag>
      </template>
      <template #CellOperation="{ row }">
        <ElButton link size="small" @click="onView(row)" type="info">
          查看
        </ElButton>
        <ElButton
          link
          size="small"
          :type="docStatusDict[row.docStatus]"
          @click="resubmit(row.invcAdjustDocId)"
          v-if="row.docStatus === '20'"
        >
          再次提交
        </ElButton>
        <ElButton
          link
          size="small"
          type="primary"
          @click="submit(row.invcAdjustDocId)"
          v-if="row.docStatus === '00'"
        >
          提交单据
        </ElButton>
        <ElButton
          link
          size="small"
          type="danger"
          @click="deleteDoc(row.invcAdjustDocId, formModalApi, gridApi)"
          v-if="row.docStatus === '00'"
        >
          删除单据
        </ElButton>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton :loading="exportLoading" circle @click="exportHandle">
            <template #icon>
              <IconFont name="xiazai" />
            </template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
