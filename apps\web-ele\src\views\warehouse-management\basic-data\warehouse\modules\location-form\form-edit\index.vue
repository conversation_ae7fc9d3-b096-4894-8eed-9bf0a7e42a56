<script setup lang="ts">
import type { LocationInfoApi } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage, ElMessageBox } from 'element-plus';

import {
  getLocationDetail,
  modLocation,
  saveLocation,
} from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  locationId: {
    type: String,
    default: '',
  },
  locationCode: {
    type: String,
    default: '',
  },
  viewBtn: {
    type: Boolean,
    default: true,
  },
});
const emits = defineEmits(['userFormSubmitSuccess', 'cancel']);
/** 库位详细信息 */
const locationDetail = ref<LocationInfoApi.LocationDetail>();
const loading = ref(false);
/** 提交表单 基础资料表单 编辑*/
const onSubmitLocation = async (values: Record<string, any>) => {
  try {
    await modLocation({
      ...values,
      locationId: props.locationId,
    });
  } catch {
    ElMessage.error('基础资料表单提交失败');
  }
};

/** 基础资料表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  handleSubmit: onSubmitLocation,
  schema: useFormSchema(!!(props.locationId || props.locationCode)),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});

/** 校验表单 */
const validateForm = async () => {
  // 校验表单
  const verification = await formApi.validate();
  if (!verification.valid) {
    ElMessage.error('请填写完整表单');
    return false;
  }
  return true;
};
/** 提交表单 */
const submit = async () => {
  // 判断是否有id 有id则是编辑 否则是新增
  if (props.locationId || props.locationCode) {
    onSubmitEdit();
  } else {
    onSubmit();
  }
};
/** 提交表单 添加时*/
const onSubmit = async () => {
  try {
    // 校验表单
    if (!(await validateForm())) return;
    await ElMessageBox.confirm('确认提交新增吗？', '提示', {
      type: 'warning',
    });
    loading.value = true;
    // 获取基础资料
    const formData = await formApi.getValues();
    // 提交数据
    const res = await saveLocation({
      ...formData,
    });
    ElMessage.success('提交成功');
    emits('userFormSubmitSuccess', res.locationId, res.locationCode);
  } catch {
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};

/** 提交表单 编辑时 */
const onSubmitEdit = async () => {
  try {
    // 校验表单
    if (!(await validateForm())) return;
    await ElMessageBox.confirm('确认提交编辑吗？', '提示', {
      type: 'warning',
    });
    loading.value = true;
    // 提交表单
    await formApi.validateAndSubmitForm();
    ElMessage.success('提交编辑成功');
    emits('userFormSubmitSuccess');
  } catch {
    ElMessage.error('提交编辑失败');
  } finally {
    loading.value = false;
  }
};

/** 获取数据 */
const getData = async () => {
  try {
    loading.value = true;
    // 检查是否有id
    if (props.locationId || props.locationCode) {
      // 获取库位详细信息
      const resWarehouse = await getLocationDetail(
        props.locationId,
        props.locationCode,
      );
      locationDetail.value = resWarehouse;
      // 填充表单数据
      formApi.setValues(resWarehouse);
    }
  } catch {
    ElMessage.error('数据获取失败');
  } finally {
    loading.value = false;
  }
};

/** 清空表单 */
const clearForm = () => {
  formApi.resetForm();
  if (locationDetail.value?.locationId) {
    emits('cancel', locationDetail.value.locationId);
  }
};

onMounted(() => {
  getData();
});
defineExpose({
  Form,
  formApi,
  clearForm,
  onSubmit,
  onSubmitEdit,
  submit,
});
</script>
<template>
  <div class="h-full w-full">
    <ElScrollbar noresize>
      <div v-loading="loading">
        <FormCard :is-footer="false">
          <template #title>
            <span>基础资料</span>
          </template>
          <template #default>
            <Form />
          </template>
        </FormCard>
      </div>
    </ElScrollbar>
    <div class="flex min-h-[40px] justify-end" v-if="props.viewBtn">
      <ElButton type="info" @click="clearForm"> 取消 </ElButton>
      <ElButton
        type="primary"
        @click="submit"
        v-if="props.locationId || props.locationCode"
        v-access:code="'wm:location:edit:mod'"
      >
        提交编辑
      </ElButton>
      <ElButton
        type="primary"
        @click="submit"
        v-if="!props.locationId && !props.locationCode"
        v-access:code="'wm:location:edit:add'"
      >
        提交新增
      </ElButton>
    </div>
  </div>
</template>
