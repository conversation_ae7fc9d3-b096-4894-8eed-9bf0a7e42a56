<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { OutBound } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getInOutReqDocDetail } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import { useColumns } from './data';

const props = defineProps({
  /** 其它出入库申请单编号 */
  inOutReqDocNumber: {
    type: String,
    default: '',
  },
  /** 其它出入库申请单ID */
  inOutReqDocId: {
    type: String,
    default: '',
  },
});
/** 当前表单数据 */
const data = ref<OutBound.InOutBoundReqDocDetail>();
const loading = ref(false);
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: [],
    maxHeight: '500',
    keepSource: true,
    pagerConfig: {
      enabled: false,
    },
    data: [],
  } as VxeTableGridOptions,
});
/** 出库申请明细 */
const getData = async () => {
  try {
    loading.value = true;
    data.value = await getInOutReqDocDetail(
      props.inOutReqDocId,
      props.inOutReqDocNumber,
      true,
    );
    // 设置表格数据
    gridApi.setGridOptions({
      data: data.value?.reqItemList || [],
      columns: useColumns(data.value?.docStatus),
    });
  } catch {
    ElMessage.error('获取物料信息失败');
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  if (props.inOutReqDocId || props.inOutReqDocNumber) {
    getData();
  } else {
    ElMessage.error('没有申请单编号和ID');
  }
});
defineExpose({
  gridApi,
  Grid,
});
</script>

<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>出库申请明细</span>
    </template>
    <Grid>
      <template #actualQuantitySum="{ row }">
        <span
          :class="{
            'text-red-500': row.actualQuantitySum !== row.applyQuantitySum,
          }"
        >
          {{ row.actualQuantitySum }}
        </span>
      </template>
    </Grid>
  </FormCard>
</template>
