import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';

/** 调整单信息 */
export function useFormSchema(
  docStatus: string,
  docCode: string,
  isStaff: boolean,
): VbenFormSchema[] {
  const data = [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'transferDocNumber',
      label: '单据编号',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'docCodeLabel',
      label: '调拨类型',
    },
    {
      component: 'Input',
      fieldName: 'docStatusLabel',
      label: '单据状态',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'oldWarehouseName',
      label: '调出仓库',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'targetWarehouseName',
      label: '调入仓库',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'submitUserName',
      label: '申请人',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'submitTime',
      label: '提交时间',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      dependencies: {
        if(row: any) {
          return row.closeTime;
        },
        triggerFields: ['closeTime'],
      },
      fieldName: 'closeTime',
      label: '关闭时间',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      dependencies: {
        if(row: any) {
          return row.closeUserName;
        },
        triggerFields: ['closeUserName'],
      },
      fieldName: 'closeUserName',
      label: '取消申请人',
    },
    {
      component: 'Input',
      fieldName: 'remarkOptionList',
      formItemClass: 'col-span-full items-start',
      label: '取消原因',
    },
    {
      component: (props: any) => {
        if (!props.modelValue) {
          return h('div', null, '暂无附件');
        }
        return h(UploadFiles, {
          serialNumber: props.modelValue,
          showOperatButton: false,
          showTable: true,
          showThumbnail: false,
          mode: 'readMode',
          maxHeight: '400',
          tableLabel: {
            fileName: '文件名',
            fileOperate: '操作',
            filePreview: '文件预览',
            fileSize: '文件大小',
          },
        });
      },
      fieldName: 'serialNumber',
      label: '附件',
      formItemClass: 'col-span-full',
    },
    {
      component: 'Input',
      fieldName: 'documentProcess',
      formItemClass: 'col-span-full',
      label: '单据流程',
    },
  ];

  if (docStatus === '20' && docCode !== 'WM0080' && isStaff) {
    // 在 '提交时间'后面插入 '领料码'
    const submitTimeIndex = data.findIndex(
      (item) => item.fieldName === 'submitTime',
    );
    if (submitTimeIndex !== -1) {
      data.splice(submitTimeIndex + 1, 0, {
        component: 'Input',
        fieldName: 'execCode',
        label: '领料码',
      });
    }
  }

  return data;
}
