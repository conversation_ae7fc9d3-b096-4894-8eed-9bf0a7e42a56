<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElCard,
  ElDatePicker,
  ElMessage,
  ElMessageBox,
  ElTag,
  ElTooltip,
} from 'element-plus';

import {
  closeWareTransferDoc,
  execWareTransferDoc,
  exportWareTransferDoc,
  getWareTransferDocPage,
} from '#/api';
import { isAfter, isBefore } from '#/utils/dateUtils';

import EditForm from '../modules/EditForm.vue';
import ViewForm from '../modules/ViewForm.vue';
import { docStatusDict } from '../utils/index';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});

const exportLoading = ref(false);
/** 提交时间 */
const submitTime = ref({
  startTime: props.params?.submitStartTime,
  endTime: props.params?.submitEndTime,
});
/** 出库时间 */
const outFinishTime = ref({
  startTime: props.params?.outFinishStartTime,
  endTime: props.params?.outFinishEndTime,
});
/** 入库时间 */
const inFinishTime = ref({
  startTime: props.params?.inFinishStartTime,
  endTime: props.params?.inFinishEndTime,
});

onMounted(async () => {
  /** 初始化搜索条件 */
  await gridApi.formApi.setValues({
    transferDocNumberList:
      props.params?.transferDocNumberList?.split(',') || [],
    docCode: props.params?.docCode?.split(',') || '',
    oldWarehouseIdList: props.params?.oldWarehouseIdList?.split(',') || [],
    targetWarehouseIdList:
      props.params?.targetWarehouseIdList?.split(',') || [],
    submitUserList: props.params?.submitUserList?.split(',') || [],
    docStatusList: props.params?.docStatusList?.split(',') || [],
  });
});

/** 模态框组件*/
const [EditModal, editModalApi] = useVbenModal({
  connectedComponent: EditForm,
  destroyOnClose: true,
});
const [ViewModal, viewModalApi] = useVbenModal({
  connectedComponent: ViewForm,
  destroyOnClose: true,
});

/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    submitTime.value = {
      startTime: '',
      endTime: '',
    };
    outFinishTime.value = {
      startTime: '',
      endTime: '',
    };
    inFinishTime.value = {
      startTime: '',
      endTime: '',
    };
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};

/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    handleReset,
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || true,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getWareTransferDocPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            submitStartTime: submitTime.value.startTime,
            submitEndTime: submitTime.value.endTime,
            outFinishStartTime: outFinishTime.value.startTime,
            outFinishEndTime: outFinishTime.value.endTime,
            inFinishStartTime: inFinishTime.value.startTime,
            inFinishEndTime: inFinishTime.value.endTime,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions,
});

/** 判断是否显示完成调拨按钮 */
const shouldShowAutoIo = (row: any) => {
  // 无库存调拨单据数据，默认不显示
  if (!row) return false;
  // 单据类型不为WM0080，不显示
  if (row.docCode !== 'WM0080') return false;
  // 单据不为待出库，不显示
  if (row.docStatus !== '20') return false;

  return true;
};

/** 判断是否显示取消按钮 */
const shouldShowClose = (row: any) => {
  // 无库存调拨单据数据，默认不显示
  if (!row) return false;
  // 单据类型不为WM0080，不显示
  if (row.docCode !== 'WM0080') return false;
  // 单据不为待出库或待入库，不显示
  if (!['20', '30'].includes(row.docStatus)) return false;

  return true;
};

/** 刷新列表 */
const refreshList = () => {
  gridApi.query();
};

/** 新增 */
const onAdd = () => {
  editModalApi
    .setState({ title: '新增' })
    .setData({
      transferDocId: '',
      transferDocNumber: '',
      refreshList: () => {
        refreshList();
      },
    })
    .open();
};

/** 导出数据 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportWareTransferDoc({
      ...formValues,
      submitStartTime: submitTime.value.startTime,
      submitEndTime: submitTime.value.endTime,
      outFinishStartTime: outFinishTime.value.startTime,
      outFinishEndTime: outFinishTime.value.endTime,
      inFinishStartTime: inFinishTime.value.startTime,
      inFinishEndTime: inFinishTime.value.endTime,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};

/** 查看 */
const onView = (row: any) => {
  viewModalApi
    .setState({ title: '库存调拨详情' })
    .setData({
      transferDocId: row.transferDocId,
      transferDocNumber: row.transferDocNumber,
      processInstanceId: row.processInstanceId,
      isShowAutoIo: shouldShowAutoIo(row),
      isShowClose: shouldShowClose(row),
      refreshList: () => {
        refreshList();
      },
    })
    .open();
};

/** 公共弹窗 */
const dialog = async (content: string, title: string) => {
  try {
    await ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    });
    return true;
  } catch {
    return false;
  }
};

/** 完成调拨 */
const execute = async (transferDocId: string) => {
  try {
    if (await dialog('确定执行吗？', '提示')) {
      await execWareTransferDoc(transferDocId);
      ElMessage.success('执行成功');
      refreshList();
    }
  } catch {
    ElMessage.error('执行失败');
  }
};

/** 关闭单据 */
const close = async (transferDocId: string) => {
  try {
    if (await dialog('确定取消吗？', '提示')) {
      await closeWareTransferDoc(transferDocId);
      ElMessage.success('取消成功');
      refreshList();
    }
  } catch {
    ElMessage.error('取消失败');
  }
};
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <EditModal class="h-full w-10/12" />
    <ViewModal class="h-full w-10/12" />

    <ElCard class="h-full" body-class="h-full !p-[10px]">
      <Grid>
        <template #toolbar-actions>
          <ElButton
            v-access:code="['wm:ware:transfer:submit']"
            type="primary"
            @click="onAdd"
          >
            新增
          </ElButton>
        </template>
        <template #form-submitTime>
          <ElDatePicker
            v-model="submitTime.startTime"
            type="datetime"
            placeholder="开始日期"
            :disabled-date="
              (time: Date) =>
                isAfter(time, submitTime.endTime || new Date('2099-12-31'))
            "
            value-format="YYYY-MM-DD HH:mm"
            format="YYYY-MM-DD HH:mm"
            time-format="HH:mm"
            class="!w-full"
          />
          <span class="px-[10px] text-[16px]">-</span>
          <ElDatePicker
            v-model="submitTime.endTime"
            type="datetime"
            placeholder="结束日期"
            :disabled-date="
              (time: Date) =>
                isBefore(time, submitTime.startTime || new Date('1900-01-01'))
            "
            value-format="YYYY-MM-DD HH:mm"
            time-format="HH:mm"
            format="YYYY-MM-DD HH:mm"
            :default-time="new Date(2000, 1, 1, 23, 59)"
            class="!w-full"
          />
        </template>
        <template #form-outFinishTime>
          <ElDatePicker
            v-model="outFinishTime.startTime"
            type="datetime"
            placeholder="开始日期"
            :disabled-date="
              (time: Date) =>
                isAfter(time, outFinishTime.endTime || new Date('2099-12-31'))
            "
            value-format="YYYY-MM-DD HH:mm"
            format="YYYY-MM-DD HH:mm"
            time-format="HH:mm"
            class="!w-full"
          />
          <span class="px-[10px] text-[16px]">-</span>
          <ElDatePicker
            v-model="outFinishTime.endTime"
            type="datetime"
            placeholder="结束日期"
            :disabled-date="
              (time: Date) =>
                isBefore(
                  time,
                  outFinishTime.startTime || new Date('1900-01-01'),
                )
            "
            value-format="YYYY-MM-DD HH:mm"
            time-format="HH:mm"
            format="YYYY-MM-DD HH:mm"
            :default-time="new Date(2000, 1, 1, 23, 59)"
            class="!w-full"
          />
        </template>
        <template #form-inFinishTime>
          <ElDatePicker
            v-model="inFinishTime.startTime"
            type="datetime"
            placeholder="开始日期"
            :disabled-date="
              (time: Date) =>
                isAfter(time, inFinishTime.endTime || new Date('2099-12-31'))
            "
            value-format="YYYY-MM-DD HH:mm"
            format="YYYY-MM-DD HH:mm"
            time-format="HH:mm"
            class="!w-full"
          />
          <span class="px-[10px] text-[16px]">-</span>
          <ElDatePicker
            v-model="inFinishTime.endTime"
            type="datetime"
            placeholder="结束日期"
            :disabled-date="
              (time: Date) =>
                isBefore(time, inFinishTime.startTime || new Date('1900-01-01'))
            "
            value-format="YYYY-MM-DD HH:mm"
            time-format="HH:mm"
            format="YYYY-MM-DD HH:mm"
            :default-time="new Date(2000, 1, 1, 23, 59)"
            class="!w-full"
          />
        </template>
        <template #docStatusLabel="{ row }">
          <ElTag size="small" :type="docStatusDict[row.docStatus]">
            {{ row.docStatusLabel }}
          </ElTag>
        </template>
        <template #CellOperation="{ row }">
          <div>
            <ElButton link size="small" @click="onView(row)" type="info">
              查看
            </ElButton>
            <ElButton
              v-access:code="['wm:ware:transfer:exec']"
              v-if="shouldShowAutoIo(row)"
              link
              size="small"
              @click="execute(row.transferDocId)"
              type="primary"
            >
              完成调拨
            </ElButton>
            <ElButton
              v-access:code="['wm:ware:transfer:close']"
              v-if="shouldShowClose(row)"
              link
              size="small"
              @click="close(row.transferDocId)"
              type="danger"
            >
              取消单据
            </ElButton>
          </div>
        </template>
        <template #toolbar-tools>
          <ElTooltip
            class="box-item"
            effect="light"
            content="导出数据"
            placement="top-start"
          >
            <ElButton
              v-access:code="['wm:ware:transfer:export']"
              :loading="exportLoading"
              circle
              @click="exportHandle"
            >
              <template #icon><IconFont name="xiazai" /></template>
            </ElButton>
          </ElTooltip>
        </template>
      </Grid>
    </ElCard>
  </Page>
</template>
