<script setup lang="ts">
import type { PropType } from 'vue';

import type { InventoryQueryApi } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
import { useVbenForm } from '@girant/adapter';
import { ElMessage, ElMessageBox } from 'element-plus';

import { lock } from '#/api/warehouse-management';

import { useFormSchema } from './data';

const props = defineProps({
  /** 库存情况 */
  inventoryData: {
    type: Object as PropType<InventoryQueryApi.InventoryData>,
    default: () => ({}),
  },
});
const emit = defineEmits(['submitSuccess']);
const loading = ref(false);
const fileRef = ref<InstanceType<typeof UploadFiles>>();
/** 确认框 */
const confirm = (content: string, title: string) => {
  return new Promise((resolve) => {
    ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
      .then(() => {
        resolve(true);
      })
      .catch(() => {});
  });
};
/** 提交表单 */
const onSubmit = async (values: Record<string, any>) => {
  try {
    const isCompleted = await fileRef.value?.isComplete;
    if (!isCompleted) {
      ElMessage.error('等待文件上传完成');
      return;
    }
    await confirm('确认提交吗？', '提示');
    loading.value = true;
    await lock(values);
    ElMessage.success('提交成功');
    emit('submitSuccess');
  } catch {
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};

/** 锁库表单 */
const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    labelClass: 'w-[100px]',
  },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});
onMounted(() => {
  if (props.inventoryData.warehouseId && props.inventoryData.materialId) {
    formApi.setValues({
      warehouseId: props.inventoryData.warehouseId,
      materialId: props.inventoryData.materialId,
    });
  }
});
defineExpose({
  Form,
  formApi,
});
</script>

<template>
  <Form v-loading="loading">
    <template #warehouseId>
      <span>{{ inventoryData.warehouseName }}</span>
    </template>
    <template #materialId>
      <span>{{ inventoryData.materialName }}</span>
    </template>
  </Form>
</template>
