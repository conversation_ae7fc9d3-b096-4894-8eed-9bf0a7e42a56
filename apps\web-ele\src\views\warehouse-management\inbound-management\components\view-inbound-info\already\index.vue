<script setup lang="ts">
import type { InBoundDocApi } from '#/api/warehouse-management/index';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage, ElTag } from 'element-plus';

import { getInBoundDocDetail } from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';
import StepProgress from '#/components/step-progress/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
});

const inBoundData = ref<InBoundDocApi.InBoundDocDetail>(
  {} as InBoundDocApi.InBoundDocDetail,
);

const loading = ref(false);

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-9',
});

/** 获取数据 */
const getInBoundDocDetailHandle = async () => {
  try {
    loading.value = true;
    const inBoundRes = await getInBoundDocDetail({
      inBoundDocId: props.inBoundDocId,
      inBoundDocNumber: props.inBoundDocNumber,
      isQueryItem: false,
    });
    inBoundData.value = inBoundRes;
    return inBoundRes;
  } catch {
    ElMessage.error('获取入库单据失败');
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  if (props.inBoundDocId && props.inBoundDocNumber) {
    const inBoundRes = await getInBoundDocDetailHandle();
    if (inBoundRes) {
      inBoundData.value = inBoundRes;
      formApi.setValues(inBoundRes);
    }
  }
});
</script>

<template>
  <div class="relative">
    <IconFont
      name="yiruku"
      :size="150"
      color="dark:bg-gray-800"
      class="text-primary-500 absolute right-20 top-14"
    />
    <FormCard :is-footer="false" title="入库信息">
      <template #default>
        <Form v-loading="loading">
          <template #inBoundDocNumber="{ modelValue }">
            <div>
              {{ modelValue }}
              <ElTag type="success">{{ inBoundData.docStatusLabel }}</ElTag>
              <ElTag v-if="inBoundData.isRectify" type="primary" class="ml-2">
                补录
              </ElTag>
            </div>
          </template>
          <template #applyUserName="{ modelValue }">
            <div>
              {{ modelValue }}
              <span v-if="inBoundData.applyUserDeptName">
                ({{ inBoundData.applyUserDeptName }})
              </span>
            </div>
          </template>
          <template #executorUserName="{ modelValue }">
            <div>
              {{ modelValue }}
              <span v-if="inBoundData.executorUserDeptName">
                ({{ inBoundData.executorUserDeptName }})
              </span>
            </div>
          </template>

          <template #docProcess>
            <StepProgress
              v-if="inBoundData?.inBoundDocNumber"
              :doc-number="inBoundData?.inBoundDocNumber"
              class="min-h-[65px] overflow-x-auto"
            />
            <div v-else>/</div>
          </template>
        </Form>
      </template>
    </FormCard>
  </div>
</template>
