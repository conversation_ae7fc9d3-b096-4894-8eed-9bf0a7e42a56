<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItemData } from '../types';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElButton, ElTag } from 'element-plus';

const props = defineProps({
  // 物料信息（包含仓库、库位信息）
  materialItemData: {
    type: Object as PropType<MaterialItemData.DocItem>,
    default: () => ({}),
  },
});

const emits = defineEmits(['handleMaterialCode']);

const handleMaterialCode = () => {
  emits('handleMaterialCode', props.materialItemData.materialId);
};
</script>
<template>
  <div class="flex pt-2">
    <div class="ml-10 w-[50px]" v-if="materialItemData.pictureFileId">
      <ImageViewer
        :src="materialItemData.pictureFileId"
        class="h-[50px] rounded-md"
      />
    </div>
    <div class="ml-4 flex flex-1 justify-between">
      <div class="name space-x-2">
        <span class="text-primary-500 font-medium">
          {{ materialItemData.materialName }}
          <ElTag v-if="!materialItemData.isStandard" type="danger" size="small">
            非标
          </ElTag>
        </span>

        <ElButton
          type="info"
          link
          class="underline"
          @click="handleMaterialCode"
        >
          {{ materialItemData.materialCode }}
        </ElButton>

        <span class="text-sm text-gray-500">
          规格型号： {{ materialItemData.materialCategory }}
        </span>
      </div>
      <div class="text-sm text-gray-600">
        <slot name="rightExtra" :material-item-data="materialItemData"></slot>
        <template v-if="!$slots.rightExtra">
          <span>出库合计：</span>
          <span class="font-medium">{{ materialItemData.quantitySum }}</span>
          <span class="text-gray-500">
            ({{ materialItemData.baseUnitLabel }})
          </span>
        </template>
      </div>
    </div>
  </div>
</template>
