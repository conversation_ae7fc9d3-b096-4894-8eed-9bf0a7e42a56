import type { MaterialItem } from '../../../components/wuliao-item/types/index.ts';

import type { InBoundDocApi } from '#/api/warehouse-management/index';

// 状态常量
export const DOC_STATUS = {
  DRAFT: '05', // 取消审核中
  COMPLETED: '10', // 已入库
  CANCELLED: '99', // 已取消
} as const;

// 转换物料项列表数据
export const transformItemList = (
  item: InBoundDocApi.InBoundDocDetail['inBoundItemList'][0],
  docStatus: string,
): MaterialItem.Item[] => {
  // 草稿和取消状态返回空数组
  if (docStatus === DOC_STATUS.DRAFT || docStatus === DOC_STATUS.CANCELLED) {
    return [];
  }

  // 已入库状态使用实际数据
  if (docStatus === DOC_STATUS.COMPLETED) {
    return item.actualItemList.map((actualItem) => ({
      ...actualItem,
      quantity: actualItem.actualQuantity,
    }));
  }

  // 其他状态使用申请数据
  return item.applyItemList.map((applyItem) => ({
    ...applyItem,
    quantity: applyItem.applyQuantity,
  }));
};

// 转换入库单据数据为组件所需格式
export const transformInBoundData = (
  res: InBoundDocApi.InBoundDocDetail,
): MaterialItem.MaterialItemData => {
  return {
    ...res,
    inBoundDocId: res.inBoundDocId || '',
    inBoundDocNumber: res.inBoundDocNumber || '',
    inBoundItemList:
      res.inBoundItemList?.map((item) => ({
        ...item,
        quantitySum:
          res.docStatus === DOC_STATUS.COMPLETED
            ? item.actualQuantitySum
            : item.applyQuantitySum,
        itemList: transformItemList(item, res.docStatus),
      })) || [],
  };
};
