import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import type { dictItemListType } from '#/api/common';

import { h, markRaw } from 'vue';

import { ElInputTag, ElTag } from 'element-plus';

import { getDictItemList } from '#/api/common';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';
/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'inOutReqDocNumberList',
      label: '单据编号',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'modifyTime',
      label: '修改时间',
      formItemClass: 'col-span-2',
    },
    {
      component: h(DeptStaffTree, {
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'materialUserList',
      label: '使用人',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return warehouseList;
        },
        api: () => {
          return getDictItemList('wmOtherOutReqType');
        },
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'docCodeList',
      label: '出库类型',
      formItemClass: 'col-span-1',
    },
  ];
}

/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      slots: {
        default: ({ row }) => {
          return h(
            'div',
            {
              class: 'relative',
            },
            [
              h('span', row.inOutReqDocNumber || '暂无单据编号'),
              row.isRectify
                ? h(
                    ElTag,
                    {
                      type: 'primary',
                      class: 'ml-2 absolute top-1/2 right-0 -translate-y-1/2',
                    },
                    { default: () => '补录' },
                  )
                : null,
            ],
          );
        },
      },
      title: '单据编号',
      field: 'inOutReqDocNumber',
      minWidth: 235,
    },
    {
      title: '出库类型',
      field: 'docCodeLabel',
      minWidth: 100,
    },
    {
      title: '修改时间',
      field: 'modifyTime',
      minWidth: 115,
    },
    {
      slots: {
        default: 'materialUserName',
      },
      title: '使用人',
      field: 'materialUserName',
      minWidth: 150,
    },
    {
      slots: {
        default: 'docStatusLabel',
      },
      title: '单据状态',
      field: 'docStatusLabel',
      minWidth: 110,
    },
    {
      align: 'center',
      slots: {
        default: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      minWidth: 150,
      title: '操作',
    },
  ];
}
