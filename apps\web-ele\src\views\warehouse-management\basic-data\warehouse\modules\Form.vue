<script setup lang="ts">
import { onMounted, ref } from 'vue';

import locationForm from './location-form/index.vue';
import WarehouseForm from './warehouse-form/index.vue';

const props = defineProps({
  /** 当前表单 */
  form: {
    type: String,
    default: 'warehouse',
  },
  /** 当前id */
  currentId: {
    type: String,
    default: '',
  },
  /** 当前编号 */
  currentCode: {
    type: String,
    default: '',
  },
  /** 当前选中的仓库id */
  warehouseId: {
    type: String,
    default: '',
  },

  /** 是否是查看 */
  isView: {
    type: Boolean,
    default: false,
  },
});
/** 提交成功 */
const emits = defineEmits(['formSubmitSuccess']);
/** 表单组件 */
const locationRef = ref<InstanceType<typeof locationForm>>();

onMounted(() => {
  if (props.warehouseId) {
    locationRef.value?.locationRef?.formApi.setValues({
      warehouseId: props.warehouseId,
    });
  }
});
defineExpose({
  props,
});
</script>

<template>
  <div class="w-full">
    <WarehouseForm
      v-if="form === 'warehouse'"
      :warehouse-id="currentId"
      :warehouse-code="currentCode"
      :is-view="isView"
      @form-submit-success="emits('formSubmitSuccess')"
      class="!border-primary"
    />
    <locationForm
      v-else
      ref="locationRef"
      :location-id="currentId"
      :location-code="currentCode"
      :is-view="isView"
      class="!border-primary"
      @form-submit-success="emits('formSubmitSuccess')"
    />
  </div>
</template>
