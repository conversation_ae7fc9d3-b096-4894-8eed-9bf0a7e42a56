<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import { onMounted, ref } from 'vue';

import { useAccess } from '@vben/access';
import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElDatePicker,
  ElMessage,
  ElMessageBox,
  ElTag,
} from 'element-plus';

import {
  delInOutReqDoc,
  exportInOutMyDraftDocPage,
  getOtherMyDraftDocPage,
} from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';

import { docStatusDict } from '../config/list';
import FormEdit from '../modules/FormEdit.vue';
import FormView from '../modules/FormView.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});
const { hasAccessByCodes } = useAccess();
const formRef = ref();
const exportLoading = ref(false);
/** 其它出入库申请单编号 */
const inOutReqDocNumber = ref('');
/** 其它出入库申请单id */
const inOutReqDocId = ref('');
/** 审核流程实例ID */
const processInstanceId = ref('');
const isView = ref(false);
/** 当前单据状态 */
const docStatus = ref('');
/** 是否显示暂存 */
const isShowSave = ref(false);
/** 最后修改时间 */
const modifyTime = ref({
  modifyStartTime: props.params?.modifyStartTime,
  modifyEndTime: props.params?.modifyEndTime,
});

/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onConfirm: () => {
    formRef.value?.onSubmit();
  },
  confirmText: '提交',
  showCancelButton: true,
  showConfirmButton: hasAccessByCodes(['wm:outboundreq:submit']),
});
/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    modifyTime.value = {
      modifyStartTime: '',
      modifyEndTime: '',
    };
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};
/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || false,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    handleReset,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getOtherMyDraftDocPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            ...modifyTime.value,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions,
});

/** 查看 */
const onView = (row: any) => {
  isView.value = true;
  docStatus.value = row.docStatus;
  inOutReqDocId.value = row.inOutReqDocId;
  inOutReqDocNumber.value = row.inOutReqDocNumber;
  processInstanceId.value = row.processInstanceId;
  isShowSave.value = false;
  formModalApi
    .setState({
      showConfirmButton: false,
      title: '其他出入库申请单详情',
    })
    .open();
};
/** 新增 */
const onAdd = () => {
  isView.value = false;
  isShowSave.value = true;
  inOutReqDocId.value = '';
  inOutReqDocNumber.value = '';
  docStatus.value = '';
  formModalApi
    .setState({
      showConfirmButton: hasAccessByCodes(['wm:outboundreq:submit']),
      title: '新增出库申请',
    })
    .open();
};
/** 提交单据 */
const onSubmit = async (row: any) => {
  isView.value = false;
  inOutReqDocId.value = row.inOutReqDocId;
  inOutReqDocNumber.value = row.inOutReqDocNumber;
  docStatus.value = '';
  isShowSave.value = true;
  formModalApi
    .setState({
      showConfirmButton: hasAccessByCodes(['wm:outboundreq:submit']),
      title: '其他出库申请单详情',
    })
    .open();
};
/** 删除单据 */
const onDelete = async (inOutReqDocId: string) => {
  try {
    await ElMessageBox.confirm('确认删除？', '提示', {
      type: 'warning',
    });
    await delInOutReqDoc(inOutReqDocId);
    ElMessage.success('删除成功');
    formModalApi.close();
    gridApi.query();
  } catch {
    ElMessage.error('删除失败');
  }
};
/** 再次提交 */
const resubmit = async () => {
  await formModalApi.close();
  isView.value = false;
  isShowSave.value = true;
  docStatus.value = '';
  formModalApi
    .setState({
      showConfirmButton: hasAccessByCodes(['wm:outboundreq:submit']),
      title: '再次提交',
    })
    .open();
};
/** 导出 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportInOutMyDraftDocPage({
      ...formValues,
      ...modifyTime.value,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};

/** 提交成功 */
const submitSuccess = () => {
  // 重新获取数据
  gridApi.query();
  formModalApi.close();
};
onMounted(async () => {
  await gridApi.formApi.setValues({
    inOutReqDocNumberList:
      props.params?.inOutReqDocNumberList?.split(',') || [],
    materialUserList: props.params?.materialUserList?.split(',') || [],
    docCodeList: props.params?.docCodeList?.split(',') || [],
  });
});
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <FormModal class="h-full w-8/12">
      <FormView
        v-if="isView"
        :in-out-req-doc-number="inOutReqDocNumber"
        :in-out-req-doc-id="inOutReqDocId"
        :process-instance-id="processInstanceId"
      />
      <FormEdit
        ref="formRef"
        v-else
        @submit-success="submitSuccess"
        :in-out-req-doc-number="inOutReqDocNumber"
        :in-out-req-doc-id="inOutReqDocId"
      />
      <template #center-footer>
        <ElButton
          v-if="isShowSave"
          type="primary"
          @click="formRef?.onSave()"
          v-access:code="'wm:outboundreq:submit'"
        >
          暂存
        </ElButton>
        <ElButton
          v-if="docStatus === '00'"
          type="danger"
          @click="onDelete(inOutReqDocId)"
        >
          删除单据
        </ElButton>
        <ElButton
          v-if="docStatus === '80'"
          type="danger"
          @click="resubmit()"
          v-access:code="'wm:outboundreq:submit'"
        >
          再次提交
        </ElButton>
      </template>
    </FormModal>
    <Grid>
      <template #toolbar-actions>
        <ElButton
          type="primary"
          @click="onAdd"
          v-access:code="'wm:outboundreq:submit'"
        >
          新增
        </ElButton>
      </template>
      <template #form-modifyTime>
        <ElDatePicker
          v-model="modifyTime.modifyStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, modifyTime.modifyEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="modifyTime.modifyEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                modifyTime.modifyStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          class="!w-full"
          :default-time="new Date(2000, 1, 1, 23, 59)"
        />
      </template>
      <template #materialUserName="{ row }">
        <span>{{ row.materialUserName }}</span>
        <span v-if="row.materialUserDeptName">
          ({{ row.materialUserDeptName }})
        </span>
      </template>
      <template #docStatusLabel="{ row }">
        <ElTag size="small" :type="docStatusDict[row.docStatus]">
          {{ row.docStatusLabel }}
        </ElTag>
      </template>
      <template #CellOperation="{ row }">
        <ElButton link size="small" @click="onView(row)" type="info">
          查看
        </ElButton>
        <ElButton
          v-if="row.docStatus === '00'"
          link
          size="small"
          @click="onSubmit(row)"
          type="primary"
          v-access:code="'wm:outboundreq:submit'"
        >
          提交单据
        </ElButton>
        <ElButton
          v-if="row.docStatus === '80'"
          link
          size="small"
          @click="onSubmit(row)"
          type="primary"
          v-access:code="'wm:outboundreq:submit'"
        >
          再次提交
        </ElButton>
        <ElButton
          link
          size="small"
          @click="onDelete(row.inOutReqDocId)"
          type="danger"
        >
          删除
        </ElButton>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            :loading="exportLoading"
            circle
            @click="exportHandle"
            v-access:code="'wm:outboundreq:export'"
          >
            <template #icon>
              <IconFont name="xiazai" />
            </template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
