import { ElMessage, ElMessageBox } from 'element-plus';

import {
  closeWareTransferDoc,
  delWareTransferDoc,
  execWareTransferDoc,
} from '#/api';

/** 调拨单据状态Tag类型*/
export const docStatusDict: { [key: string]: any } = {
  /** 待提交 */
  '00': 'warning',
  /** 待审核 */
  '10': 'primary',
  /** 待出库 */
  '20': 'primary',
  /** "取消审核中" */
  '25': 'warning',
  /** 待入库 */
  '30': 'primary',
  /** 已完成 */
  '40': 'success',
  /** 审核驳回 */
  '80': 'danger',
  /** 已关闭 */
  '90': 'info',
};

/** 调拨单据状态图片 dictKey*/
export const docStatusIconDict: { [key: string]: any } = {
  /** 已完成 */
  '40': {
    name: 'yiwan<PERSON>',
    color: 'text-lime-500',
  },
  /** 审核驳回 */
  '80': {
    name: 'shenhebutongguo',
    color: 'text-red-500',
  },
  /** 已关闭 */
  '90': {
    name: 'yiguanbi',
    color: 'text-slate-500 !text-gray-300',
  },
};

/** 提示会话框 */
export const dialog = (content: string, title: string) => {
  return new Promise((resolve) => {
    ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
      .then(() => {
        resolve(true);
      })
      .catch(() => {});
  });
};

/** 打开模态框 */
export const openModal = (
  /** 模态框API */
  formModalApi: any,
  /** 是否显示确认按钮 */
  showConfirmButton: boolean,
  /** 标题 */
  title: string,
) => {
  formModalApi
    .setState({
      showConfirmButton,
      title,
    })
    .open();
};

/** 判断是否显示完成调拨按钮 */
export const shouldShowAutoIo = (row: any) => {
  // 无库存调拨单据数据，默认不显示
  if (!row) return false;
  // 单据类型不为WM0080，不显示
  if (row.docCode !== 'WM0080') return false;
  // 单据不为待出库，不显示
  if (row.docStatus !== '20') return false;

  return true;
};

/** 判断是否显示取消按钮 */
export const shouldShowClose = (row: any) => {
  // 无库存调拨单据数据，默认不显示
  if (!row) return false;
  // 单据类型不为WM0080，不显示
  if (row.docCode !== 'WM0080') return false;
  // 单据不为待出库或待入库，不显示
  if (!['20', '30'].includes(row.docStatus)) return false;

  return true;
};

/** 删除单据 */
export const deleteDoc = async (
  transferDocId: string,
  formModalApi: any,
  gridApi: any,
) => {
  try {
    if (await dialog('确定删除吗？', '提示')) {
      await delWareTransferDoc(transferDocId);
      ElMessage.success('删除成功');
      formModalApi.close();
      gridApi.query();
    }
  } catch {
    ElMessage.error('删除失败');
  }
};

/** 自动完成出入库 */
export const autoExecute = async (
  transferDocId: string,
  formModalApi: any,
  gridApi: any,
) => {
  try {
    if (await dialog('确定执行吗？', '提示')) {
      await execWareTransferDoc(transferDocId);
      ElMessage.success('执行成功');
      formModalApi.close();
      gridApi.query();
    }
  } catch {
    ElMessage.error('执行失败');
  }
};

/** 关闭拆卸单据 */
export const closeDoc = async (
  transferDocId: string,
  formModalApi: any,
  gridApi: any,
) => {
  try {
    if (await dialog('确定取消吗？', '提示')) {
      await closeWareTransferDoc(transferDocId);
      ElMessage.success('取消成功');
      formModalApi.close();
      gridApi.query();
    }
  } catch {
    ElMessage.error('取消失败');
  }
};
