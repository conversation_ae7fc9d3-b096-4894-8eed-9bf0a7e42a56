<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';

import { getInboundDocumentsPage } from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';
import { WS } from '#/utils/socket/common-socketio';

import CloseBound from '../components/close-bound/form-edit/index.vue';
import FormToInboundModal from '../modules/FormToInboundModal.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      applyEndTime?: string;
      applyStartTime?: string;
      applyUserList?: string;
      closeEndTime?: string;
      closeStartTime?: string;
      docStatusList?: string;
      executorEndTime?: string;
      executorStartTime?: string;
      executorUserList?: string;
      inBoundDocNumberList?: string;
      origDocNumberList?: string;
      origDocTypeCodeList?: string;
    }>,
    default: () => ({}),
  },
});

// ws消息类型
const wsType = [
  'wm.inbound.docstatus.rescind.passed',
  'wm.inbound.docstatus.rescind.checking',
  'wm.inbound.docstatus.rescind.reject',
];
/** 提交时间 */
const applyTime = ref({
  // 开始时间
  startTime: props.params?.applyStartTime || '',
  // 结束时间
  endTime: props.params?.applyEndTime || '',
});

/** 入库时间 */
const executorTime = ref({
  // 开始时间
  startTime: props.params?.executorStartTime || '',
  // 结束时间
  endTime: props.params?.executorEndTime || '',
});

/** 关闭时间 */
const closeTime = ref({
  // 开始时间
  startTime: props.params?.closeStartTime || '',
  // 结束时间
  endTime: props.params?.closeEndTime || '',
});

const inBoundDocId = ref<string>('');
const inBoundDocNumber = ref<string>('');
const origDocTypeCode = ref<string>('');

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      applyTime.value = {
        startTime: '',
        endTime: '',
      };
      executorTime.value = {
        startTime: '',
        endTime: '',
      };
      closeTime.value = {
        startTime: '',
        endTime: '',
      };
      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[75px]',
    },
    schema: useGridFormSchema(),
    collapsed: isEmpty(props.attr?.collapsed) ? true : props.attr?.collapsed,
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? true
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    checkboxConfig: {
      highlight: true,
    },
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          };

          params.applyStartTime = applyTime.value.startTime;
          params.applyEndTime = applyTime.value.endTime;
          params.executorStartTime = executorTime.value.startTime;
          params.executorEndTime = executorTime.value.endTime;
          params.closeStartTime = closeTime.value.startTime;
          params.closeEndTime = closeTime.value.endTime;

          if (params.executorUserList) {
            params.executorUserList = params.executorUserList.join(',');
          }

          if (params.applyUserList) {
            params.applyUserList = params.applyUserList.join(',');
          }

          return await getInboundDocumentsPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      keyField: 'supplierId',
    },
    // showOverflow: false,
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    inBoundDocNumberList: props.params?.inBoundDocNumberList?.split(',') || [],
    origDocNumberList: props.params?.origDocNumberList?.split(',') || [],
    origDocTypeCodeList: props.params?.origDocTypeCodeList?.split(',') || [],
    applyUserList: props.params?.applyUserList?.split(',') || [],
    executorUserList: props.params?.executorUserList?.split(',') || [],
    docStatusList: props.params?.docStatusList?.split(',') || [],
  });

  WS.on(wsType, refreshList);
});

/** 刷新列表 */
const refreshList = () => {
  gridApi.query();
};

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: FormToInboundModal,
  destroyOnClose: true,
  closeOnClickModal: false,
});

/** 关闭出入库弹窗 */
const [CloseModal, closeModalApi] = useVbenModal({
  confirmText: '取消入库',
  destroyOnClose: true,
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
  onBeforeClose: () => {
    inBoundDocId.value = '';
    inBoundDocNumber.value = '';
    return true;
  },
});

function onView(row: RowType) {
  let title = '确认入库';

  switch (row.docStatus) {
    case 'cancelAudit': {
      title = '取消审核详情';
      break;
    }

    case 'closed': {
      title = '已关闭详情';
      break;
    }

    case 'pending': {
      title = '确认入库';
      break;
    }

    case 'stocked': {
      title = '已入库详情';
      break;
    }

    default: {
      title = '已关闭详情';
      break;
    }
  }

  formModalApi
    .setState({
      title,
    })
    .setData({
      inBoundDocId: row.inBoundDocId,
      inBoundDocNumber: row.inBoundDocNumber,
      docStatus: row.docStatus,
      refreshList: () => {
        refreshList();
      },
    })
    .open();
}

// 取消入库
async function onCancelInbound(row: RowType) {
  inBoundDocId.value = row.inBoundDocId;
  inBoundDocNumber.value = row.inBoundDocNumber;
  origDocTypeCode.value = row.origDocTypeCode;

  closeModalApi
    .setState({
      title: `取消入库`,
    })
    .open();
}

function onCloseBoundLoading(loading: boolean) {
  closeModalApi.setState({ loading });
}

function onCloseBoundSuccess() {
  closeModalApi.close();
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="w-10/12" />

    <CloseModal class="w-3/5">
      <CloseBound
        :in-out-bound-doc-id="inBoundDocId"
        :in-out-bound-doc-number="inBoundDocNumber"
        :orig-doc-type-code="origDocTypeCode"
        @close-bound-loading="onCloseBoundLoading"
        @close-bound-success="onCloseBoundSuccess"
        @handle-cancel="closeModalApi.close()"
      />
    </CloseModal>
    <Grid>
      <template #form-applyTime>
        <ElDatePicker
          v-model="applyTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, applyTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="applyTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, applyTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #form-executorTime>
        <ElDatePicker
          v-model="executorTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, executorTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="executorTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, executorTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #form-closeTime>
        <ElDatePicker
          v-model="closeTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, closeTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="closeTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, closeTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #action="{ row }">
        <ElButton link type="info" @click="onView(row)">查看</ElButton>
        <ElButton
          v-access:code="'wm:inoutbound:cancel:submit'"
          v-if="
            row.docStatus !== 'cancelAudit' &&
            row.docStatus !== 'closed' &&
            row.docStatus !== 'stocked'
          "
          link
          type="danger"
          @click="onCancelInbound(row)"
        >
          取消入库
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
