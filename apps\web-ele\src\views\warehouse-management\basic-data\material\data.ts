import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import type { WarehouseInfoApi } from '#/api/warehouse-management';
import type { materialConfig } from '#/api/warehouse-management/basic-data/material';

import { h, markRaw } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElInputTag } from 'element-plus';

import { getLocationList, getWarehouseList } from '#/api/warehouse-management';

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '多个编号用回车分隔',
      },
      fieldName: 'materialCodeList',
      label: '物料编号',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'materialName',
      label: '物料名称',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        class: 'w-full',
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择默认仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getWarehouseList();
        },
      },
      labelWidth: 120,
      formItemClass: 'col-span-1',
      defaultValue: [],
      fieldName: 'warehouseIdList',
      label: '默认仓库',
    },
    {
      component: 'Select',
      renderComponentContent: () => ({
        default: ({ item }: { item: any }) =>
          `${item.label} [${item.locationCode}]`,
      }),
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        options: [],
        placeholder: '请选择默认库位',
      },
      dependencies: {
        async componentProps(values) {
          const locationList: any[] = [];
          if (values.warehouseIdList && values.warehouseIdList.length > 0) {
            // 获取库位列表 多个仓库
            const requests = values.warehouseIdList.map((item: string) =>
              getLocationList({
                warehouseId: item,
              }),
            );
            const responses = await Promise.all(requests);
            // 合并结果
            responses.forEach((res: any) => {
              if (res.length > 0) {
                locationList.push(
                  ...res.map(
                    (item: {
                      locationCode: string;
                      locationId: string;
                      locationName: string;
                    }) => ({
                      label: item.locationName,
                      value: item.locationId,
                      locationCode: item.locationCode,
                    }),
                  ),
                );
              }
            });
            // 过滤掉locationIdList不存在locationList中的库位
            values.locationIdList = values.locationIdList.filter(
              (item: string) =>
                locationList.some((location: any) => location.value === item),
            );
          } else {
            const res = await getLocationList();
            const data = res.map((item: any) => ({
              label: item.locationName,
              value: item.locationId,
              locationCode: item.locationCode,
            }));
            locationList.push(...data);
          }
          return {
            options: locationList,
          };
        },
        triggerFields: ['warehouseIdList'],
      },
      labelWidth: 120,
      formItemClass: 'col-span-1',
      defaultValue: [],
      fieldName: 'locationIdList',
      label: '默认库位',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择参与安全库存预警的仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getWarehouseList();
        },
      },
      labelClass: 'min-w-[98px]',
      labelWidth: 120,
      formItemClass: 'col-span-1',
      defaultValue: [],
      fieldName: 'safetyInvcWarehouseIdList',
      label: '安全库存预警',
    },
    {
      component: 'Input',
      fieldName: 'safetyInventory',
      label: '安全库存',
      formItemClass: 'col-span-2',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择参与呆滞分析的仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getWarehouseList();
        },
      },
      defaultValue: [],
      formItemClass: 'col-span-1',
      fieldName: 'slowMovWarehouseIdList',
      label: '呆滞分析',
    },
    {
      component: 'Input',
      fieldName: 'obsoletePeriod',
      label: '呆滞期',
      formItemClass: 'col-span-2',
    },
  ];
}

/** 表格 */
export function useColumns<T = materialConfig.materialPage>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      slots: {
        default: ({ row }) =>
          h(ImageViewer, {
            imgId: row.pictureFileId,
            imgCss: 'h-[50px]',
          }),
      },
      title: '图片',
      field: 'pictureFileId',
      minWidth: 90,
    },
    {
      title: '物料编号',
      field: 'materialCode',
      minWidth: 100,
    },
    {
      title: '物料名称',
      field: 'materialName',
      minWidth: 100,
    },
    {
      title: '物料规格',
      field: 'materialSpecs',
      minWidth: 200,
    },
    {
      slots: {
        default: 'warehouseName',
      },
      title: '默认仓库/库位',
      field: 'warehouseName',
      minWidth: 100,
    },
    {
      title: '单位',
      field: 'baseUnitLabel',
      minWidth: 40,
    },
    {
      slots: {
        default: 'safetyInventory',
      },
      title: '安全库存',
      field: 'safetyInventory',
      className: '!text-left',
      minWidth: 170,
    },
    {
      slots: {
        default: 'obsoletePeriod',
      },
      title: '呆滞期/天',
      field: 'obsoletePeriod',
      className: '!text-left',
      minWidth: 170,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'materialName',
          nameTitle: '物料配置',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: ['view', 'edit'],
      },
      field: 'operation',
      fixed: 'right',
      minWidth: 90,
      title: '操作',
    },
  ];
}
