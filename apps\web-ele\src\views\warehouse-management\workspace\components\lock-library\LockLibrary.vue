<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';

import { CountTo } from '@vben/common-ui';

import { getLockMaterialNum } from '#/api/warehouse-management';
import { WS } from '#/utils/socket/common-socketio';

export default defineComponent({
  components: { CountTo },
  emits: ['cardClick'],
  setup(_props, { emit }) {
    const wsType = ['wm.inventorylock.lock', 'wm.inventorylock.unlock'];

    const loading = ref(true);
    const lockTypeCount = ref(0); // 已锁物料（项）
    const lockCount = ref(0); // 数量合计

    const fetchLockMaterialData = async () => {
      try {
        const response = await getLockMaterialNum();
        lockTypeCount.value = response.matKindCnt || 0;
        lockCount.value = response.quantitySum || 0;
      } catch (error) {
        console.error('获取锁库数据失败:', error);
      } finally {
        loading.value = false;
      }
    };

    onMounted(() => {
      WS.on(wsType, fetchLockMaterialData);
      fetchLockMaterialData();
    });

    const handleCardClick = () => {
      emit('cardClick', {
        name: 'lock-library',
        attrs: {
          wrapperClass:
            'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2',
          collapsed: true,
          collapsedRows: 2,
          showCollapseButton: true,
        },
      });
    };

    return {
      lockTypeCount,
      lockCount,
      loading,
      handleCardClick,
    };
  },
});
</script>
<template>
  <div
    class="hover:bg-primary-100 bg-primary-50 cursor-pointer rounded-lg px-3 py-2 text-black"
    @click="handleCardClick"
  >
    <div class="pb-1 text-sm font-bold">锁库情况</div>
    <div>
      <div class="flex h-6 items-end">
        <span class="ml-1 text-sm">已锁</span>
        <span class="mx-1 -mb-0.5 text-xl font-bold text-gray-700">
          <CountTo
            v-loading="loading"
            :start-val="0"
            :end-val="lockTypeCount"
            :duration="1500"
            separator=""
          />
        </span>
        <span class="ml-1 text-sm">项物料</span>
      </div>
      <div class="flex h-6 items-end">
        <span class="ml-1 text-sm">数量合计</span>
        <span class="mx-1 mb-0.5 font-bold text-gray-700">
          <CountTo
            v-loading="loading"
            :start-val="0"
            :end-val="lockCount"
            :duration="1500"
            separator=""
          />
        </span>
      </div>
    </div>
  </div>
</template>
