<script setup lang="ts">
import { computed, defineAsyncComponent, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

/** 共享数据 */
const shareData = ref();

const [Modal, modalApi] = useVbenModal({
  cancelText: '关闭',
  onClosed() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      shareData.value = modalApi.getData<Record<string, any>>();
    }
  },
});

const currentComponent = computed(() => {
  switch (shareData.value.type) {
    case 'inBound': {
      return defineAsyncComponent(
        () => import('../components/inbound-view-form/index.vue'),
      );
    }
    case 'inCancel': {
      return defineAsyncComponent(
        () =>
          import(
            '#/views/warehouse-management/inbound-management/components/close-bound/form-view/index.vue'
          ),
      );
    }
    case 'material': {
      return defineAsyncComponent(
        () =>
          import(
            '#/views/warehouse-management/basic-data/material/modules/material-form/index.vue'
          ),
      );
    }
    case 'outBound': {
      return defineAsyncComponent(
        () => import('../components/outbound-view-form/index.vue'),
      );
    }
    case 'outCancel': {
      return defineAsyncComponent(
        () =>
          import(
            '#/views/warehouse-management/outbound-management/components/close-bound/form-view/index.vue'
          ),
      );
    }
    default: {
      return null;
    }
  }
});
</script>

<template>
  <Modal>
    <component :is="currentComponent" v-bind="shareData.attr" />
  </Modal>
</template>
