import type { VxeTableGridOptions } from '@girant/adapter';

import type { VbenFormSchema } from '@vben/common-ui';

import type { dictItemListType } from '#/api/common';
import type {
  MaterialCategoryTreeType,
  WarehouseInfoApi,
} from '#/api/warehouse-management';

import { h, markRaw, ref } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElBadge, ElCascader, ElInputTag, ElMessage } from 'element-plus';

import { getDictItemList } from '#/api/common';
import {
  getLocationList,
  getMaterialCategoryTree,
  getWarehouseList,
} from '#/api/warehouse-management';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

/** 物料细类数据 */
const materialCategoryData = ref<any[]>([]);
/** 获取物料细类数据 */
const getData = async () => {
  try {
    // 获取物料细类数据
    const data = await getMaterialCategoryTree();
    // 处理数据
    const convertMaterialData = (item: any) => {
      return {
        label: item.categoryName,
        value: item.categoryCode,
        children: item.children
          ? item.children.map((child: MaterialCategoryTreeType) =>
              convertMaterialData(child),
            )
          : [],
      };
    };
    // 执行转换
    materialCategoryData.value = data.map((item) => convertMaterialData(item));
  } catch {
    ElMessage.error('获取数据失败');
  }
};
getData();
/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'invcAdjustDocNumberList',
      label: '单据编号',
      formItemClass: 'col-span-1',
    },
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'materialCodeList',
      formItemClass: 'col-span-1',
      label: '物料编号',
    },
    {
      componentProps: {
        clearable: true,
      },
      component: 'Input',
      fieldName: 'materialName',
      label: '物料名',
    },
    {
      componentProps: {
        clearable: true,
      },
      component: 'Input',
      fieldName: 'batchNumber',
      label: '批次号',
    },
    {
      component: h(DeptStaffTree, {
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'submitUserList',
      label: '提交人',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'submitTime',
      formItemClass: 'col-span-2 w-full',
      label: '提交时间',
    },
    {
      component: h(DeptStaffTree, {
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'executorUserList',
      label: '执行人',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'execTime',
      formItemClass: 'col-span-2 w-full',
      label: '执行时间',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          // 过滤待提交 value=00
          const filterList = warehouseList.filter(
            (item) => item.value !== '00',
          );
          return filterList;
        },
        api: () => {
          return getDictItemList('publicDocStatus');
        },
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'docStatusList',
      label: '单据状态',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        multiple: false,
        filterable: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getWarehouseList();
        },
      },
      fieldName: 'warehouseIdList',
      formItemClass: 'col-span-1',
      label: '仓库',
    },
    {
      component: 'Select',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        options: [],
        placeholder: '请选择库位',
      },
      dependencies: {
        async componentProps(values) {
          // 获取仓库下的库位
          const res = await getLocationList({
            warehouseId: values.warehouseIdList,
          });
          // 提取出库位id和库位名称
          const locationList = res?.map((item) => ({
            label: item.locationName,
            value: item.locationId,
          }));
          // 过滤掉locationIdList不存在locationList中的库位
          values.locationIdList = values.locationIdList.filter((item: string) =>
            locationList.some((location: any) => location.value === item),
          );
          return {
            options: locationList,
          };
        },

        triggerFields: ['warehouseIdList'],
      },
      defaultValue: [],
      fieldName: 'locationIdList',
      formItemClass: 'col-span-1',
      label: '库位',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return warehouseList;
        },
        api: () => {
          return getDictItemList('baseMaterialAttribute');
        },
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'materialAttributeList',
      label: '物料属性',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return warehouseList;
        },
        api: () => {
          return getDictItemList('baseMaterialType');
        },
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'materialTypeList',
      label: '物料大类',
      formItemClass: 'col-span-1',
    },
    {
      component: h(ElCascader, {
        class: 'w-full',
        props: {
          multiple: true,
          emitPath: false,
        },
        options: materialCategoryData.value,
        'max-collapse-tags': 1,
        'collapse-tags': true,
        filterable: true,
        'collapse-tags-tooltip': true,
        clearable: true,
      }),
      modelPropName: 'v-model',
      fieldName: 'materialCategoryList',
      label: '物料细类',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
      fieldName: 'isStandard',
      label: '是否标准物料',
      labelWidth: 100,
    },
  ];
}
/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      title: '单据编号',
      field: 'invcAdjustDocNumber',
      minWidth: 120,
    },
    {
      slots: {
        default: 'docStatusLabel',
      },
      title: '单据状态',
      field: 'docStatusLabel',
      minWidth: 110,
    },
    {
      slots: {
        default: 'quantity',
      },
      title: '调整数量',
      field: 'quantity',
      minWidth: 100,
    },

    {
      title: '提交人',
      field: 'submitUserName',
      minWidth: 100,
    },
    {
      title: '提交时间',
      field: 'submitTime',
      minWidth: 115,
    },
    {
      title: '执行人',
      field: 'executorUserName',
      minWidth: 100,
    },
    {
      title: '调整时间',
      field: 'execTime',
      minWidth: 115,
    },
    {
      slots: {
        default: ({ row }) =>
          h(ImageViewer, {
            imgId: row.pictureFileId,
            imgCss: 'h-[50px]',
          }),
      },
      title: '物料图片',
      field: 'pictureFileId',
      minWidth: 90,
    },
    {
      title: '物料编号',
      slots: {
        default: ({ row }) => {
          return h(
            ElBadge,
            {
              value: '非标',
              type: 'primary',
              offset: [-15, -5],
              badgeStyle: 'border-radius:0px',
              hidden: row.isStandard,
              class: 'item',
            },
            {
              default: () => row.materialCode,
            },
          );
        },
      },
      field: 'materialCode',
      minWidth: 150,
    },
    {
      title: '物料名称',
      field: 'materialName',
      minWidth: 100,
    },
    {
      title: '具体规格',
      field: 'materialSpecs',
      minWidth: 200,
    },
    {
      title: '物料属性',
      field: 'materialAttributeLabel',
      minWidth: 65,
    },
    {
      title: '物料大类',
      field: 'materialTypeLabel',
      minWidth: 65,
    },
    {
      title: '物料细类',
      field: 'materialCategoryName',
      minWidth: 100,
    },
    {
      title: '仓库',
      field: 'warehouseName',
      minWidth: 100,
    },
    {
      title: '库位',
      field: 'locationName',
      minWidth: 100,
    },
    {
      title: '批次号',
      field: 'batchNumber',
      minWidth: 100,
    },

    {
      align: 'center',
      slots: {
        default: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      minWidth: 175,
      title: '操作',
    },
  ];
}
