<script setup lang="ts">
import type { InBoundDocApi } from '#/api/warehouse-management/index';

import { computed, defineAsyncComponent, markRaw, onMounted, ref } from 'vue';

import { ElMessage } from 'element-plus';

import { getInBoundDocDetail } from '#/api/warehouse-management/index';

const props = defineProps({
  inBoundDocId: {
    type: String,
    default: '',
  },
  inBoundDocNumber: {
    type: String,
    default: '',
  },
  docStatus: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['inboundSuccess', 'inboundError']);

const formRef = ref();

const currentDocStatus = ref(props.docStatus);

const inBoundData = ref<InBoundDocApi.InBoundDocDetail>(
  {} as InBoundDocApi.InBoundDocDetail,
);

/** 获取数据 */
const getInBoundDocDetailHandle = async () => {
  try {
    const inBoundRes = await getInBoundDocDetail({
      inBoundDocId: props.inBoundDocId,
      inBoundDocNumber: props.inBoundDocNumber,
      isQueryItem: false,
    });
    inBoundData.value = inBoundRes;
    return inBoundRes;
  } catch {
    ElMessage.error('获取入库单据失败');
  }
};

onMounted(async () => {
  if (!currentDocStatus.value) {
    const inBoundRes = await getInBoundDocDetailHandle();
    if (inBoundRes) {
      currentDocStatus.value = inBoundRes.docStatus;
    }
  }
});

const currentComponent = computed(() => {
  switch (currentDocStatus.value) {
    // 待入库
    case '00': {
      return markRaw(defineAsyncComponent(() => import('./EditForm.vue')));
    }

    // 已入库
    case '10': {
      return markRaw(defineAsyncComponent(() => import('./ViewForm.vue')));
    }

    // 已关闭
    default: {
      return markRaw(defineAsyncComponent(() => import('./ViewForm.vue')));
    }
  }
});

const execInboundHandle = async () => {
  const res = await formRef.value?.submitAllForm();

  if (res) {
    emits('inboundSuccess');
  } else {
    emits('inboundError');
  }
};

defineExpose({
  execInboundHandle,
});
</script>

<template>
  <component
    v-if="currentDocStatus"
    :is="currentComponent"
    ref="formRef"
    :in-bound-doc-id="inBoundDocId"
    :in-bound-doc-number="inBoundDocNumber"
    :doc-status="currentDocStatus"
  />
</template>
