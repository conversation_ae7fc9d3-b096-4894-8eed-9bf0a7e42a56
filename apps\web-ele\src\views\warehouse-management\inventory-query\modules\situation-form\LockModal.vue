<script setup lang="ts">
import { h, nextTick, onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElInputNumber } from 'element-plus';

import LockForm from '#/views/warehouse-management/inventory-lock/modules/lock-form/index.vue';
/** 共享数据 */
const shareData = ref();
const lockFormRef = ref();
const [Modal, modalApi] = useVbenModal({
  title: '新增锁库',
  footer: true,
  showCancelButton: true,
  showConfirmButton: true,
  confirmText: '提交',
  onConfirm: () => {
    lockFormRef.value!.formApi.validateAndSubmitForm();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      shareData.value = modalApi.getData<Record<string, any>>();
    }
  },
  onOpened() {
    // 定制锁库表单
    nextTick(async () => {
      const schema = lockFormRef.value?.formApi.getState()?.schema;
      if (!schema) return;
      // 设置仓库
      const warehouse = schema.find(
        (item: any) => item.fieldName === 'warehouseId',
      );
      warehouse!.component = h(
        'div',
        null,
        shareData.value.inventoryData.warehouseName,
      );
      warehouse!.rules = undefined;
      // 设置物料
      const material = schema.find(
        (item: any) => item.fieldName === 'materialId',
      );
      material!.component = h(
        'div',
        null,
        shareData.value.inventoryData.materialName,
      );
      material!.rules = undefined;
      // 设置基本单位
      const baseUnit = schema.find(
        (item: any) => item.fieldName === 'baseUnitLabel',
      );
      baseUnit!.component = h(
        'div',
        null,
        shareData.value.inventoryData.baseUnitLabel,
      );
      // 设置规格型号
      const materialSpecs = schema.find(
        (item: any) => item.fieldName === 'materialSpecs',
      );
      materialSpecs!.component = h(
        'div',
        null,
        shareData.value.inventoryData.materialSpecs,
      );
      // 设置锁库表单
      const blockQuantity = schema.find(
        (item: any) => item.fieldName === 'blockQuantity',
      );
      blockQuantity!.component = (props: any) => {
        return h(
          'div',
          {
            class: 'flex items-center gap-2',
          },
          [
            h(
              ElInputNumber,
              {
                modelValue: props.modelValue,
                'onUpdate:modelValue': (value: number | undefined) => {
                  if (value !== undefined && value !== null) {
                    // 向外部发送更新事件
                    props['onUpdate:modelValue']?.(value);
                  }
                },
                min: 1,
                max: shareData.value.inventoryData.availableInventory,
                precision: 0,
              },
              {},
            ),
            h(
              'div',
              {
                class: `text-sm ml-4`,
              },
              {
                default: () =>
                  `当前库存量：${shareData.value.inventoryData.inventory || 0},可用量：${shareData.value.inventoryData.availableInventory || 0}`,
              },
            ),
          ],
        );
      };
      lockFormRef.value?.formApi.updateSchema(schema);
    });
  },
});
onMounted(() => {});
</script>

<template>
  <Modal>
    <LockForm
      ref="lockFormRef"
      :inventory-data="shareData.inventoryData"
      @submit-success="shareData.refreshForm()"
    />
  </Modal>
</template>
