<script setup lang="ts">
import type { InBoundDocApi } from '#/api/warehouse-management';

import { computed, onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { ElMessage } from 'element-plus';

import {
  getInBoundDocDetail,
  getInBoundDocDetailByInCancelDoc,
} from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';
import TriangleCard from '#/components/triangle-card/Index.vue';
import MaterialsItem from '#/views/warehouse-management/inbound-management/components/materials-item/index.vue';

const props = defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
  cancelDocId: {
    default: '',
    type: String,
  },
});

const inBoundData = ref<InBoundDocApi.InBoundDocDetail>(
  {} as InBoundDocApi.InBoundDocDetail,
);

/** 获取数据 */
const getInBoundDocDetailHandle = async () => {
  try {
    const inBoundRes = await getInBoundDocDetail({
      inBoundDocId: props.inBoundDocId,
      inBoundDocNumber: props.inBoundDocNumber,
      isQueryItem: true,
    });
    inBoundData.value = inBoundRes;
    return inBoundRes;
  } catch {
    ElMessage.error('获取入库单据失败');
  }
};

const getInBoundDocDetailByInCancelDocHandle = async () => {
  try {
    const inBoundRes = await getInBoundDocDetailByInCancelDoc({
      inCancelDocId: props.cancelDocId,
      isQueryItem: true,
    });
    inBoundData.value = inBoundRes;
    return inBoundRes;
  } catch {
    ElMessage.error('获取入库单据失败');
    return {} as InBoundDocApi.InBoundDocDetail;
  }
};

const itemList = computed(() => inBoundData.value.inBoundItemList);

onMounted(async () => {
  const isCancelDoc = !isEmpty(props.cancelDocId); // 是否为取消单据id

  switch (isCancelDoc) {
    case false: {
      if (props.inBoundDocId || props.inBoundDocNumber) {
        await getInBoundDocDetailHandle();
      } else {
        ElMessage.error('缺少入库单据id');
      }
      break;
    }
    case true: {
      if (props.cancelDocId) {
        await getInBoundDocDetailByInCancelDocHandle();
      } else {
        ElMessage.error('缺少取消单据id');
      }
      break;
    }
    default: {
      ElMessage.error('缺少入库单据id或取消单据id');
    }
  }
});
</script>

<template>
  <FormCard :is-footer="false">
    <template #title>
      <span>入库明细</span>
    </template>

    <template #titleMore>
      <span class="text-sm text-gray-500">
        共
        <span class="text-primary-500">
          {{ inBoundData?.inBoundItemList?.length || 0 }}
        </span>
        种物料
      </span>
    </template>
    <template #default v-if="itemList?.length > 0">
      <template v-for="(item, index) in itemList" :key="item.materialId">
        <TriangleCard :number="index + 1" title="" class="mb-5">
          <template #content>
            <MaterialsItem :material-item-data="item" />
          </template>
        </TriangleCard>
      </template>
    </template>
  </FormCard>
</template>
