<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItem } from '../types/index.ts';

import Box from './Box.vue';
import DynamicFormSelect from './dynamic-form-select/ViewDynamicFormSelect.vue';
import WarehouseSelect from './warehouse-select/ViewWarehouseSelect.vue';

defineProps({
  warehouseItemData: {
    type: Object as PropType<MaterialItem.WarehouseItemDataType>,
    default: () => ({}),
  },
});
</script>

<template>
  <Box>
    <template #left>
      <div class="flex h-full w-full items-center">
        <WarehouseSelect :warehouse-item-data="warehouseItemData" />
      </div>
    </template>
    <template #right>
      <div class="ml-4">
        <DynamicFormSelect :warehouse-item-data="warehouseItemData" />
      </div>
    </template>
  </Box>
</template>
