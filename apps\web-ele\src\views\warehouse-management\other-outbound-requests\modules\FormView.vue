<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { ElScrollbar } from 'element-plus';

import { getInOutReqDocDetail } from '#/api/warehouse-management';
import ApprovalTimeline from '#/components/approval-timeline/Index.vue';
import FormCard from '#/components/form-card/Index.vue';

import OutboundOrderDetailsView from './outbound-order-details/form-view/index.vue';
import OutboundOrderInformationView from './outbound-order-information/form-view/index.vue';

const props = defineProps({
  /** 其它出入库申请单id */
  inOutReqDocId: {
    type: String,
    default: '',
  },
  /** 其它出入库申请单编号 */
  inOutReqDocNumber: {
    type: String,
    default: '',
  },
  /** 审核流程实例ID */
  processInstanceId: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
/** 审核流程实例ID */
const processId = ref(props.processInstanceId);

/** 获取审核流程实例ID */
const getProcessId = async () => {
  try {
    const res = await getInOutReqDocDetail(
      props.inOutReqDocId,
      props.inOutReqDocNumber,
    );
    processId.value = res?.processInstanceId;
  } catch (error) {
    console.error(error);
  }
};

onMounted(async () => {
  // 如果没有审核流程，发送请求获取
  if (!processId.value) {
    getProcessId();
  }
});
defineExpose({
  loading,
});
</script>
<template>
  <div v-loading="loading">
    <OutboundOrderInformationView
      :in-out-req-doc-number="inOutReqDocNumber"
      :in-out-req-doc-id="inOutReqDocId"
    />
    <OutboundOrderDetailsView
      :in-out-req-doc-number="inOutReqDocNumber"
      :in-out-req-doc-id="inOutReqDocId"
    />
    <FormCard :is-footer="false">
      <template #title>
        <span>审核流程</span>
      </template>
      <ElScrollbar>
        <ApprovalTimeline v-if="processId" :process-instance-id="processId" />
      </ElScrollbar>
    </FormCard>
  </div>
</template>
