<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage, ElMessageBox, ElScrollbar } from 'element-plus';

import {
  delInOutReqDocInBound,
  getInOutReqDocDetail,
} from '#/api/warehouse-management';
import ApprovalTimeline from '#/components/approval-timeline/Index.vue';
import FormCard from '#/components/form-card/Index.vue';

import InboundOrderDetailsView from './inbound-order-details/form-view/index.vue';
import InboundOrderInformationView from './inbound-order-information/form-view/index.vue';
/** 共享数据 */
const sharedData = ref();
const loading = ref(false);

/** 获取审核流程实例ID*/
const getProcessId = async () => {
  try {
    const res = await getInOutReqDocDetail(
      sharedData.value.inOutReqDocId,
      sharedData.value.inOutReqDocNumber,
    );
    sharedData.value.processInstanceId = res?.processInstanceId;
  } catch (error) {
    console.error(error);
  }
};
const [Modal, modalApi] = useVbenModal({
  showConfirmButton: false,
  onCancel() {
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      sharedData.value = modalApi.getData<Record<string, any>>();
      if (!sharedData.value.processInstanceId) {
        getProcessId();
      }
    }
  },
});
/** 刷新列表 */
const refreshList = () => {
  sharedData.value.refreshList();
  modalApi.close();
};
/** 删除单据 */
const onDelete = async (inOutReqDocId: string) => {
  try {
    await ElMessageBox.confirm('确认删除？', '提示', {
      type: 'warning',
    });
    await delInOutReqDocInBound(inOutReqDocId);
    ElMessage.success('删除成功');
    refreshList();
  } catch {
    ElMessage.error('删除失败');
  }
};
defineExpose({
  loading,
});
</script>
<template>
  <Modal>
    <div v-loading="loading">
      <InboundOrderInformationView
        :in-out-req-doc-number="sharedData.inOutReqDocNumber"
        :in-out-req-doc-id="sharedData.inOutReqDocId"
      />
      <InboundOrderDetailsView
        :in-out-req-doc-number="sharedData.inOutReqDocNumber"
        :in-out-req-doc-id="sharedData.inOutReqDocId"
      />
      <FormCard :is-footer="false">
        <template #title>
          <span>审核流程</span>
        </template>
        <ElScrollbar>
          <ApprovalTimeline
            v-if="sharedData.processInstanceId"
            :process-instance-id="sharedData.processInstanceId"
          />
        </ElScrollbar>
      </FormCard>
    </div>
    <template #center-footer>
      <ElButton
        v-if="sharedData.docStatus === '00'"
        type="danger"
        @click="onDelete(sharedData.inOutReqDocId)"
      >
        删除单据
      </ElButton>
    </template>
  </Modal>
</template>
