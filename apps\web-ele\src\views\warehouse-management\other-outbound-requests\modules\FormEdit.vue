<script setup lang="ts">
import { ref } from 'vue';

import { useAccess } from '@vben/access';
import { useVbenModal } from '@vben/common-ui';

import { ElMessage, ElMessageBox } from 'element-plus';

import { saveInOutReqDoc, submitInOutReqDoc } from '#/api/warehouse-management';

import OutboundOrderDetailsEdit from './outbound-order-details/form-edit/index.vue';
import OutboundOrderInformationEdit from './outbound-order-information/form-edit/index.vue';

const { hasAccessByCodes } = useAccess();
/** 共享数据 */
const sharedData = ref();
/** 调整信息ref*/
const adjustFormRef = ref();
/** 物料信息ref*/
const materialFormRef = ref();
const loading = ref(false);
const [Modal, modalApi] = useVbenModal({
  confirmText: '提交',
  title: '新增出库申请',
  closeOnClickModal: false,
  showConfirmButton: hasAccessByCodes(['wm:outboundreq:submit']),
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    onSubmit();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      sharedData.value = modalApi.getData<Record<string, any>>();
    }
  },
});
/** 校验表单 */
const validateForm = async () => {
  // 校验调整信息表单 // 校验物料信息表单
  const [verification, verification2] = await Promise.all([
    adjustFormRef.value?.validateForm(),
    materialFormRef.value?.validateForm(),
  ]);
  // 获取表单数据
  const data = await getFormData();
  if (data.applyItemList.length === 0) {
    ElMessage.error('请填写物料信息');
    return false;
  }
  if (!verification || !verification2) {
    ElMessage.error('请填写完整表单');
    return false;
  }
  return true;
};
/** 获取表单数据 */
const getFormData = async () => {
  const [data, data2] = await Promise.all([
    adjustFormRef.value?.getFormData(),
    materialFormRef.value?.getFormData(),
  ]);
  return {
    ...data,
    applyItemList: data2,
  };
};
/** 刷新列表 */
const refreshList = () => {
  sharedData.value.refreshList();
  modalApi.close();
};
/** 提交表单 新增*/
const onSubmit = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    await ElMessageBox.confirm('确定提交吗？', '提示', {
      type: 'warning',
    });
    loading.value = true;
    await submitInOutReqDoc({
      inOutReqDocId: sharedData.value.inOutReqDocId,
      ...data,
    });
    ElMessage.success('提交成功');
    refreshList();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};
/** 提交暂存 */
const onSave = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    await ElMessageBox.confirm('确定提交暂存吗？', '提示', {
      type: 'warning',
    });
    loading.value = true;
    await saveInOutReqDoc({
      inOutReqDocId: sharedData.value.inOutReqDocId,
      ...data,
    });
    ElMessage.success('提交暂存成功');
    refreshList();
  } catch (error) {
    console.error(error);
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};
defineExpose({
  onSubmit,
  onSave,
  validateForm,
  loading,
});
</script>
<template>
  <Modal>
    <div v-loading="loading">
      <OutboundOrderInformationEdit
        ref="adjustFormRef"
        :in-out-req-doc-number="sharedData.inOutReqDocNumber"
        :in-out-req-doc-id="sharedData.inOutReqDocId"
      />
      <OutboundOrderDetailsEdit
        ref="materialFormRef"
        :in-out-req-doc-number="sharedData.inOutReqDocNumber"
        :in-out-req-doc-id="sharedData.inOutReqDocId"
      />
    </div>
    <template #center-footer>
      <ElButton
        v-if="sharedData.isShowSave || !sharedData.inOutReqDocId"
        type="primary"
        @click="onSave()"
        v-access:code="'wm:outboundreq:submit'"
      >
        暂存
      </ElButton>
    </template>
  </Modal>
</template>
