<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItem } from '../types/index.ts';

import { nextTick, ref, watch } from 'vue';

import Box from './Box.vue';
import EditDynamicFormSelect from './dynamic-form-select/EditDynamicFormSelect.vue';
import WarehouseSelect from './warehouse-select/EditWarehouseSelect.vue';

const props = defineProps({
  materialItemData: {
    type: Object as PropType<MaterialItem.MaterialInfo>,
    default: () => ({}),
  },

  warehouseItemData: {
    type: Object as PropType<MaterialItem.WarehouseItemDataType>,
    default: () => ({}),
  },

  selectWarehouseList: {
    type: Array as PropType<MaterialItem.SelectWarehouseListType[]>,
    default: () => [],
  },

  index: {
    type: Number,
    default: 0,
  },

  inChangeWarehouse: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(['warehouseChange', 'entryQuantityChange']);

const editDynamicFormSelectRef =
  ref<InstanceType<typeof EditDynamicFormSelect>>();

const currentWarehouseId = ref<string>('');

watch(
  () => props.warehouseItemData.warehouseId,
  (newVal) => {
    currentWarehouseId.value = newVal;
  },
  { immediate: true, deep: true },
);

// 仓库改变
const warehouseChange = (warehouseId: string) => {
  currentWarehouseId.value = warehouseId;

  nextTick(() => {
    emits('warehouseChange');
  });
};

// 当前仓库填入的数量
const currentWarehouseFillQuantity = ref(0);

// 获取当前仓库填入的数量
const getCurrentWarehouseFillQuantity = async () => {
  const fillQuantity =
    editDynamicFormSelectRef.value?.currentWarehouseFillQuantity || 0;

  currentWarehouseFillQuantity.value = fillQuantity;

  emits('entryQuantityChange');
};

const validateFormData = async (): Promise<boolean> => {
  const validateFormData =
    await editDynamicFormSelectRef.value?.validateFormData();
  return validateFormData;
};

const getFormData = async (): Promise<any[]> => {
  const formData =
    await editDynamicFormSelectRef.value?.getDynamicFormData(true);
  return formData;
};

defineExpose({
  currentWarehouseId,
  currentWarehouseFillQuantity,
  getFormData,
  validateFormData,
});
</script>

<template>
  <Box>
    <template #left>
      <div class="flex h-full w-full items-center">
        <WarehouseSelect
          :warehouse-item-data="warehouseItemData"
          :select-warehouse-list="selectWarehouseList"
          :in-change-warehouse="inChangeWarehouse"
          @warehouse-change="warehouseChange"
        />

        <div class="flex items-center">
          <slot name="delete-wrapper"></slot>
        </div>
      </div>
    </template>
    <template #right>
      <div class="ml-4">
        <EditDynamicFormSelect
          ref="editDynamicFormSelectRef"
          :warehouse-item-data="warehouseItemData"
          :current-warehouse-id="currentWarehouseId"
          :material-id="materialItemData.materialId"
          @entry-quantity-change="getCurrentWarehouseFillQuantity"
        />
      </div>
    </template>
  </Box>
</template>
