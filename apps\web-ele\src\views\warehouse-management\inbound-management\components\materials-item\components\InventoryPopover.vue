<script setup lang="ts">
import { InfoFilled } from '@element-plus/icons-vue';

defineProps({
  currentWarehouseId: {
    type: String,
    default: '',
  },
  inventory: {
    type: Number,
    default: 0,
  },
  inventoryAddQuantity: {
    type: Number,
    default: 0,
  },
  availableInventory: {
    type: Number,
    default: 0,
  },
  availableInventoryAddQuantity: {
    type: Number,
    default: 0,
  },
});
</script>

<template>
  <ElPopover
    width="250px"
    trigger="hover"
    :show-arrow="true"
    popper-class="inventory-popover"
  >
    <template #reference>
      <el-icon
        class="hover:!text-primary-500 ml-1 cursor-pointer !text-gray-400 transition-colors"
      >
        <InfoFilled />
      </el-icon>
    </template>
    <div class="inventory-info w-full" v-if="currentWarehouseId">
      <table>
        <thead>
          <tr>
            <th></th>
            <th>入库前</th>
            <th>入库后</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>库存量</td>
            <td>{{ inventory }}</td>
            <td>{{ inventoryAddQuantity }}</td>
          </tr>
          <tr>
            <td>可用量</td>
            <td>{{ availableInventory }}</td>
            <td>{{ availableInventoryAddQuantity }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div v-else class="p-2 text-center text-sm text-gray-500">请先选择仓库</div>
  </ElPopover>
</template>
<style scoped>
.inventory-info {
  @apply rounded-lg bg-white p-2;
}

.inventory-info table {
  @apply w-full border-separate border-spacing-0;
}

.inventory-info th,
.inventory-info td {
  @apply p-2 text-center text-sm;
}

.inventory-info th {
  @apply bg-gray-50 font-medium text-gray-600;
}

.inventory-info td:first-child {
  @apply text-left font-medium text-gray-600;
}

.inventory-info tr:not(:last-child) td {
  @apply border-b border-gray-100;
}

:deep(.el-button.el-button--default) {
  @apply !text-base !font-medium;
}

:deep(.el-button--default.is-link) {
  @apply !text-primary-500 hover:!text-primary-600;
}

:deep(.el-button--danger.is-link) {
  @apply !text-red-500 hover:!text-red-600;
}
</style>
