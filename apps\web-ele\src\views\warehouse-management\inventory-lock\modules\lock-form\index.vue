<script setup lang="ts">
import { ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { lock } from '#/api/warehouse-management';

import { confirm } from '../method';
import { useFormSchema } from './data';

const emit = defineEmits(['submitSuccess']);
const loading = ref(false);

/** 提交表单 */
const onSubmit = async (values: Record<string, any>) => {
  try {
    // 等待文件上传完成
    const serialNumber: any =
      await formApi?.getFieldComponentRef('serialNumber');
    const isCompleted = await serialNumber?.getCompleteStatus();
    if (!isCompleted) {
      ElMessage.warning('请等待附件上传完成');
      return;
    }
    await confirm('确认提交吗？', '提示');
    loading.value = true;
    await lock(values);
    ElMessage.success('提交成功');
    emit('submitSuccess');
  } catch (error: any) {
    ElMessage.error(error.msg || '提交失败');
  } finally {
    loading.value = false;
  }
};

/** 锁库表单 */
const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    labelClass: 'w-[100px]',
  },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

defineExpose({
  Form,
  formApi,
});
</script>

<template>
  <Form v-loading="loading">
    <!-- <template #blockQuantity="slotProps">
      {{ console.log(slotProps) }}
      <ElInputNumber v-bind="slotProps" :min="1" />
    </template> -->
  </Form>
</template>
