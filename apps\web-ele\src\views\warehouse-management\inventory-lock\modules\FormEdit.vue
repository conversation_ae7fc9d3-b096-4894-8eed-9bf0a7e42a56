<script setup lang="ts">
import { ref } from 'vue';

import { useAccess } from '@vben/access';
import { useVbenModal } from '@vben/common-ui';

import LockForm from './lock-form/index.vue';

const emit = defineEmits(['submitSuccess']);
const { hasAccessByCodes } = useAccess();
/** 锁库表单ref */
const lockFormRef = ref();
/** 共享数据 */
const sharedData = ref();
const [Modal, modalApi] = useVbenModal({
  showConfirmButton: hasAccessByCodes(['wm:inventory:lock:lock']),
  cancelText: '关闭',
  onConfirm: () => {
    lockFormRef.value?.onSubmit();
  },
  onCancel() {
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      sharedData.value = modalApi.getData<Record<string, any>>();
    }
  },
});
/** 刷新列表 */
const refreshList = () => {
  sharedData.value.refreshList();
  modalApi.close();
};
/** 提交表单 新增 */
const onSubmit = async () => {
  lockFormRef.value!.formApi.validateAndSubmitForm();
};

defineExpose({
  onSubmit,
});
</script>

<template>
  <Modal>
    <LockForm ref="lockFormRef" @submit-success="emit('submitSuccess')" />
  </Modal>
</template>
