import type { VbenFormSchema } from '@girant/adapter';

import type {
  InventoryQueryApi,
  WarehouseInfoApi,
} from '#/api/warehouse-management';

import { h, ref } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
import { ElInputNumber } from 'element-plus';

import { getEnableWarehouseList, getOriginalDocConfigList } from '#/api';
import {
  getInvcDetailByMaterIdAndWareId,
  getInventoryPage,
} from '#/api/warehouse-management';
import RemoteSearchSelect from '#/components/remote-search-select/Index.vue';
/** 当前选择的仓库 */
const warehouseId = ref('');
/** 获取库存分页列表 用来获取仓库下的物料*/
export const fetchInventory = async ({
  keyword,
  pageNum,
  pageSize,
}: {
  keyword: string;
  pageNum: number;
  pageSize: number;
  warehouseIdList: string;
}) => {
  if (!warehouseId.value) {
    return {
      records: [],
    };
  }
  const resData = await getInventoryPage({
    materialName: keyword,
    pageNum,
    pageSize,
    warehouseIdList: warehouseId.value,
    minAvailableInventory: 1,
  });
  resData.records?.filter((item: any) => item.availableInventory > 0);
  resData.records = resData.records?.map((item: any) => {
    item.materialCode = `${item.materialCode}(安全库存：${item.availableInventory})`;
    return item;
  });
  return resData;
};

/** 新增锁库 */
export function useFormSchema(): VbenFormSchema[] {
  /** 物料库存明细*/
  const invcDetail = ref<InventoryQueryApi.InventoryDetailQuery>();
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        filterable: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getEnableWarehouseList();
        },
      },
      dependencies: {
        trigger: (values) => {
          warehouseId.value = values.warehouseId;
          values.materialId = null;
        },
        triggerFields: ['warehouseId'],
      },
      fieldName: 'warehouseId',
      formItemClass: 'col-span-1',
      label: '仓库',
      rules: 'selectRequired',
    },
    {
      component: h(RemoteSearchSelect, {
        fetchMethod: fetchInventory,
        valueKey: 'materialId',
        labelKey: 'materialName',
        subLabelKey: 'materialCode',
        placeholder: '请先选择仓库',
        pageSize: 5,
      }),
      modelPropName: 'modelValue',
      dependencies: {
        trigger: (values) => {
          if (!values.materialId) {
            values.baseUnitLabel = '';
            values.materialSpecs = '';
            invcDetail.value = undefined;
            return;
          }
          // 根据仓库物料查询库存明细
          getInvcDetailByMaterIdAndWareId(
            values.materialId,
            values.warehouseId,
          ).then((res) => {
            invcDetail.value = res;
            values.baseUnitLabel = res.baseUnitLabel;
            values.materialSpecs = res.materialSpecs;
          });
        },
        triggerFields: ['materialId'],
      },
      fieldName: 'materialId',
      label: '物料名称',
      rules: 'required',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue);
      },
      fieldName: 'baseUnitLabel',
      label: '基本单位',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue);
      },
      fieldName: 'materialSpecs',
      label: '规格型号',
    },
    {
      component: (props: any) => {
        return h(
          'div',
          {
            class: 'flex items-center gap-2',
          },
          [
            h(
              ElInputNumber,
              {
                modelValue: props.modelValue,
                'onUpdate:modelValue': (value: number | undefined) => {
                  if (value !== undefined && value !== null) {
                    // 向外部发送更新事件
                    props['onUpdate:modelValue']?.(value);
                  }
                },
                disabled: !invcDetail.value,
                min: 1,
                max: invcDetail.value?.availableInventory,
                precision: 0,
              },
              {},
            ),
            h(
              'div',
              {
                class: `${invcDetail.value ? 'block' : 'hidden'} text-sm ml-4`,
              },
              {
                default: () =>
                  `当前库存量：${invcDetail.value?.inventory || 0},可用量：${invcDetail.value?.availableInventory || 0}`,
              },
            ),
          ],
        );
      },
      fieldName: 'blockQuantity',
      defaultValue: 1,
      formItemClass: 'col-span-full',
      label: '锁库数量',
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: WarehouseInfoApi.OriginalDocConfigList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.docName,
            value: item.docCode,
          }));
          return warehouseList;
        },
        api: () => {
          return getOriginalDocConfigList();
        },
        clearable: true,
        filterable: true,
      },
      fieldName: 'docTypeCode',
      label: '关联单据标识',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values) {
          if (values.docTypeCode) {
            return 'required';
          }
          return null;
        },
        triggerFields: ['docTypeCode'],
      },
      fieldName: 'docNumber',
      label: '关联单据编号',
    },
    {
      component: 'Input',
      fieldName: 'remarkOptionList',
      label: '锁库原因',
      rules: 'selectRequired',
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '锁库原因说明',
      formItemClass: 'col-span-full items-start',
      componentProps: {
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
    },
    {
      component: h(UploadFiles, {
        mode: 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
        class: 'w-full',
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      label: '附件',
      formItemClass: 'col-span-full items-start',
    },
  ];
}
