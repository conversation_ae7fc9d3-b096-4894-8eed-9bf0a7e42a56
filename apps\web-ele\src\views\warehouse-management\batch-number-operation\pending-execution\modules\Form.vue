<script setup lang="ts">
import { ref } from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';

import { execBatchnumDoc } from '#/api/warehouse-management';

import Form from '../../components/FormView.vue';

const props = defineProps({
  batchnumDocId: {
    type: String,
    default: '',
  },
  batchnumDocNumber: {
    type: String,
    default: '',
  },
  docCode: {
    type: String,
    default: '',
  },
});

const emits = defineEmits([
  'handleCancel',
  'execSuccess',
  'batchNumberLoading',
]);

const handleCancel = () => {
  emits('handleCancel');
};

const FormRef = ref();

const handleExec = async () => {
  if (
    await ElMessageBox.confirm('确定执行吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    try {
      emits('batchNumberLoading', true);
      await execBatchnumDoc(props.batchnumDocId);
      ElMessage.success('执行成功');
      emits('execSuccess');
    } catch {
      emits('batchNumberLoading', false);
      ElMessage.error('执行失败');
    }
  }
};
</script>

<template>
  <div class="relative mb-12 h-full">
    <Form
      ref="FormRef"
      :batchnum-doc-id="batchnumDocId"
      :batchnum-doc-number="batchnumDocNumber"
      :doc-code="docCode"
    />

    <div
      class="fixed bottom-0 right-0 z-10 h-[50px] w-full rounded-b-lg border-t bg-white p-2 text-right"
    >
      <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
      <ElButton
        type="primary"
        @click="handleExec"
        v-access:code="'wm:batchnum:exec'"
      >
        执行
      </ElButton>
    </div>
  </div>
</template>
