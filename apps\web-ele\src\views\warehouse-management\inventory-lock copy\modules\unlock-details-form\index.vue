<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getInvcBlock } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  /** 库存锁库id */
  blockId: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
/** 锁库信息表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 xl:grid-cols-3',
});
/** 根据库存锁库id获取库存锁库详细信息 */
const getMaterialInfo = async (blockId: string) => {
  try {
    loading.value = true;
    const res = await getInvcBlock(blockId);
    formApi.setValues(res);
  } catch {
    ElMessage.error('获取库存锁库详细信息失败');
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  if (props.blockId) {
    getMaterialInfo(props.blockId);
  }
});
defineExpose({
  Form,
  formApi,
});
</script>
<template>
  <FormCard title="解锁信息" :is-footer="false">
    <Form v-loading="loading" />
  </FormCard>
</template>
