<script setup lang="ts">
import { ref } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { useFormSchema } from './data';

/** 附件*/
const uploadSerialNumberRef = ref();

/** 表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' }, labelWidth: 60 },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});

const filesSubmitSuccess = (field: string, res: any) => {
  formApi.setFieldValue(field, res.serialNumber);
};

const loadMoreInfoData = (data: any) => {
  formApi.setValues(data);
};

// 获取表单数据
const getFormData = async () => {
  const isCompleted_SerialNumber = await uploadSerialNumberRef.value.isComplete;
  if (!isCompleted_SerialNumber) {
    ElMessage.error('等待附件上传完成');
    return false;
  }
  const formValues = await formApi.getValues();
  return formValues;
};

defineExpose({
  formApi,
  getFormData,
  loadMoreInfoData,
});
</script>

<template>
  <Form>
    <template #serialNumber="row">
      <div class="w-full">
        <UploadFiles
          ref="uploadSerialNumberRef"
          mode="editMode"
          :show-operat-button="false"
          :show-table="uploadSerialNumberRef?.fileList?.length > 0"
          :show-thumbnail="false"
          :auto-upload="true"
          :serial-number="row.value"
          @files-submit-success="filesSubmitSuccess('serialNumber', $event)"
        >
          <template #trigger>
            <div class="text-center">
              <i class="icon-[bx--folder] mx-auto h-12 w-12"></i>
              <p class="mt-2">点击或将文件拖拽到这里上传</p>
              <p class="text-xs text-gray-500">
                支持格式：.rar .zip .doc .docx .pdf .jpg...
              </p>
            </div>
          </template>
        </UploadFiles>
      </div>
    </template>
  </Form>
</template>
