<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { InventoryAdjustment } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElDatePicker,
  ElMessage,
  ElTag,
  ElTooltip,
} from 'element-plus';

import {
  exportInvcAdjustItem,
  getInvcAdjustItemPage,
} from '#/api/warehouse-management';

import Form from '../modules/Form.vue';
import {
  confirmExecute,
  createDisabledDate,
  docStatusDict,
  openModal,
} from '../modules/method';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});
const formRef = ref<InstanceType<typeof Form>>();
const isView = ref(false);
/** 库存调整单据id */
const invcAdjustDocId = ref('');
/** 库存调整单据编号 */
const invcAdjustDocNumber = ref('');
const exportLoading = ref(false);
/** 是否显示暂存 */
const isShowSave = ref(false);
/** 当前单据状态 */
const docStatus = ref('');
/** 审核流程实例ID */
const processInstanceId = ref('');
/** 提交时间 */
const submitTime = ref({
  startTime: props.params?.submitStartTime,
  endTime: props.params?.submitEndTime,
});

/** 执行时间 */
const execTime = ref({
  startTime: props.params?.execStartTime,
  endTime: props.params?.execEndTime,
});
/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    submitTime.value = {
      startTime: '',
      endTime: '',
    };
    execTime.value = {
      startTime: '',
      endTime: '',
    };
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};
/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onConfirm: () => {
    formRef.value?.onSubmit();
  },
  cancelText: '关闭',
  showCancelButton: true,
  showConfirmButton: true,
});
// 表格
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    handleReset,
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || true,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getInvcAdjustItemPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            ...submitTime.value,
            ...execTime.value,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },

    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<InventoryAdjustment.InvcAdjustItemPage>,
});
/** 导出 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportInvcAdjustItem({
      ...formValues,
      submitStartTime: submitTime.value.startTime,
      submitEndTime: submitTime.value.endTime,
      execStartTime: execTime.value.startTime,
      execEndTime: execTime.value.endTime,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};
/** 查看 */
const onView = (row: any) => {
  isView.value = true;
  docStatus.value = row.docStatus;
  invcAdjustDocId.value = row.invcAdjustDocId;
  invcAdjustDocNumber.value = row.invcAdjustDocNumber;
  isShowSave.value = false;
  processInstanceId.value = row.processInstanceId;
  openModal(formModalApi, false, '调整单据详情');
};
/** 新增 */
const onAdd = () => {
  isView.value = false;
  invcAdjustDocId.value = '';
  invcAdjustDocNumber.value = '';
  docStatus.value = '';
  isShowSave.value = true;
  openModal(formModalApi, true, '新增');
};

/** 提交成功 */
const submitSuccess = () => {
  // 重新获取数据
  gridApi.query();
  formModalApi.close();
};
onMounted(async () => {
  await gridApi.formApi.setValues({
    invcAdjustDocNumberList:
      props.params?.invcAdjustDocNumberList?.split(',') || [],
    materialCodeList: props.params?.materialCodeList?.split(',') || [],
    submitUserList: props.params?.submitUserList?.split(',') || [],
    executorUserList: props.params?.executorUserList?.split(',') || [],
    docStatusList: props.params?.docStatusList?.split(',') || [],
    warehouseIdList: props.params?.warehouseIdList?.split(',') || [],
    locationIdList: props.params?.locationIdList?.split(',') || [],
  });
});
defineExpose({
  Grid,
  gridApi,
  formModalApi,
  FormModal,
  formRef,
});
</script>
<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <FormModal class="h-full w-10/12">
      <Form
        ref="formRef"
        :is-view="isView"
        @submit-success="submitSuccess"
        :invc-adjust-doc-id="invcAdjustDocId"
        :doc-status="docStatus"
        :process-instance-id="processInstanceId"
        :invc-adjust-doc-number="invcAdjustDocNumber"
      />
      <template #center-footer>
        <ElButton v-if="isShowSave" type="primary" @click="formRef?.onSave()">
          暂存
        </ElButton>
        <ElButton
          v-if="docStatus === '30'"
          type="primary"
          @click="confirmExecute(invcAdjustDocId, formModalApi, gridApi)"
        >
          确认执行
        </ElButton>
      </template>
    </FormModal>
    <Grid>
      <template #toolbar-actions>
        <ElButton type="primary" @click="onAdd"> 新增 </ElButton>
      </template>
      <template #form-submitTime>
        <ElDatePicker
          v-model="submitTime.startTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="createDisabledDate(false, submitTime)"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="submitTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="createDisabledDate(true, submitTime)"
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #form-execTime>
        <ElDatePicker
          v-model="execTime.startTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="createDisabledDate(false, execTime)"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="execTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="createDisabledDate(true, execTime)"
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #docStatusLabel="{ row }">
        <ElTag size="small" :type="docStatusDict[row.docStatus]">
          {{ row.docStatusLabel }}
        </ElTag>
      </template>
      <template #quantity="{ row }">
        <span :class="row.quantity > 0 ? 'text-lime-500' : 'text-red-500'">
          <span v-text="row.quantity > 0 ? '+' : ''"></span>
          <span>{{ row.quantity }}</span>
        </span>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton :loading="exportLoading" circle @click="exportHandle">
            <template #icon>
              <IconFont name="xiazai" />
            </template>
          </ElButton>
        </ElTooltip>
      </template>
      <template #CellOperation="{ row }">
        <ElButton link size="small" @click="onView(row)" type="info">
          查看
        </ElButton>
        <ElButton
          link
          size="small"
          type="primary"
          v-if="row.docStatus === '30'"
          @click="confirmExecute(row.invcAdjustDocId, formModalApi, gridApi)"
        >
          确认执行
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
