<script lang="ts">
import { computed, defineComponent, onMounted, ref } from 'vue';

import { CountTo } from '@vben/common-ui';

import { getSafetyStockWarnNum } from '#/api/warehouse-management';
import PanelCard from '#/components/panel-card/Index.vue';
import { WS } from '#/utils/socket/common-socketio';

export default defineComponent({
  components: { PanelCard, CountTo },
  props: {
    activeId: {
      type: String,
      default: '',
    },
  },
  emits: ['cardClick'],
  setup(props, { emit }) {
    const wsType = ['wm.inventory.safetyStockWarning'];

    const isActive = computed(() => props.activeId === 'inventory-alert');

    const loading = ref(true);
    const safetyInventoryNum = ref(0); // 数量

    const fetchSafetyStockData = async () => {
      try {
        const response = await getSafetyStockWarnNum();
        safetyInventoryNum.value = response.safetyInventoryNum || 0;
      } catch (error) {
        console.error('获取库存预警数据失败:', error);
      } finally {
        loading.value = false;
      }
    };

    onMounted(() => {
      WS.on(wsType, fetchSafetyStockData);
      fetchSafetyStockData();
    });

    const handleCardClick = () => {
      emit('cardClick', {
        name: 'inventory-alert',
        attrs: {
          wrapperClass:
            'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2',
        },
      });
    };

    return {
      isActive,
      safetyInventoryNum,
      handleCardClick,
      loading,
    };
  },
});
</script>

<template>
  <PanelCard title="库存预警">
    <template #default>
      <div
        class="hover:bg-primary-100 bg-primary-50 cursor-pointer rounded-lg p-2 text-center text-black"
        @click="handleCardClick"
      >
        <div class="flex h-6 items-end justify-center">
          <span class="text-primary mx-1 mb-[-4px] text-2xl font-bold">
            <CountTo
              v-loading="loading"
              :start-val="0"
              :end-val="safetyInventoryNum"
              :duration="1500"
              separator=""
            />
          </span>
          <span class="-mb-0.5 text-sm">项物料低于安全库存</span>
        </div>
      </div>
    </template>
  </PanelCard>
</template>
