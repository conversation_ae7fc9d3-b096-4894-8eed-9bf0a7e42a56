import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'inBoundDocNumber',
      label: '入库单号',
      formItemClass: 'col-span-3',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'origDocNumber',
      label: '转入申请单',
      formItemClass: 'col-span-2',
    },
    {
      component: 'Input',
      fieldName: 'applyUserName',
      label: '申请人',
      formItemClass: 'col-span-2',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'applyTime',
      label: '提交时间',
      formItemClass: 'col-span-2',
    },

    {
      component: 'Input',
      fieldName: 'docProcess',
      label: '单据流程',
      formItemClass: 'col-span-full',
    },
  ];
}
