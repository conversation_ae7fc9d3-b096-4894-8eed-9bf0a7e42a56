<script setup lang="ts">
import type { InBoundDocApi } from '#/api/warehouse-management/index';

import { computed, onMounted, ref } from 'vue';

import { ElMessage } from 'element-plus';

import { getInBoundDocDetail } from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';
import TriangleCard from '#/components/triangle-card/Index.vue';
import MaterialsItem from '#/views/warehouse-management/inbound-management/components/materials-item/index.vue';

const props = defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
});

const inBoundData = ref<InBoundDocApi.InBoundDocDetail>(
  {} as InBoundDocApi.InBoundDocDetail,
);

const itemList = computed(() => inBoundData.value.inBoundItemList);
const docStatus = computed(() => inBoundData.value.docStatus);

/** 获取数据 */
const getInBoundDocDetailHandle = async () => {
  try {
    const inBoundRes = await getInBoundDocDetail({
      inBoundDocId: props.inBoundDocId,
      inBoundDocNumber: props.inBoundDocNumber,
      isQueryItem: true,
    });
    inBoundData.value = inBoundRes;
    return inBoundRes;
  } catch {
    ElMessage.error('获取入库单据失败');
  }
};

onMounted(async () => {
  if (props.inBoundDocId && props.inBoundDocNumber) {
    const inBoundRes = await getInBoundDocDetailHandle();
    if (inBoundRes) {
      inBoundData.value = inBoundRes;
    }
  }
});
</script>

<template>
  <FormCard :is-footer="false">
    <template #title>
      <span>入库明细</span>
    </template>

    <template #titleMore>
      <span class="text-sm text-gray-500">
        共
        <span class="text-primary-500">
          {{ inBoundData?.inBoundItemList?.length || 0 }}
        </span>
        种物料
      </span>
    </template>
    <template #default>
      <template v-for="(item, index) in itemList" :key="item.materialId">
        <TriangleCard :number="index + 1" title="" class="mb-5">
          <template #content>
            <MaterialsItem :material-item-data="item" :doc-status="docStatus" />
          </template>
        </TriangleCard>
      </template>
    </template>
  </FormCard>
</template>
