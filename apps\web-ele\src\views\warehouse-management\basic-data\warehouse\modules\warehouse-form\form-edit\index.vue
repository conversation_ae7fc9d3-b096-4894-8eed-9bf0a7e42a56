<script setup lang="ts">
import type { mapType } from './data';

import type { WarehouseInfoApi } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import {
  ElCheckbox,
  ElCheckboxGroup,
  ElMessage,
  ElMessageBox,
  ElRadio,
  ElRadioGroup,
} from 'element-plus';

import { getDictItemList, getEnumByName } from '#/api/common';
import {
  getOriginalDocConfigList,
  getWarehouseDetail,
  getWarehouseRestriction,
  modWarehouse,
  restrictionModWareRest,
  saveWarehouse,
} from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import { useFormSchema, useFormSchema2 } from './data';

const props = defineProps({
  warehouseId: {
    type: String,
    default: '',
  },
  /** 仓库编码 */
  warehouseCode: {
    type: String,
    default: '',
  },
  viewBtn: {
    type: Boolean,
    default: true,
  },
});
const emits = defineEmits(['formSubmitSuccess', 'cancel']);
/** 仓库详细信息 */
const warehouseInfo = ref<WarehouseInfoApi.WarehouseDetail>();
/** 物料库存策略枚举 */
const wmInOutStrategyEnums = ref<mapType[]>([]);
/** 操作限制单据类型 */
const limitDocTypeEnums = ref<mapType[]>([]);
/** 物料类型限制 */
const baseMaterialTypeList = ref<mapType[]>([]);
const loading = ref(false);
/** 选择的操作单据列表 */
const limitDocsList = ref<string[]>([]);
/** 全部操作单据列表 */
const originalDocConfigList = ref<
  Map<string, WarehouseInfoApi.OriginalDocConfigList[]>
>(new Map());
/** 校验表单 */
const validateForm = async () => {
  // 校验表单
  const [verification, verification2] = await Promise.all([
    formApi.validate(),
    RestrictionFFormApi.validate(),
  ]);
  if (!verification.valid || !verification2.valid) {
    ElMessage.error('请填写完整表单');
    return false;
  }
  return true;
};

/** 提交表单 基础资料表单 编辑*/
const onSubmitWarehouse = async (values: Record<string, any>) => {
  try {
    await modWarehouse({
      ...values,
      warehouseId: props.warehouseId,
      managerUserId: values.managerUserId?.staffId || values.managerUserId,
    });
    ElMessage.success('基础资料表单提交成功');
  } catch (error) {
    console.error(error);
    ElMessage.error('基础资料表单提交失败');
  }
};
/** 提交表单 仓库策略表单 编辑*/
const onSubmitRestriction = async (values: Record<string, any>) => {
  try {
    await restrictionModWareRest({
      ...values,
      warehouseId: props.warehouseId,
      limitDocs: limitDocsList.value,
      isSafetyStockWarn:
        values.warehouseStrategy?.includes('isSafetyStockWarn'),
      isObsoleteAnalysis:
        values.warehouseStrategy?.includes('isObsoleteAnalysis'),
    });
    ElMessage.success('仓库策略表单提交成功');
  } catch {
    ElMessage.error('仓库策略表单提交失败');
  }
};
/** 提交表单 */
const submit = async () => {
  // 判断是否有id 有id则是编辑 否则是新增
  if (props.warehouseId || props.warehouseCode) {
    onSubmitEdit();
  } else {
    onSubmit();
  }
};
/** 提交表单 添加时*/
const onSubmit = async () => {
  try {
    // 校验表单
    if (!(await validateForm())) return;
    await ElMessageBox.confirm('确认提交新增吗？', '提示', {
      type: 'warning',
    });
    loading.value = true;
    // 获取基础资料和仓库策略表单数据
    const [formData, restrictionData] = await Promise.all([
      formApi.getValues(),
      RestrictionFFormApi.getValues(),
    ]);
    // 提交数据
    const res = await saveWarehouse({
      ...formData,
      ...restrictionData,
      limitDocs:
        restrictionData.limitDocType === '00' ? [] : limitDocsList.value,
      isSafetyStockWarn:
        restrictionData.warehouseStrategy?.includes('isSafetyStockWarn'),
      isObsoleteAnalysis:
        restrictionData.warehouseStrategy?.includes('isObsoleteAnalysis'),
    });
    ElMessage.success('提交成功');
    emits('formSubmitSuccess', res.warehouseId, res.warehouseCode);
  } catch (error) {
    console.error(error);
    // ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};

/** 提交表单 编辑时 */
const onSubmitEdit = async () => {
  try {
    // 校验表单
    if (!(await validateForm())) return;
    // 获取基础资料表单数据
    const formData = await formApi.getValues();
    const text =
      formData.isEnable === true
        ? '确认提交编辑吗？'
        : '确认提交编辑吗？ 当前选择的仓库状态为停用,仓库策略表单将不会提交。';
    await ElMessageBox.confirm(text, '提示', {
      type: 'warning',
    });
    loading.value = true;
    // 提交表单
    await formApi.validateAndSubmitForm();
    if (formData.isEnable === true) {
      await RestrictionFFormApi.validateAndSubmitForm();
    }
    emits('formSubmitSuccess');
  } catch {
    ElMessage.error('提交编辑失败');
  } finally {
    loading.value = false;
  }
};

/** 基础资料表单 */
const [Form, formApi] = useVbenForm({
  // 提交函数
  handleSubmit: onSubmitWarehouse,
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(!!props.warehouseId || !!props.warehouseCode),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});

/** 仓库策略表单 */
const [RestrictionForm, RestrictionFFormApi] = useVbenForm({
  commonConfig: {
    componentProps: { class: 'w-full' },
    labelWidth: 125,
    formItemClass: 'col-span-full',
  },
  wrapperClass: 'grid-cols-1',
  handleSubmit: onSubmitRestriction,
  schema: useFormSchema2(),
  showDefaultActions: false,
});

/** 获取数据 */
const getData = async () => {
  try {
    loading.value = true;
    // 获取物料库存策略枚举 和 操作限制单据类型枚举 //操作限制单据列表 //物料类型限制
    const [
      resWmInOutStrategyEnums,
      resLimitDocTypeEnums,
      ResoriginalDocConfigList,
      resBaseMaterialType,
    ] = await Promise.all([
      getEnumByName('WmInOutStrategyEnums'),
      getEnumByName('WmLimitDocTypeEnums'),
      getOriginalDocConfigList(),
      getDictItemList('baseMaterialType'),
    ]);
    // 使用 Map 按照originalDocTypeLabel分组数据
    const map = new Map<string, WarehouseInfoApi.OriginalDocConfigList[]>();
    ResoriginalDocConfigList?.forEach((doc) => {
      const type = doc.originalDocTypeLabel;
      if (!map.has(type)) {
        map.set(type, []);
      }
      map.get(type)!.push(doc);
    });
    originalDocConfigList.value = map;
    wmInOutStrategyEnums.value = resWmInOutStrategyEnums?.map((item) => {
      return {
        label: item.enumLabel,
        value: item.enumValue,
      };
    });
    limitDocTypeEnums.value = resLimitDocTypeEnums?.map((item) => {
      return {
        label: item.enumLabel,
        value: item.enumValue,
      };
    });
    baseMaterialTypeList.value = resBaseMaterialType?.map((item) => {
      return {
        label: item.dictLabel,
        value: item.dictValue,
      };
    });
    // 检查是否有id
    if (props.warehouseId || props.warehouseCode) {
      // 获取仓库详细信息 获取仓库策略信息
      const [resWarehouse, resRestriction] = await Promise.all([
        getWarehouseDetail(props.warehouseId, props.warehouseCode),
        getWarehouseRestriction(props.warehouseId, props.warehouseCode),
      ]);
      // 物料类型限制处理
      const limitMaterialTypes: string[] = [];
      resRestriction?.limitMaterialTypeList?.forEach((item: any) => {
        limitMaterialTypes.push(item.limitMaterialTypes);
      });
      // 操作限制单据列表处理
      limitDocsList.value = resRestriction?.limitDocs;
      warehouseInfo.value = resWarehouse;
      // 填充表单数据
      if (resWarehouse?.managerUserId) {
        formApi.setValues({
          ...resWarehouse,
        });
      } else {
        formApi.setValues({
          ...resWarehouse,
        });
      }

      RestrictionFFormApi.setValues({
        ...resRestriction,
        limitMaterialTypes,
        warehouseStrategy: [
          resRestriction.isSafetyStockWarn ? 'isSafetyStockWarn' : '',
          resRestriction.isObsoleteAnalysis ? 'isObsoleteAnalysis' : '',
        ],
      });
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('数据获取失败');
  } finally {
    loading.value = false;
  }
};

/** 清空表单 */
const clearForm = () => {
  formApi.resetForm();
  RestrictionFFormApi.resetForm();
  if (warehouseInfo.value?.warehouseId) {
    emits('cancel', warehouseInfo.value?.warehouseId);
  }
};
onMounted(() => {
  getData();
});
defineExpose({
  Form,
  formApi,
  RestrictionForm,
  RestrictionFFormApi,
  getData,
  onSubmit,
  clearForm,
  onSubmitEdit,
  validateForm,
  submit,
});
</script>

<template>
  <div class="h-full w-full">
    <ElScrollbar noresize>
      <div v-loading="loading">
        <FormCard :is-footer="false">
          <template #title>
            <span>基础资料</span>
          </template>
          <template #default>
            <Form />
          </template>
        </FormCard>
        <FormCard :is-footer="false">
          <template #title>
            <span>仓库策略</span>
          </template>
          <template #default>
            <RestrictionForm>
              <!-- 物料类型限制 -->
              <template #limitMaterialTypes="slotProps">
                <b
                  v-if="baseMaterialTypeList.length === 0"
                  class="w-[100px] text-[14px]"
                >
                  暂无数据
                </b>
                <ElCheckboxGroup v-bind="slotProps">
                  <ElCheckbox
                    v-for="item in baseMaterialTypeList"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </ElCheckbox>
                </ElCheckboxGroup>
              </template>
              <!-- 物料库存策略-->
              <template #inOutStrategy="slotProps">
                <b
                  v-if="wmInOutStrategyEnums.length === 0"
                  class="w-[100px] text-[14px]"
                >
                  暂无数据
                </b>
                <ElRadioGroup v-bind="slotProps">
                  <ElRadio
                    v-for="item in wmInOutStrategyEnums"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </ElRadio>
                </ElRadioGroup>
              </template>
              <!-- 操作限制单据类型 -->
              <template #limitDocType="slotProps">
                <div class="flex items-center">
                  <b
                    v-if="limitDocTypeEnums.length === 0"
                    class="w-[130px] text-[14px]"
                  >
                    暂无数据
                  </b>
                  <ElRadioGroup v-bind="slotProps">
                    <ElRadio
                      v-for="item in limitDocTypeEnums"
                      :key="item.value"
                      :value="item.value"
                      @change="slotProps.setValue(item.value)"
                    >
                      {{ item.label }}
                    </ElRadio>
                  </ElRadioGroup>
                  <b
                    v-if="limitDocTypeEnums.length > 0"
                    class="w-[150px] text-[14px]"
                  >
                    {{
                      slotProps.value === '00' ? '单据出入库' : '以下单据出入库'
                    }}
                  </b>
                </div>
              </template>
              <!-- 操作限制单据 -->
              <template #limitDocs>
                <div>
                  <b
                    v-if="originalDocConfigList.size === 0"
                    class="text-[14px]"
                  >
                    暂无数据
                  </b>
                  <div
                    v-else
                    v-for="[groupName, docs] of originalDocConfigList"
                    :key="groupName"
                    class="flex items-baseline text-[14px] font-bold"
                  >
                    <span
                      class="w-[50px] shrink-0"
                      style="text-align-last: justify"
                    >
                      {{ groupName }}
                    </span>
                    <span class="mr-[10px]">:</span>
                    <ElCheckboxGroup v-model="limitDocsList">
                      <ElCheckbox
                        v-for="doc in docs"
                        :key="doc.docCode"
                        :value="doc.docCode"
                        :label="doc.docCode"
                        class="!mr-[10px]"
                      >
                        <div :title="doc.docName" class="w-[120px] truncate">
                          {{ doc.docName }}
                        </div>
                      </ElCheckbox>
                    </ElCheckboxGroup>
                  </div>
                </div>
              </template>
            </RestrictionForm>
          </template>
        </FormCard>
      </div>
    </ElScrollbar>
    <div class="flex min-h-[40px] justify-end" v-if="props.viewBtn">
      <ElButton type="info" @click="clearForm"> 取消 </ElButton>
      <ElButton
        type="primary"
        @click="submit"
        v-if="props.warehouseId || props.warehouseCode"
        v-access:code="'wm:warehouse:edit:mod'"
      >
        提交编辑
      </ElButton>
      <ElButton
        type="primary"
        @click="submit"
        v-if="!props.warehouseId && !props.warehouseCode"
        v-access:code="'wm:warehouse:edit:add'"
      >
        提交新增
      </ElButton>
    </div>
  </div>
</template>
