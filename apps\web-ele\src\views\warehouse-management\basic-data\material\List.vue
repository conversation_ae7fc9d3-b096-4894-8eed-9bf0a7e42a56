<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';
import type { UploadRequestOptions } from 'element-plus';

import type { materialConfig } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElInput,
  ElMessage,
  ElTag,
  ElTooltip,
  ElUpload,
} from 'element-plus';

import {
  exportMaterialConfig,
  exportMaterialConfigTemplate,
  getMaterialConfigPage,
  importMaterialConfig,
} from '#/api/warehouse-management';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/Form.vue';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});
/** 分页数据 */
const MaterialConfigPage = ref<materialConfig.materialPageRecords[]>([]);
/** 物料id */
const materialId = ref<string>('');
/** 表单ref */
const formRef = ref<InstanceType<typeof Form>>();
const isView = ref(false);
const exportLoading = ref(false);
/** 选择的物料配置 */
const materialConfigData = ref<materialConfig.materialPageRecords>();
/** 安全库存范围 */
const safetyInventory = ref({
  minSafetyInventory: props.params?.minSafetyInventory,
  maxSafetyInventory: props.params?.maxSafetyInventory,
});
/** 呆滞期范围 */
const obsoletePeriod = ref({
  minObsoletePeriod: props.params?.minObsoletePeriod,
  maxObsoletePeriod: props.params?.maxObsoletePeriod,
});
/** 操作 */
const onActionClick = (e: any) => {
  switch (e.code) {
    case 'edit': {
      onAction(e.row, false, '编辑');
      break;
    }
    case 'view': {
      onAction(e.row, true, '查看');
      break;
    }
  }
};
/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    safetyInventory.value = {
      minSafetyInventory: '',
      maxSafetyInventory: '',
    };
    obsoletePeriod.value = {
      minObsoletePeriod: '',
      maxObsoletePeriod: '',
    };
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};
// 表格
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
    commonConfig: {
      labelClass: 'min-w-[70px]',
    },
    showCollapseButton: props.attr?.showCollapseButton || true,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    handleReset,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getMaterialConfigPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            ...safetyInventory.value,
            ...obsoletePeriod.value,
          });
          MaterialConfigPage.value = res.records;
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      keyField: 'materialCode',
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<materialConfig.materialPage>,
});
/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onConfirm: () => {
    formRef.value?.onSubmit();
  },
  confirmText: '提交',
  showCancelButton: true,
  showConfirmButton: true,
});

/**
 * 表格操作
 * @param row row
 * @param view 是否是查看状态
 * @param title 标题
 */
const onAction = (
  row: materialConfig.materialPageRecords,
  view: boolean = false,
  title: string = '操作',
) => {
  materialId.value = row.materialId;
  isView.value = view;
  materialConfigData.value = row;
  formModalApi
    .setState({
      showConfirmButton: !view,
      title,
    })
    .open();
};

/** 下载模板 */
async function getMaterialConfigTemplate() {
  try {
    const response = await exportMaterialConfigTemplate();
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch (error) {
    console.error('物料库存配置模板下载失败：', error);
  }
}

/** 数据导入*/
async function importMaterialConfigHandle(options: UploadRequestOptions) {
  const { file, onSuccess } = options;
  try {
    const data = { file };
    const response = await importMaterialConfig(data);
    gridApi.query();
    onSuccess(response);
    ElMessage.success('数据导入成功');
  } catch {
    ElMessage.error('数据导入失败');
  }
}

/** 导出 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportMaterialConfig({
      ...formValues,
      ...safetyInventory.value,
      ...obsoletePeriod.value,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch (error) {
    console.error('数据导出失败:', error);
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};
/** 提交成功 */
const submitSuccess = () => {
  // 重新获取数据
  gridApi.query();
  formModalApi.close();
};
onMounted(async () => {
  await gridApi.formApi.setValues({
    materialCodeList: props.params?.materialCodeList?.split(',') || [],
    warehouseIdList: props.params?.warehouseIdList?.split(',') || [],
    locationIdList: props.params?.locationIdList?.split(',') || [],
    safetyInvcWarehouseIdList:
      props.params?.safetyInvcWarehouseIdList?.split(',') || [],
    slowMovWarehouseIdList:
      props.params?.slowMovWarehouseIdList?.split(',') || [],
  });
});
defineExpose({
  gridApi,
  Grid,
  FormModal,
  formModalApi,
});
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <FormModal class="h-full w-9/12">
      <Form
        ref="formRef"
        :is-view="isView"
        :material-id="materialId"
        :material-config-data="materialConfigData"
        @submit-success="submitSuccess"
      />
    </FormModal>
    <!-- 查询表单 -->
    <Grid>
      <template #form-safetyInventory>
        <ElInput
          v-model="safetyInventory.minSafetyInventory"
          type="number"
          placeholder="请输入最小值"
          clearable
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElInput
          v-model="safetyInventory.maxSafetyInventory"
          type="number"
          placeholder="请输入最大值"
          clearable
        />
      </template>
      <template #form-obsoletePeriod>
        <ElInput
          v-model="obsoletePeriod.minObsoletePeriod"
          type="number"
          placeholder="请输入最小值"
          clearable
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElInput
          v-model="obsoletePeriod.maxObsoletePeriod"
          type="number"
          placeholder="请输入最大值"
          clearable
        />
      </template>
      <template #warehouseName="{ row }">
        {{ row.warehouseName }}/{{ row.locationName }}
      </template>
      <template #safetyInventory="{ row }">
        <div
          v-for="item in row.safetyInventoryWarnList"
          :key="item.invcConfigId"
          class="mb-[10px]"
        >
          <div v-if="item.safetyInventory" class="flex flex-wrap">
            <b>{{ item.warehouseName }}:</b>
            <ElTag
              size="small"
              :type="item.isSafetyInventoryWarn ? 'success' : 'danger'"
            >
              {{ item.isSafetyInventoryWarn ? '参与预警' : '不参与预警' }}
            </ElTag>
          </div>
          <span v-if="item.safetyInventory">
            库存数量:<span class="text-primary ml-[5px]">{{
              item.safetyInventory || 0
            }}</span>
          </span>
        </div>
      </template>
      <template #obsoletePeriod="{ row }">
        <div
          v-for="item in row.safetyInventoryWarnList"
          :key="item.invcConfigId"
          class="mb-[10px]"
        >
          <div v-if="item.obsoletePeriod" class="flex flex-wrap">
            <b>{{ item.warehouseName }}:</b>
            <ElTag
              size="small"
              :type="item.isSlowMovingAnalysis ? 'success' : 'danger'"
            >
              {{
                item.isSlowMovingAnalysis ? '参与呆滞分析' : '不参与呆滞分析'
              }}
            </ElTag>
          </div>
          <span v-if="item.obsoletePeriod">
            呆滞期:<span class="text-primary-500 ml-[5px]">{{
              item.obsoletePeriod || 0
            }}</span>
          </span>
        </div>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="下载导入模板"
          placement="top-start"
        >
          <ElButton circle class="mr-2" @click="getMaterialConfigTemplate">
            <template #icon><span class="iconfont">&#xe678;</span></template>
          </ElButton>
        </ElTooltip>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导入数据"
          placement="top-start"
        >
          <ElUpload
            :show-file-list="false"
            :http-request="importMaterialConfigHandle"
            accept=".xlsx"
          >
            <ElButton
              circle
              class="mr-2"
              v-access:code="'wm:material:config:import'"
            >
              <template #icon>
                <span class="iconfont">&#xe621;</span>
              </template>
            </ElButton>
          </ElUpload>
        </ElTooltip>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            :loading="exportLoading"
            circle
            @click="exportHandle"
            v-access:code="'wm:material:config:export'"
          >
            <template #icon>
              <IconFont name="xiazai" class="iconfont" />
            </template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
