import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

export function useFormSchema(
  isShowInOutCancelDocNumber: boolean,
): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'inBoundDocNumber',
      label: '入库单号',
      formItemClass: 'col-span-3',
    },
    {
      component: 'Input',
      fieldName: 'docStatus',
      label: '入库状态值',
      formItemClass: 'hidden',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'origDocNumber',
      label: '转入申请单',
      formItemClass: 'col-span-2',
    },
    {
      component: 'Input',
      fieldName: 'applyUserName',
      label: '申请人',
      formItemClass: 'col-span-2',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'applyTime',
      label: '提交时间',
      formItemClass: 'col-span-2',
    },

    ...(isShowInOutCancelDocNumber
      ? [
          {
            component: 'Input',
            fieldName: 'inOutCancelDocNumber',
            label: '取消申请单',
            formItemClass: 'col-span-3',
          },
        ]
      : []),

    {
      component: 'Input',
      fieldName: 'closeUserName',
      label: '关闭人',
      formItemClass: 'col-span-2',
      dependencies: {
        triggerFields: ['docStatus'],
        if: (values) => values.docStatus === 'closed',
      },
    },

    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'closeTime',
      label: '关闭时间',
      formItemClass: 'col-span-2',
      dependencies: {
        triggerFields: ['docStatus'],
        if: (values) => values.docStatus === 'closed',
      },
    },

    {
      component: 'Input',
      fieldName: 'docProcess',
      label: '单据流程',
      formItemClass: 'col-span-full',
    },
  ];
}
