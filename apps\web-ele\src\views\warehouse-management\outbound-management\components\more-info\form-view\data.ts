import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

/** 表单 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        const val = props.modelValue;
        const showText = val || '/';
        return h('div', null, showText);
      },
      fieldName: 'remark',
      formItemClass: 'mr-6',
      label: '备注',
    },
    {
      component: 'Upload',
      fieldName: 'serialNumber',
      formItemClass: 'mr-6',
      label: '附件',
    },
  ];
}
