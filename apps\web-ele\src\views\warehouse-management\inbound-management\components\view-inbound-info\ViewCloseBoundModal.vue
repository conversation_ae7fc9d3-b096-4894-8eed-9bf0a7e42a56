<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import CloseBoundInfo from '../close-bound/form-view/index.vue';

/** 共享数据 */
const data = ref();

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  onClosed() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
    }
  },
});
</script>

<template>
  <Modal>
    <CloseBoundInfo
      :in-out-cancel-doc-id="data.inOutCancelDocId"
      :in-out-cancel-doc-number="data.inOutCancelDocNumber"
    />
  </Modal>
</template>
