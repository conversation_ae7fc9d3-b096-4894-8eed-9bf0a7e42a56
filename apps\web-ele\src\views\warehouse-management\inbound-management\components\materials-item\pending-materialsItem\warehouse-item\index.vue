<script setup lang="ts">
import type { VbenFormProps } from '@girant/adapter';

import type { PropType } from 'vue';

import type {
  LocationItemType,
  SelectWarehouseListType,
  WarehouseItemDataType,
} from '../index.vue';

import { computed, h, nextTick, ref, watch } from 'vue';

import { DynamicForm } from '@girant-web/dynamic-table-component';
import { z } from '@girant/adapter';
import { ElButton, ElMessage, ElOption, ElSelect } from 'element-plus';

import {
  generateBatchNumber,
  getLocationList,
} from '#/api/warehouse-management/index';
import IconFont from '#/components/IconFont/IconFont.vue';
import { add } from '#/utils/numberUtils';
import BatchNumberAlert from '#/views/warehouse-management/batch-number-operation/components/BatchNumberAlert.vue';

import InventoryPopover from '../../components/InventoryPopover.vue';

const props = defineProps({
  warehouseItemData: {
    type: Object as PropType<WarehouseItemDataType>,
    default: () => ({}),
  },
  selectWarehouseList: {
    type: Array as PropType<SelectWarehouseListType[]>,
    default: () => [],
  },
  materialId: {
    type: String,
    default: '',
  },
  defaultLocationId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['entryQuantityChange', 'warehouseChange']);

const dynamicFormRef = ref<InstanceType<typeof DynamicForm>>();

// 当前选择的仓库id
const currentWarehouseId = ref<string>(props.warehouseItemData.warehouseId);

// 库位选项列表
const locationListOptions = ref<
  {
    label: string;
    value: string;
  }[]
>([]);

// 初始化时的第一条库位数据
const initLocationList = ref<LocationItemType[]>([]);

// 数据是否处理完成
const isLoaded = ref(false);

// 当前仓库填入的数量
const currentWarehouseFillQuantity = ref(0);

// 获取表单数据
const getFormData = async (hasWarehouseId: boolean = false) => {
  if (!dynamicFormRef.value) {
    console.warn('dynamicFormRef is not ready');
    return [];
  }

  try {
    const formValuesPromises = await dynamicFormRef.value.getAllFormValues();

    const formData = await Promise.all(formValuesPromises);

    if (hasWarehouseId) {
      return formData.map((item: any) => {
        return {
          ...item,
          warehouseId: currentWarehouseId.value,
        };
      });
    }
    return formData;
  } catch (error) {
    console.error('Error getting form data:', error);
    return [];
  }
};

// 获取当前仓库填入的数量
const getCurrentWarehouseFillQuantity = async () => {
  const formData = await getFormData();
  let fillQuantity = 0;
  for (const item of formData) {
    fillQuantity = add(fillQuantity, item.applyQuantity);
  }
  currentWarehouseFillQuantity.value = fillQuantity;
  emits('entryQuantityChange');
};

// 监听当前选择的仓库id
watch(
  () => currentWarehouseId.value,
  async (newVal) => {
    if (!newVal) {
      locationListOptions.value = [];
      isLoaded.value = true;
      return;
    }

    // 获取库位列表
    const locationListRes = await getLocationList({
      warehouseId: newVal,
      isLock: false,
      isEnable: true,
    });

    // 处理库位列表
    locationListOptions.value = locationListRes.map((item) => ({
      label: item.locationName,
      value: item.locationId,
    }));

    dynamicFormRef.value?.removeAllForms();
    dynamicFormRef.value?.initForm([]);

    if (!isLoaded.value) {
      const initLocationItem = props.warehouseItemData.locationList[0] || {
        locationId: '',
        unitPrice: 0,
        batchNumber: '',
        applyQuantity: 0,
      };

      if (initLocationItem) {
        initLocationItem.locationId =
          locationListOptions.value.find(
            (item) => item.value === props.defaultLocationId,
          )?.value ||
          locationListOptions.value[0]?.value ||
          '';
      }

      initLocationList.value = [initLocationItem];
    }

    isLoaded.value = true;

    nextTick(() => {
      getCurrentWarehouseFillQuantity();
      emits('warehouseChange');
    });
  },
  { immediate: true },
);

// 生成批次号
const generateBatchNumberForMaterialId = async () => {
  try {
    const batchNumber = await generateBatchNumber(props.materialId);
    return batchNumber;
  } catch {
    return '';
  }
};

// 数量改变
const onApplyQuantityChange = async () => {
  // 防抖
  getCurrentWarehouseFillQuantity();
};

// 动态表单配置
const formOptions = ref<VbenFormProps>({
  schema: [
    {
      component: (props: any) => {
        return h(
          ElSelect,
          {
            modelValue: props.modelValue,
            placeholder: '请选择库位',
            disabled: !currentWarehouseId.value,
          },
          {
            // 使用默认插槽渲染选项
            default: () =>
              locationListOptions.value.map((item) =>
                h(ElOption, {
                  key: item.value,
                  label: item.label,
                  value: item.value,
                }),
              ),
          },
        );
      },
      fieldName: 'locationId',
      label: '库位',
      rules: z.string().min(1, '请选择库位'),
      labelWidth: 50,
    },
    {
      component: 'Input',
      fieldName: 'batchNumber',
      label: '批次号',
      componentProps: {
        placeholder: '为空系统自动生成',
        clearable: true,
      },
      dependencies: {
        triggerFields: ['locationId'],
        componentProps: (props) => {
          return {
            disabled: !props.locationId,
          };
        },
      },
      renderComponentContent: (values: any) => {
        return {
          suffix: () => {
            return h('div', {}, [
              h(
                ElButton,
                {
                  link: true,
                  size: 'small',
                  disabled: !values.locationId,
                  onClick: async (e: MouseEvent) => {
                    e.stopPropagation();
                    try {
                      const batchNumber =
                        await generateBatchNumberForMaterialId();
                      values.mergeBatchNumber = batchNumber;
                    } catch {
                      ElMessage.error('生成批次号失败');
                    }
                  },
                },
                () => h(IconFont, { name: 'bianji', class: 'iconfont' }),
              ),
              h(BatchNumberAlert, {
                class: 'absolute right-[-30px] top-0',
                materialId: props.materialId,
                locationId: values.locationId,
                batchNumber: values.batchNumber,
              }),
            ]);
          },
        };
      },
      labelWidth: 60,
      // 选择第一个是div的子元素设置overflow-visible
      formItemClass: 'mr-10 col-span-2 [&>div]:!overflow-visible',
    },
    {
      component: 'InputNumber',
      fieldName: 'applyQuantity',
      label: '数量',
      componentProps: {
        min: 0,
        precision: 3,
        controlsPosition: 'right',
        onChange: onApplyQuantityChange,
      },
      dependencies: {
        triggerFields: ['locationId'],
        componentProps: (props) => {
          return {
            disabled: !props.locationId,
          };
        },
      },
      defaultValue: 0,
      rules: 'required',
      labelWidth: 50,
    },
  ],
  wrapperClass: 'grid-cols-4',
});

// 删除一个表单
const removeForm = () => {
  nextTick(() => {
    getCurrentWarehouseFillQuantity();
  });
};

// 校验表单数据
const validateFormData = async () => {
  const validateFormData = await dynamicFormRef.value?.validateAllForms(false);
  return validateFormData;
};

// 库存量
const inventory = computed(() => {
  return props.selectWarehouseList.find(
    (item) => item.value === currentWarehouseId.value,
  )?.inventory;
});

// 加上填入的数量
const InventoryAddQuantity = computed(() => {
  return inventory.value
    ? add(inventory.value, currentWarehouseFillQuantity.value)
    : 0;
});

// 可用量
const availableInventory = computed(() => {
  return props.selectWarehouseList.find(
    (item) => item.value === currentWarehouseId.value,
  )?.availableInventory;
});

// 加上填入的数量
const AvailableInventoryAddQuantity = computed(() => {
  return availableInventory.value
    ? add(availableInventory.value, currentWarehouseFillQuantity.value)
    : 0;
});

defineExpose({
  getFormData,
  validateFormData,
  currentWarehouseFillQuantity,
  currentWarehouseId,
});
</script>

<template>
  <div class="bg-primary-50/80 mb-3 rounded-lg px-2 shadow-sm">
    <div class="flex">
      <div
        class="border-primary-200/80 relative flex w-1/4 flex-col justify-center border-r-2 border-dashed pr-2"
      >
        <div class="space-y-3">
          <div class="flex items-center">
            <div class="text-sm text-gray-700">仓库：</div>
            <ElSelect
              v-model="currentWarehouseId"
              class="!w-full flex-1"
              placeholder="请选择仓库"
            >
              <ElOption
                v-for="item in selectWarehouseList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              />
            </ElSelect>

            <InventoryPopover
              :current-warehouse-id="currentWarehouseId"
              :inventory="inventory"
              :inventory-add-quantity="InventoryAddQuantity"
              :available-inventory="availableInventory"
              :available-inventory-add-quantity="AvailableInventoryAddQuantity"
            />

            <div class="flex items-center">
              <slot name="delete-wrapper"></slot>
            </div>
          </div>
        </div>
      </div>
      <div class="flex flex-1 flex-col">
        <DynamicForm
          v-if="isLoaded"
          ref="dynamicFormRef"
          class="flex-1 pl-4"
          :form-data="initLocationList"
          :form-options="formOptions"
          @remove-form="removeForm"
        />
      </div>
    </div>
  </div>
</template>
