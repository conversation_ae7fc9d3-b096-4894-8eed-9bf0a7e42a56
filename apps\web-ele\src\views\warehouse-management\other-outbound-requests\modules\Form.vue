<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { ElMessage, ElScrollbar } from 'element-plus';

import {
  getInOutReqDocDetail,
  saveInOutReqDoc,
  submitInOutReqDoc,
} from '#/api/warehouse-management';
import ApprovalTimeline from '#/components/approval-timeline/Index.vue';
import FormCard from '#/components/form-card/Index.vue';

import AdjustFormEdit from './adjust-form/form-edit/index.vue';
import AdjustFormView from './adjust-form/form-view/index.vue';
import MaterialFormEdit from './material-form/form-edit/index.vue';
import MaterialFormView from './material-form/form-view/index.vue';
import { confirm } from './method';

const props = defineProps({
  isView: {
    type: Boolean,
    default: false,
  },
  /** 其它出入库申请单id */
  inOutReqDocId: {
    type: String,
    default: '',
  },
  /** 其它出入库申请单编号 */
  inOutReqDocNumber: {
    type: String,
    default: '',
  },
  /** 审核流程实例ID */
  processInstanceId: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['submitSuccess']);
/** 调整信息ref*/
const adjustFormRef = ref();
/** 物料信息ref*/
const materialFormRef = ref();
const loading = ref(false);
/** 审核流程实例ID */
const processId = ref(props.processInstanceId);
/** 校验表单 */
const validateForm = async () => {
  // 校验调整信息表单 // 校验物料信息表单
  const [verification, verification2] = await Promise.all([
    adjustFormRef.value?.validateForm(),
    materialFormRef.value?.validateForm(),
  ]);
  // 获取表单数据
  const data = await getFormData();
  if (data.applyItemList.length === 0) {
    ElMessage.error('请填写物料信息');
    return false;
  }
  if (!verification || !verification2) {
    ElMessage.error('请填写完整表单');
    return false;
  }
  return true;
};
/** 获取表单数据 */
const getFormData = async () => {
  const [data, data2] = await Promise.all([
    adjustFormRef.value?.getFormData(),
    materialFormRef.value?.getFormData(),
  ]);
  return {
    ...data,
    applyItemList: data2,
  };
};
/** 提交表单 新增*/
const onSubmit = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    if (await confirm('确定提交吗？', '提示')) {
      loading.value = true;
      await submitInOutReqDoc({
        inOutReqDocId: props.inOutReqDocId,
        ...data,
      });
    }
    ElMessage.success('提交成功');
    emit('submitSuccess');
  } catch (error) {
    console.error(error);
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};
/** 提交暂存 */
const onSave = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    if (await confirm('确定提交暂存吗？', '提示')) {
      loading.value = true;
      await saveInOutReqDoc({
        inOutReqDocId: props.inOutReqDocId,
        ...data,
      });
    }
    ElMessage.success('提交暂存成功');
    emit('submitSuccess');
  } catch (error) {
    console.error(error);
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};
onMounted(async () => {
  try {
    // 如果没有审核流程，发送请求获取
    if (!processId.value) {
      const res = await getInOutReqDocDetail(
        props.inOutReqDocId,
        props.inOutReqDocNumber,
      );
      processId.value = res?.processInstanceId;
    }
  } catch (error) {
    console.error(error);
  }
});
defineExpose({
  onSubmit,
  onSave,
  validateForm,
  loading,
});
</script>
<template>
  <div v-loading="loading">
    <AdjustFormView
      v-if="isView"
      ref="adjustFormRef"
      :in-out-req-doc-number="inOutReqDocNumber"
      :in-out-req-doc-id="inOutReqDocId"
    />
    <AdjustFormEdit
      v-else
      ref="adjustFormRef"
      :in-out-req-doc-number="inOutReqDocNumber"
      :in-out-req-doc-id="inOutReqDocId"
    />
    <MaterialFormView
      v-if="isView"
      ref="materialFormRef"
      :in-out-req-doc-number="inOutReqDocNumber"
      :in-out-req-doc-id="inOutReqDocId"
    />
    <MaterialFormEdit
      v-else
      ref="materialFormRef"
      :in-out-req-doc-number="inOutReqDocNumber"
      :in-out-req-doc-id="inOutReqDocId"
    />
    <FormCard :is-footer="false" v-if="isView">
      <template #title>
        <span>审核流程</span>
      </template>
      <ElScrollbar>
        <ApprovalTimeline v-if="processId" :process-instance-id="processId" />
      </ElScrollbar>
    </FormCard>
  </div>
</template>
