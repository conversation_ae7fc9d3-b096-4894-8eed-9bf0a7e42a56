import type { VxeTableGridOptions } from '@girant/adapter';

/** 物料信息 */
export function useColumns(docStatus: string): VxeTableGridOptions['columns'] {
  const oldData = [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      slots: {
        default: 'materialName',
      },
      title: '物料',
      field: 'materialName',
      minWidth: 180,
    },
    {
      title: '规格型号',
      field: 'materialSpecs',
      minWidth: 150,
    },
    {
      title: '基本单位',
      field: 'baseUnitLabel',
      width: 65,
    },
    {
      title: '仓库',
      field: 'warehouseName',
      minWidth: 60,
    },
    {
      title: '库位',
      field: 'locationName',
      minWidth: 60,
    },
    {
      title: '批次号',
      field: 'batchNumber',
      minWidth: 100,
    },
    {
      title: docStatus === '40' ? '调整前库存量' : '现库存量',
      field: docStatus === '40' ? 'oldInventory' : 'inventory',
      minWidth: 100,
    },
    {
      slots: {
        default: 'quantity',
      },
      title: '调整数量',
      field: 'quantity',
      width: 65,
    },
  ];
  if (docStatus === '40') {
    oldData.push({
      title: '调整后库存量',
      field: 'newInventory',
      minWidth: 100,
    });
  }
  return oldData;
}
