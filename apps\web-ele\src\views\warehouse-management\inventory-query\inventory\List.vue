<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';
import type { UploadRequestOptions } from 'element-plus';

import type {
  InventoryQueryApi,
  MaterialCategoryTreeType,
} from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElCascader,
  ElCheckbox,
  ElDatePicker,
  ElInput,
  ElMessage,
  ElPopover,
  ElTooltip,
  ElUpload,
} from 'element-plus';

import {
  exportInventory,
  exportInventoryTemplate,
  getInventoryPage,
  getMaterialCategoryTree,
  importInventory,
} from '#/api/warehouse-management';

import Form from '../modules/Form.vue';
import { createDisabledDate } from '../modules/method';
import { useColumns, useGridFormSchema } from './data';
import ImportErrorData from './ImportErrorData.vue';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});
/** 物料id */
const materialId = ref<string>('');
/** 导出加载 */
const exportLoading = ref(false);
/** 仓库id */
const warehouseId = ref<string>('');
/** 库存数量 */
const inventoryQuantity = ref({
  minInventory: props.params?.minInventory,
  maxInventory: props.params?.maxInventory,
});
/** 可用量 */
const availableInventory = ref({
  minAvailableInventory: props.params?.minAvailableInventory,
  maxAvailableInventory: props.params?.maxAvailableInventory,
});
/** 物料细类选择器配置 */
const propsConfig = { multiple: true, emitPath: false };
/** 物料细类选择的数据 */
const materialCategory = ref(
  props.params?.materialCategoryList?.split(',') || [],
);
/** 物料细类数据 */
const materialCategoryData = ref<any[]>([]);
/** 最后入库时间*/
const lastInTime = ref({
  startTime: props.params?.lastInStartTime,
  endTime: props.params?.lastInEndTime,
});
/** 最后出库时间*/
const lastOutTime = ref({
  startTime: props.params?.lastOutStartTime,
  endTime: props.params?.lastOutEndTime,
});

/** 操作 */
const onActionClick = (e: any) => {
  switch (e.code) {
    case 'view': {
      onView(e.row);
      break;
    }
  }
};
/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    inventoryQuantity.value = {
      minInventory: '',
      maxInventory: '',
    };
    availableInventory.value = {
      minAvailableInventory: '',
      maxAvailableInventory: '',
    };
    lastInTime.value = {
      startTime: '',
      endTime: '',
    };
    lastOutTime.value = {
      startTime: '',
      endTime: '',
    };
    materialCategory.value = [];
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};
// 表格
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    handleReset,
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || true,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getInventoryPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            minInventory: inventoryQuantity.value.minInventory,
            maxInventory: inventoryQuantity.value.maxInventory,
            minAvailableInventory:
              availableInventory.value.minAvailableInventory,
            maxAvailableInventory:
              availableInventory.value.maxAvailableInventory,
            materialCategoryList: materialCategory.value,
            lastInStartTime: lastInTime.value.startTime,
            lastInEndTime: lastInTime.value.endTime,
            lastOutStartTime: lastOutTime.value.startTime,
            lastOutEndTime: lastOutTime.value.endTime,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<InventoryQueryApi.InventoryPage>,
});
/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  showCancelButton: true,
  showConfirmButton: false,
});
/** 查看 */
const onView = (row: any) => {
  materialId.value = row.materialId;
  warehouseId.value = row.warehouseId;
  formModalApi
    .setState({
      title: '查看',
    })
    .open();
};

/** 下载模板 */
async function getInventoryTemplate() {
  try {
    const response = await exportInventoryTemplate();
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch (error) {
    console.error('库存信息模板下载失败：', error);
  }
}

/** 是否覆盖已经存在的库存 */
const isOverwrite = ref(false);

/** 导入错误数据 */
const importErrorData = ref<InventoryQueryApi.ImportInventoryRes['data']>(
  {} as InventoryQueryApi.ImportInventoryRes['data'],
);

/** 导入数据错误模态框*/
const [ErrorDataModal, errorDataModalApi] = useVbenModal({
  title: '导入错误数据',
  showCancelButton: false,
  showConfirmButton: false,
  destroyOnClose: true,
  closeOnClickModal: false,
});

/** 数据导入*/
async function importInventoryHandle(options: UploadRequestOptions) {
  const { file, onSuccess } = options;
  try {
    const data = { file };
    const response = await importInventory({
      data,
      isOverwrite: isOverwrite.value,
    });
    onSuccess(response);

    gridApi.query();
    ElMessage.success('数据导入成功');
  } catch (error) {
    ElMessage.error('数据导入失败');
    if (error instanceof Error) {
      ElMessage.error(error.message);
    } else {
      importErrorData.value = (
        error as InventoryQueryApi.ImportInventoryRes
      ).data;
      errorDataModalApi.open();
    }
  }
}

/** 数据导出 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportInventory({
      ...formValues,
      minInventory: inventoryQuantity.value.minInventory,
      maxInventory: inventoryQuantity.value.maxInventory,
      minAvailableInventory: availableInventory.value.minAvailableInventory,
      maxAvailableInventory: availableInventory.value.maxAvailableInventory,
      materialCategoryList: materialCategory.value,
      lastInStartTime: lastInTime.value.startTime,
      lastInEndTime: lastInTime.value.endTime,
      lastOutStartTime: lastOutTime.value.startTime,
      lastOutEndTime: lastOutTime.value.endTime,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};
/** 获取数据 */
const getData = async () => {
  try {
    // 获取物料细类数据
    const data = await getMaterialCategoryTree();
    // 处理数据
    const convertMaterialData = (item: any) => {
      return {
        label: item.categoryName,
        value: item.categoryCode,
        children: item.children
          ? item.children.map((child: MaterialCategoryTreeType) =>
              convertMaterialData(child),
            )
          : [],
      };
    };
    // 执行转换
    materialCategoryData.value = data.map((item) => convertMaterialData(item));
  } catch {
    ElMessage.error('获取数据失败');
  }
};
onMounted(async () => {
  await gridApi.formApi.setValues({
    warehouseIdList: props.params?.warehouseIdList?.split(',') || [],
    materialCodeList: props.params?.materialCodeList?.split(',') || [],
    materialAttributeList:
      props.params?.materialAttributeList?.split(',') || [],
    materialTypeList: props.params?.materialTypeList?.split(',') || [],
  });
  await getData();
});
defineExpose({
  gridApi,
  Grid,
});
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <FormModal class="h-full w-8/12">
      <Form :material-id="materialId" :warehouse-id="warehouseId" />
    </FormModal>
    <!-- 错误数据展示区域 -->
    <ErrorDataModal class="w-6/12">
      <ImportErrorData :error-data="importErrorData" />
    </ErrorDataModal>

    <Grid>
      <template #form-materialCategoryList>
        <ElCascader
          class="w-full"
          :props="propsConfig"
          v-model="materialCategory"
          :options="materialCategoryData"
          :max-collapse-tags="1"
          collapse-tags
          filterable
          collapse-tags-tooltip
          clearable
        />
      </template>
      <template #form-lastInTime>
        <ElDatePicker
          v-model="lastInTime.startTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="createDisabledDate(false, lastInTime)"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="lastInTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="createDisabledDate(true, lastInTime)"
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #form-lastOutTime>
        <ElDatePicker
          v-model="lastOutTime.startTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="createDisabledDate(false, lastOutTime)"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="lastOutTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="createDisabledDate(true, lastOutTime)"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #form-inventoryQuantity>
        <ElInput
          v-model="inventoryQuantity.minInventory"
          type="number"
          placeholder="请输入最小值"
          clearable
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElInput
          v-model="inventoryQuantity.maxInventory"
          type="number"
          placeholder="请输入最大值"
          clearable
        />
      </template>
      <template #form-availableInventory>
        <ElInput
          v-model="availableInventory.minAvailableInventory"
          type="number"
          placeholder="请输入最小值"
          clearable
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElInput
          v-model="availableInventory.maxAvailableInventory"
          type="number"
          placeholder="请输入最大值"
          clearable
        />
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="下载导入模板"
          placement="top-start"
        >
          <ElButton circle class="mr-2" @click="getInventoryTemplate">
            <template #icon><span class="iconfont">&#xe678;</span></template>
          </ElButton>
        </ElTooltip>

        <ElPopover placement="bottom" width="180">
          <template #reference>
            <ElUpload
              :show-file-list="false"
              :http-request="importInventoryHandle"
              accept=".xlsx"
            >
              <ElButton circle class="mr-2">
                <template #icon>
                  <span class="iconfont">&#xe621;</span>
                </template>
              </ElButton>
            </ElUpload>
          </template>
          <ElCheckbox
            v-model="isOverwrite"
            label="覆盖已经存在的库存"
            size="small"
          />
        </ElPopover>

        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton :loading="exportLoading" circle @click="exportHandle">
            <template #icon><IconFont name="xiazai" /></template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
