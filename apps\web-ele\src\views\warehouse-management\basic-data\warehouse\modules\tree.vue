<script setup lang="ts">
import type { UploadRequestOptions } from 'element-plus';

import type { WarehouseInfoApi } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { cloneDeep, downloadFileFromResponse } from '@vben/utils';

import { Tree } from '@girant-web/tree-component';
import { ElButton, ElMessage, ElTag, ElTooltip, ElUpload } from 'element-plus';

import {
  download,
  exportWarehouse,
  getWarehouseList,
  importWarehouse,
} from '#/api/warehouse-management';

const emits = defineEmits([
  'addWarehouse',
  'editWarehouse',
  'addLocation',
  'editLocation',
  'nodeClick',
]);
/** 树组件 */
const treeRef = ref<InstanceType<typeof Tree>>();
const loading = ref(false);
/** 树数据 */
const warehouseTreeData = ref<any[]>([]);
/** 展示数据 */
const fetchData = ref<any[]>([]);
/** 模式选项*/
const modeOptions = [
  {
    label: '全部',
    value: 'all',
  },
  {
    label: '仓库',
    value: 'warehouse',
  },
  {
    label: '库位',
    value: 'location',
  },
];
/** 启用选项 */
const enableModeOptions = [
  {
    label: '全部',
    value: 'all',
  },
  {
    label: '启用',
    value: true,
  },
  {
    label: '停用',
    value: false,
  },
];
/** 选中的模式 */
const treeMode = ref('all');
/** 选中的启用状态 */
const enableMode = ref('all');
/** 加载状态 */
const loadingStatus = ref({
  template: false,
  importHandle: false,
  exportHandle: false,
});

/** 只展示仓库 */
const extractWarehouse = (nodes: any[]) => {
  nodes.forEach((node) => {
    node.children = [];
  });
  return nodes;
};

/** 只展示库位 */
const extractLocation = (nodes: any[]) => {
  nodes = nodes.flatMap((node) => node.children || []);
  return nodes;
};

/**
 * @description 根据选中的模式来展示不同的数据 默认展示全部数据
 * @description treeMode: 'all' | 'warehouse' | 'location'
 * @description enableMode: 'all' | true | false
 */
const handleChange = () => {
  fetchData.value = cloneDeep(warehouseTreeData.value);
  // 处理 仓库和库位
  switch (treeMode.value) {
    case 'location': {
      fetchData.value = extractLocation(fetchData.value);
      break;
    }
    case 'warehouse': {
      fetchData.value = extractWarehouse(fetchData.value);
      break;
    }
    // 默认展示全部数据
    default: {
      break;
    }
  }

  // 处理 启用和停用 如果不是全部 则过滤数据
  if (enableMode.value !== 'all') {
    fetchData.value = fetchData.value.filter(
      (item) => item.isEnable === enableMode.value,
    );
  }
};

/** 获取仓库数据 并处理 */
const getTreeData = async () => {
  try {
    loading.value = true;
    const res = await getWarehouseList({ isLoc: true });
    // 要显示的数据
    const treeData: any[] = [];
    // 处理库位
    const handleLoc = (data: WarehouseInfoApi.locationListType[]) => {
      return data.map((item) => ({
        ...item,
        id: item.locationId,
        label: item.locationName,
      }));
    };
    // 处理仓库数据
    res?.forEach((item) => {
      treeData.push({
        ...item,
        id: item.warehouseId,
        label: item.warehouseName,
        children: handleLoc(item.locationList),
      });
    });
    warehouseTreeData.value = treeData;
  } catch (error) {
    console.error(error);
    ElMessage.error(`获取仓库失败`);
  } finally {
    loading.value = false;
  }
};

/** 下载仓库模板 */
const getWarehouseTemplate = async () => {
  try {
    loadingStatus.value.template = true;
    const response = await download();
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch (error) {
    console.error('仓库模板下载失败：', error);
  } finally {
    loadingStatus.value.template = false;
  }
};

/** 导入仓库 */
const warehouseImportHandle = async (options: UploadRequestOptions) => {
  const { file, onSuccess } = options;
  try {
    loadingStatus.value.importHandle = true;
    const data = { file };
    const response = await importWarehouse(data);
    await refreshData();
    onSuccess(response);
    ElMessage.success('仓库数据导入成功');
  } catch {
    ElMessage.error('仓库数据导入失败');
  } finally {
    loadingStatus.value.importHandle = false;
  }
};

/** 导出仓库 */
const warehouseExportHandle = async () => {
  try {
    loadingStatus.value.exportHandle = true;
    const response = await exportWarehouse({
      isEnable: enableMode.value === 'all' ? undefined : enableMode.value,
      isLoc: treeMode.value === 'all',
    });
    downloadFileFromResponse(response);
    ElMessage.success('仓库数据导出成功');
  } catch {
    ElMessage.error('仓库数据导出失败');
  } finally {
    loadingStatus.value.exportHandle = false;
  }
};

/** 新增仓库 */
const addWarehouse = async () => {
  emits('addWarehouse');
};

/** 新增库位 */
const addLocation = async () => {
  const data = treeRef.value.tree.getCurrentNode();
  if (data?.warehouseId) {
    emits('addLocation', data.warehouseId);
  } else {
    emits('addLocation');
  }
};

/** 刷新数据 */
const refreshData = async () => {
  await getTreeData();
  handleChange();
};

/** 节点点击 */
const nodeClick = (data: any) => {
  emits('nodeClick', data);
};

onMounted(async () => {
  refreshData();
});

defineExpose({
  getWarehouseTemplate,
  warehouseImportHandle,
  warehouseExportHandle,
  addWarehouse,
  addLocation,
  refreshData,
});
</script>

<template>
  <div v-loading="loading" class="!pb-[40px] !pt-[90px]">
    <!-- 操作按钮区域 -->
    <div class="absolute top-[10px] flex w-[360px] flex-nowrap justify-end">
      <ElTooltip effect="light" content="下载导入模板" placement="top-start">
        <ElButton
          :disabled="loadingStatus.template"
          :loading="loadingStatus.template"
          circle
          @click="getWarehouseTemplate"
          class="mr-2"
        >
          <template #icon>
            <IconFont name="xiazaimoban" class="iconfont" />
          </template>
        </ElButton>
      </ElTooltip>
      <ElTooltip effect="light" content="导入" placement="top-start">
        <ElUpload
          :show-file-list="false"
          :http-request="warehouseImportHandle"
          accept=".xlsx"
        >
          <ElButton
            :disabled="loadingStatus.importHandle"
            :loading="loadingStatus.importHandle"
            circle
            class="mr-2"
          >
            <template #icon>
              <IconFont name="shangchuan" class="iconfont" />
            </template>
          </ElButton>
        </ElUpload>
      </ElTooltip>
      <ElTooltip effect="light" content="导出" placement="top-start">
        <ElButton
          :loading="loadingStatus.exportHandle"
          circle
          @click="warehouseExportHandle"
        >
          <template #icon>
            <IconFont name="xiazai" class="iconfont" />
          </template>
        </ElButton>
      </ElTooltip>
      <ElTooltip effect="light" content="刷新" placement="top-start">
        <ElButton circle @click="refreshData">
          <template #icon>
            <i class="vxe-table-icon-repeat"></i>
          </template>
        </ElButton>
      </ElTooltip>
    </div>
    <!-- 模式选择 -->
    <div class="absolute top-[50px] flex w-[360px] flex-nowrap justify-between">
      <!-- 树模式选择器 -->
      <ElSegmented
        v-model="treeMode"
        :options="modeOptions"
        size="default"
        @change="handleChange"
      />
      <!-- 启用状态选择器 -->
      <ElSegmented
        v-model="enableMode"
        :options="enableModeOptions"
        size="default"
        @change="handleChange"
      />
    </div>
    <ElScrollbar>
      <Tree
        ref="treeRef"
        :data-source="fetchData"
        :draggable="false"
        :show-icon="true"
        @node-click="nodeClick"
      >
        <template #icon="{ data }">
          <i class="icon-[stash--folder-alt-light]" v-if="data.warehouseId"></i>
          <i class="icon-[mdi-light--file]" v-else></i>
        </template>
        <template #operate="{ data }">
          <ElTag class="ml-[5px]" size="small" v-if="data.isLock"> 锁 </ElTag>
          <ElTag
            type="danger"
            class="ml-[5px]"
            size="small"
            v-if="!data.isEnable"
          >
            停用
          </ElTag>
        </template>
      </Tree>
    </ElScrollbar>
    <!-- 添加按钮 -->
    <div class="absolute bottom-[10px] right-[10px]">
      <ElButton type="primary" @click="addWarehouse">新增仓库</ElButton>
      <ElButton type="primary" @click="addLocation">新增库位</ElButton>
    </div>
  </div>
</template>
