<script setup lang="ts">
import type { PendingSubmitApi } from '#/api/warehouse-management/batch-number-operation/PendingSubmit';

import { computed, defineAsyncComponent, markRaw, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { ElEmpty, ElMessage, ElMessageBox } from 'element-plus';

import {
  execBatchnumDoc,
  saveMergeBatchnumDoc,
  saveSplitBatchnumDoc,
  submitMergeBatchnumDoc,
  submitSplitBatchnumDoc,
} from '#/api/warehouse-management';

import SelectMaterial from './select-material/Edit.vue';

const props = defineProps({
  docCode: {
    type: String,
    default: '',
  },
  batchnumDocId: {
    type: String,
    default: '',
  },
  batchnumDocNumber: {
    type: String,
    default: '',
  },
});

// 当前操作
const currentOperation = ref<string>(props.docCode || 'WM0050');

const currentWarehouseId = ref<string>('');
const currentMaterialId = ref<string>('');

const selectMaterialRef = ref();
const batchNumberOperationRef = ref();

const BatchNumberOperation = computed(() => {
  switch (currentOperation.value) {
    case 'WM0050': {
      return markRaw(
        defineAsyncComponent(() => import('./merge-batch-number/Edit.vue')),
      );
    }
    case 'WM0051': {
      return markRaw(
        defineAsyncComponent(() => import('./split-batch-number/Edit.vue')),
      );
    }
    default: {
      return markRaw(
        defineAsyncComponent(() => import('./merge-batch-number/Edit.vue')),
      );
    }
  }
});

const handleDocCodeChange = (value: string) => {
  currentOperation.value = value;
};

const handleWarehouseIdChange = (value: string) => {
  currentWarehouseId.value = value;
};

const handleMaterialIdChange = (value: string) => {
  currentMaterialId.value = value;
  currentWarehouseId.value = '';
};

/** 获取物料选择data */
const getSelectMaterialData = async () => {
  try {
    const formData = await selectMaterialRef.value.getMaterialSelForm();
    return formData;
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : '请检查表单数据');
  }
};

/** 获取合并/拆分data */
const getMergeOrSplitData = async () => {
  try {
    if (currentOperation.value === 'WM0050') {
      return await batchNumberOperationRef.value.getMergeForm();
    } else if (currentOperation.value === 'WM0051') {
      return await batchNumberOperationRef.value.getSplitForm();
    }
    return null;
  } catch (error) {
    throw new Error(
      error instanceof Error ? error.message : '请检查处理批次号数据',
    );
  }
};

const getFormData = async () => {
  try {
    const selectMaterialData = await getSelectMaterialData();
    if (!selectMaterialData) {
      return null;
    }
    const { afterFormData, beforeFormData } = await getMergeOrSplitData();
    if (selectMaterialData.docCode === 'WM0050') {
      return {
        batchnumDocId: props.batchnumDocId,
        materialId: selectMaterialData.materialId,
        warehouseId: selectMaterialData.warehouseId,
        remark: selectMaterialData.remark,
        remarkOptionList: selectMaterialData.remarkOptionList,
        serialNumber: selectMaterialData.serialNumber,
        mergeBatchNumber: afterFormData.mergeBatchNumber || '',
        locationId: afterFormData.locationId,
        batchItemList: beforeFormData.map((item: any) => {
          return {
            locationId: item.locationId,
            itemBatchNumber: item.itemBatchNumber,
            itemQuantity: item.itemQuantity,
          };
        }),
      };
    } else if (selectMaterialData.docCode === 'WM0051') {
      return {
        batchnumDocId: props.batchnumDocId,
        materialId: selectMaterialData.materialId,
        warehouseId: selectMaterialData.warehouseId,
        remark: selectMaterialData.remark,
        remarkOptionList: selectMaterialData.remarkOptionList,
        serialNumber: selectMaterialData.serialNumber,
        splitBatchNumber: beforeFormData.itemBatchNumber || '',
        locationId: beforeFormData.locationId,
        batchItemList: afterFormData.map((item: any) => {
          return {
            locationId: item.locationId,
            itemBatchNumber: item.mergeBatchNumber || '',
            itemQuantity: item.itemQuantity,
          };
        }),
      };
    }
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : '请检查表单数据');
    return null;
  }
};

const submitHandle = async () => {
  if (
    await ElMessageBox.confirm('确定暂存吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    try {
      const submitData = await getFormData();
      if (!submitData) {
        return false;
      }

      await (currentOperation.value === 'WM0050'
        ? submitMergeBatchnumDoc(
            submitData as PendingSubmitApi.SubmitMergeBatchnumDocParams,
          )
        : submitSplitBatchnumDoc(
            submitData as PendingSubmitApi.SubmitSplitBatchnumDocParams,
          ));
      ElMessage.success('提交成功');
      return true;
    } catch {
      ElMessage.error('提交失败');
      return false;
    }
  }
  return false;
};

const saveHandle = async () => {
  if (
    await ElMessageBox.confirm('确定暂存吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    try {
      const submitData = await getFormData();
      if (!submitData) {
        return false;
      }
      await (currentOperation.value === 'WM0050'
        ? saveMergeBatchnumDoc(
            submitData as PendingSubmitApi.SubmitMergeBatchnumDocParams,
          )
        : saveSplitBatchnumDoc(
            submitData as PendingSubmitApi.SubmitSplitBatchnumDocParams,
          ));
      ElMessage.success('暂存成功');
      return true;
    } catch {
      ElMessage.error('暂存失败');
      return false;
    }
  }
  return false;
};

const execHandle = async () => {
  if (
    await ElMessageBox.confirm('确定执行吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    try {
      await execBatchnumDoc(props.batchnumDocId);
      ElMessage.success('执行成功');
      return true;
    } catch {
      ElMessage.error('执行失败');
      return false;
    }
  }
  return false;
};

defineExpose({
  getFormData,
  submitHandle,
  saveHandle,
  execHandle,
});
</script>

<template>
  <div class="h-full">
    <SelectMaterial
      ref="selectMaterialRef"
      :batchnum-doc-id="batchnumDocId"
      :batchnum-doc-number="batchnumDocNumber"
      @doc-code-change="handleDocCodeChange"
      @material-id-change="handleMaterialIdChange"
      @warehouse-id-change="handleWarehouseIdChange"
    />

    <div class="border-primary-500 rounded-lg border p-2">
      <div v-if="isEmpty(currentMaterialId) || isEmpty(currentWarehouseId)">
        <ElEmpty description="请选择物料和仓库" :image-size="50" />
      </div>
      <BatchNumberOperation
        v-else
        ref="batchNumberOperationRef"
        :warehouse-id="currentWarehouseId"
        :material-id="currentMaterialId"
        :batchnum-doc-id="batchnumDocId"
        :batchnum-doc-number="batchnumDocNumber"
      />
    </div>
  </div>
</template>
