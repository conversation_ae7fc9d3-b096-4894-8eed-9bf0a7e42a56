<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { MaterialCategoryTreeType } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useAccess } from '@vben/access';
import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElCascader,
  ElDatePicker,
  ElMessage,
  ElTag,
} from 'element-plus';

import {
  exportInOutReqItem,
  getInOutReqItemPage,
  getMaterialCategoryTree,
} from '#/api/warehouse-management';

import Form from '../modules/Form.vue';
import {
  createDisabledDate,
  docStatusDict,
  openModal,
} from '../modules/method';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});
const { hasAccessByCodes } = useAccess();
const formRef = ref<InstanceType<typeof Form>>();
const exportLoading = ref(false);
/** 其它出入库申请单编号 */
const inOutReqDocNumber = ref('');
/** 其它出入库申请单id */
const inOutReqDocId = ref('');
/** 物料细类选择器配置 */
const propsConfig = { multiple: true, emitPath: false };
/** 物料细类数据 */
const materialCategoryData = ref<any[]>([]);
/** 物料细类选择的数据 */
const materialCategory = ref(props.params?.materialCategory?.split(',') || []);
/** 审核流程实例ID */
const processInstanceId = ref('');
/** 当前单据状态 */
const docStatus = ref('');
const isView = ref(false);
/** 提交时间 */
const submitTime = ref({
  startTime: props.params?.submitStartTime,
  endTime: props.params?.submitEndTime,
});

/** 完成时间 */
const finishTime = ref({
  startTime: props.params?.finishStartTime,
  endTime: props.params?.finishEndTime,
});

/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    submitTime.value = {
      startTime: '',
      endTime: '',
    };
    finishTime.value = {
      startTime: '',
      endTime: '',
    };
    materialCategory.value = [];
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};
/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onConfirm: () => {
    formRef.value?.onSubmit();
  },
  confirmText: '提交',
  showCancelButton: true,
  showConfirmButton: hasAccessByCodes(['wm:outboundreq:submit']),
});
/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    handleReset,
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || true,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getInOutReqItemPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            submitStartTime: submitTime.value.startTime,
            submitEndTime: submitTime.value.endTime,
            finishStartTime: finishTime.value.startTime,
            finishEndTime: finishTime.value.endTime,
            materialCategoryList: materialCategory.value,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions,
});
/** 查看 */
const onView = (row: any) => {
  isView.value = true;
  docStatus.value = row.docStatus;
  inOutReqDocId.value = row.inOutReqDocId;
  inOutReqDocNumber.value = row.inOutReqDocNumber;
  processInstanceId.value = row.processInstanceId;
  openModal(formModalApi, false, '其他出库申请单详情');
};
/** 新增 */
const onAdd = () => {
  isView.value = false;
  inOutReqDocId.value = '';
  inOutReqDocNumber.value = '';
  openModal(formModalApi, true, '新增出库申请');
};
/** 导出 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportInOutReqItem({
      ...formValues,
      submitStartTime: submitTime.value.startTime,
      submitEndTime: submitTime.value.endTime,
      finishStartTime: finishTime.value.startTime,
      finishEndTime: finishTime.value.endTime,
      materialCategoryList: materialCategory.value,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};
/** 获取数据 */
const getData = async () => {
  try {
    // 获取物料细类数据
    const data = await getMaterialCategoryTree();
    // 处理数据
    const convertMaterialData = (item: any) => {
      return {
        label: item.categoryName,
        value: item.categoryCode,
        children: item.children
          ? item.children.map((child: MaterialCategoryTreeType) =>
              convertMaterialData(child),
            )
          : [],
      };
    };
    // 执行转换
    materialCategoryData.value = data.map((item) => convertMaterialData(item));
  } catch {
    ElMessage.error('获取数据失败');
  }
};
/** 提交成功 */
const submitSuccess = () => {
  // 重新获取数据
  gridApi.query();
  formModalApi.close();
};
onMounted(async () => {
  await gridApi.formApi.setValues({
    inOutReqDocNumberList:
      props.params?.inOutReqDocNumberList?.split(',') || [],
    submitUserList: props.params?.submitUserList?.split(',') || [],
    materialUserList: props.params?.materialUserList?.split(',') || [],
    docStatusList: props.params?.docStatusList?.split(',') || [],
    docCodeList: props.params?.docCodeList?.split(',') || [],
    materialIdList: props.params?.materialIdList?.split(',') || [],
    materialAttributeList:
      props.params?.materialAttributeList?.split(',') || [],
    materialTypeList: props.params?.materialTypeList?.split(',') || [],
  });
  await getData();
});
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <FormModal class="h-full w-8/12">
      <Form
        ref="formRef"
        :is-view="isView"
        @submit-success="submitSuccess"
        :in-out-req-doc-number="inOutReqDocNumber"
        :in-out-req-doc-id="inOutReqDocId"
        :process-instance-id="processInstanceId"
      />
      <template #center-footer>
        <ElButton
          v-if="!inOutReqDocNumber"
          type="primary"
          @click="formRef?.onSave()"
          v-access:code="'wm:outboundreq:submit'"
        >
          暂存
        </ElButton>
      </template>
    </FormModal>
    <Grid>
      <template #toolbar-actions>
        <ElButton
          type="primary"
          @click="onAdd"
          v-access:code="'wm:outboundreq:submit'"
        >
          新增
        </ElButton>
      </template>
      <template #form-materialCategoryList>
        <ElCascader
          class="w-full"
          :props="propsConfig"
          v-model="materialCategory"
          :options="materialCategoryData"
          :max-collapse-tags="1"
          collapse-tags
          filterable
          collapse-tags-tooltip
          clearable
        />
      </template>
      <template #form-submitTime>
        <ElDatePicker
          v-model="submitTime.startTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="createDisabledDate(false, submitTime)"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="submitTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="createDisabledDate(true, submitTime)"
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #form-finishTime>
        <ElDatePicker
          v-model="finishTime.startTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="createDisabledDate(false, finishTime)"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="finishTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="createDisabledDate(true, finishTime)"
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #submitUserName="{ row }">
        <span>{{ row.submitUserName }}</span>
        <span v-if="row.submitUserDeptName">
          ({{ row.submitUserDeptName }})
        </span>
      </template>
      <template #materialUserName="{ row }">
        <span>{{ row.materialUserName }}</span>
        <span v-if="row.materialUserDeptName">
          ({{ row.materialUserDeptName }})
        </span>
      </template>
      <template #docStatusLabel="{ row }">
        <ElTag size="small" :type="docStatusDict[row.docStatus]">
          {{ row.docStatusLabel }}
        </ElTag>
      </template>
      <template #CellOperation="{ row }">
        <ElButton link size="small" @click="onView(row)" type="info">
          查看
        </ElButton>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            :loading="exportLoading"
            circle
            @click="exportHandle"
            v-access:code="'wm:outboundreq:export:list:item'"
          >
            <template #icon>
              <IconFont name="xiazai" />
            </template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
