<script setup lang="ts">
import type { StaffInfoType } from '#/api/common/staff';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@vben/common-ui';

import { UploadFiles } from '@girant-web/upload-files-component';
import { ElMessage } from 'element-plus';

import {
  getAssemblyDocDetail,
  getEnableProdBomPage,
  getMaterialDetail,
} from '#/api';
import { getStaffInfo } from '#/api/common/staff';
import FormCard from '#/components/form-card/Index.vue';
import RemoteSearchSelect from '#/components/remote-search-select/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  /** 组装拆卸单据ID */
  assemblyDocId: {
    type: String,
    default: '',
  },
  /** 组装拆卸单据编号*/
  assemblyDocNumber: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['productChanged', 'quantityChange']);

const loading = ref(false);
/** 文件上传ref*/
const fileRef = ref();
/** 当前登录用户的员工信息 */
const staffData = ref<StaffInfoType>();

/** 拆卸数量变化 */
const onQuantityChange = async (currentValue: number, oldValue: number) => {
  emit('quantityChange', currentValue, oldValue);
};

/** 拆卸信息表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(onQuantityChange),
  showDefaultActions: false,
  wrapperClass:
    'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3',
});

onMounted(async () => {
  if (props.assemblyDocId || props.assemblyDocNumber) {
    getData();
  } else {
    staffData.value = await getStaffInfo();
    formApi.setValues({
      executorUser: staffData.value?.staffId,
    });
  }
});

/** 获取拆卸单据信息 */
const getData = async () => {
  try {
    loading.value = true;
    const data = await getAssemblyDocDetail(
      props.assemblyDocId,
      props.assemblyDocNumber,
    );

    const formValues = {
      ...data,
      productMaterialId: {
        materialId: data.productMaterialId,
        materialName: `${data.productMaterialName}（${data.productMaterialCode ?? '无编号'}）`,
      },
    };
    // 赋值
    formApi.setValues(formValues);
    emit('quantityChange', data.quantity, 1);
  } catch (error) {
    console.error(error);
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

/** 获取母件分页信息 */
const fetchMaterial =
  () =>
  async ({
    keyword,
    pageNum,
    pageSize,
  }: {
    keyword: string;
    pageNum: number;
    pageSize: number;
  }) => {
    return await getEnableProdBomPage({
      materialName: keyword,
      pageNum,
      pageSize,
    });
  };

/** 母件信息变化 */
const onChange = async (materialId: any, bomMessage: any) => {
  formApi.setFieldValue('productMaterialId', {
    materialId,
    materialName: `${bomMessage.materialName}（${bomMessage.materialCode ?? '无编号'}）`,
  });

  if (materialId) {
    // 获取母件物料信息
    const materialDetailRes = await getMaterialDetail(materialId);
    formApi.setValues({
      baseUnitLabel: materialDetailRes.baseUnitLabel,
      materialSpecs: materialDetailRes.materialSpecs,
    });
    // 母件ID，查询物流详情
    emit('productChanged', bomMessage.bomId);
  } else {
    // 清空母件物料信息
    formApi.setValues({
      baseUnitLabel: '',
      materialSpecs: '',
    });
    emit('productChanged', '');
  }
};

/** 文件上传成功后 */
const filesSubmitSuccess = async (res: any) => {
  if (res.serialNumber) {
    formApi.setFieldValue('serialNumber', res.serialNumber);
  }
};

/** 校验 */
const validateForm = async () => {
  // 校验表单
  const verification = await formApi.validate();
  if (!verification.valid) {
    return false;
  }
  return true;
};

/** 提交 */
const getFormData = async () => {
  const data = await formApi.getValues();
  // 提取出需要的字段
  const result = {
    // 拆卸code 枚举WmAssemblyTypeEnums
    docCode: 'WM0061',
    executorUser: data.executorUser,
    productMaterialId: data.productMaterialId.materialId,
    warehouseId: data.warehouseId,
    quantity: data.quantity,
    remark: data.remark,
    serialNumber: data.serialNumber,
  };
  return result;
};

/** 对外开放方法 */
defineExpose({
  getFormData,
  validateForm,
});
</script>

<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>拆卸单信息</span>
    </template>
    <Form>
      <template #productMaterialId="row">
        <RemoteSearchSelect
          label-key="materialName"
          value-key="materialId"
          :model-value="row.value"
          input-placeholder="请选择拆卸成品"
          placeholder="请输入关键字搜索"
          :page-size="10"
          :multiple="false"
          :fetch-method="fetchMaterial()"
          @change="onChange"
        >
          <template #item="{ item }">
            <span class="font-bold">{{ item.materialName }}</span>
            <span class="text-gray-400"> 【{{ item.materialCode }}】 </span>
          </template>
        </RemoteSearchSelect>
      </template>
      <template #baseUnitLabel="row">
        <span v-if="row.value">{{ row.value }}</span>
        <span v-else class="text-gray-400">请选择拆卸成品</span>
      </template>
      <template #materialSpecs="row">
        <span v-if="row.value">{{ row.value }}</span>
        <span v-else class="text-gray-400">请选择拆卸成品</span>
      </template>
      <template #serialNumber="row">
        <div class="w-full">
          <UploadFiles
            mode="editMode"
            ref="fileRef"
            :show-operat-button="false"
            :show-table="fileRef?.fileList?.length > 0"
            :show-thumbnail="false"
            @files-submit-success="filesSubmitSuccess"
            :auto-upload="true"
            :serial-number="row.value"
          >
            <template #trigger>
              <div class="text-center">
                <i class="icon-[bx--folder] mx-auto h-12 w-12"></i>
                <p class="mt-2">点击或将文件拖拽到这里上传</p>
                <p class="text-xs text-gray-500">
                  支持格式：.rar .zip .doc .docx .pdf .jpg...
                </p>
              </div>
            </template>
          </UploadFiles>
        </div>
      </template>
    </Form>
  </FormCard>
</template>
