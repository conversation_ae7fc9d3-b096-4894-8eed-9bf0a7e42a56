<script setup lang="ts">
import { computed, defineAsyncComponent, markRaw, ref } from 'vue';

const props = defineProps<{
  docStatus: string;
  inBoundDocId: string;
  inBoundDocNumber: string;
}>();

const emits = defineEmits(['inboundSuccess', 'inboundLoading']);

const formModalRef = ref();

const onInboundSuccess = () => {
  emits('inboundSuccess');
};

const onInboundLoading = (loading: boolean) => {
  emits('inboundLoading', loading);
};

const currentComponent = computed(() => {
  switch (props.docStatus) {
    // 待入库
    case '00': {
      return markRaw(
        defineAsyncComponent(() => import('../stock-pending/modules/Form.vue')),
      );
    }

    // 已入库
    case '10': {
      return markRaw(
        defineAsyncComponent(() => import('../stock-already/modules/Form.vue')),
      );
    }

    // 已关闭
    default: {
      return markRaw(
        defineAsyncComponent(
          () => import('../inbound-documents/modules/Form.vue'),
        ),
      );
    }
  }
});
</script>

<template>
  <component
    :is="currentComponent"
    ref="formModalRef"
    :in-bound-doc-id="inBoundDocId"
    :in-bound-doc-number="inBoundDocNumber"
    @inbound-success="onInboundSuccess"
    @inbound-loading="onInboundLoading"
  />
</template>
