<script setup lang="ts">
import { ref } from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';

import { delTransferDoc } from '#/api/warehouse-management';

import FormToWarehouseTansfer from '../../components/FormToWarehouseTansfer.vue';

const props = defineProps({
  transferDocId: {
    type: String,
    default: '',
  },
  transferDocNumber: {
    type: String,
    default: '',
  },
  docStatus: {
    type: String,
    default: '',
  },
});

const emits = defineEmits([
  'handleCancel',
  'saveSuccess',
  'submitSuccess',
  'deleteSuccess',
  'transferLoading',
]);

const handleCancel = () => {
  emits('handleCancel');
};

const FormToWarehouseTansferRef =
  ref<InstanceType<typeof FormToWarehouseTansfer>>();

const submitHandle = async () => {
  await ElMessageBox.confirm('确定提交吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  })
    .then(async () => {
      try {
        emits('transferLoading', true);
        const result = await FormToWarehouseTansferRef.value?.submitHandle();
        if (result) {
          emits('submitSuccess');
        }
      } catch {
        ElMessage.error('提交失败');
      } finally {
        emits('transferLoading', false);
      }
    })
    .catch(() => {});
};

const saveHandle = async () => {
  await ElMessageBox.confirm('确定暂存吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        emits('transferLoading', true);
        const result = await FormToWarehouseTansferRef.value?.saveHandle();
        if (result) {
          emits('saveSuccess');
        }
      } catch {
        ElMessage.error('暂存失败');
      } finally {
        emits('transferLoading', false);
      }
    })
    .catch(() => {});
};

const deleteHandle = async () => {
  await ElMessageBox.confirm('确定删除单据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        emits('transferLoading', true);
        await delTransferDoc(props.transferDocId);
        emits('deleteSuccess');
      } catch {
        ElMessage.error('删除失败');
      } finally {
        emits('transferLoading', false);
      }
    })
    .catch(() => {});
};
</script>

<template>
  <div class="h-full">
    <FormToWarehouseTansfer
      ref="FormToWarehouseTansferRef"
      :transfer-doc-id="transferDocId"
      :transfer-doc-number="transferDocNumber"
    >
      <template #btn-group>
        <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
        <ElButton
          type="primary"
          @click="saveHandle"
          v-access:code="'wm:stock:transfer:submit'"
        >
          暂存
        </ElButton>
        <ElButton
          type="danger"
          @click="submitHandle"
          v-if="docStatus === '20'"
          v-access:code="'wm:stock:transfer:submit'"
        >
          重新提交
        </ElButton>
        <ElButton
          type="primary"
          @click="submitHandle"
          v-else
          v-access:code="'wm:stock:transfer:submit'"
        >
          提交
        </ElButton>
        <ElButton type="danger" @click="deleteHandle">删除单据</ElButton>
      </template>
    </FormToWarehouseTansfer>
  </div>
</template>
