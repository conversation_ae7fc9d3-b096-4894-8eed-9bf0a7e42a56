<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse, isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage, ElMessageBox, ElTooltip } from 'element-plus';

import {
  exportPendingOutBoundDoc,
  getPendingOutBoundDocPage,
  getPrepDocDetailByOutDoc,
  rollbackPrepDoc,
} from '#/api/warehouse-management/index';
import { isAfter, isBefore } from '#/utils/dateUtils';
import { WS } from '#/utils/socket/common-socketio';

import CloseBound from '../components/close-bound/form-edit/index.vue';
import PrepForm from '../material-pending/modules/Form.vue';
import { useColumns, useGridFormSchema } from './data';
import Form from './modules/Form.vue';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      applyEndTime?: string;
      applyStartTime?: string;
      applyUserList?: string;
      isRectify?: unknown;
      materialUserList?: string;
      origDocNumberList?: string;
      origDocTypeCodeList?: string;
      outBoundDocNumberList?: string;
      preparationStateList?: string;
    }>,
    default: () => ({}),
  },
});
const wsType = [
  'wm.outbound.docstatus.pending.add',
  'wm.outbound.docstatus.finished',
  'wm.outbound.docstatus.cancelAudit',
  'wm.outbound.docstatus.pending.cancelReject',
  'wm.outbound.docstatus.close',
  'wm.outbound.export.pending',
  'wm.outbound.export.finished',
];

const modalFormRef = ref<InstanceType<typeof Form>>();
const outBoundDocId = ref<string>('');
const outboundDocNumber = ref<string>('');
const origDocTypeCode = ref<string>('');

/** 提交时间 */
const applyTime = ref({
  // 开始时间
  applyStartTime: props.params?.applyStartTime || '',
  // 结束时间
  applyEndTime: props.params?.applyEndTime || '',
});

/** 出库弹窗 */
const [FormModal, formModalApi] = useVbenModal({
  confirmText: '确认出库',
  destroyOnClose: true,
  onBeforeClose: () => {
    outBoundDocId.value = '';
    outboundDocNumber.value = '';
    return true;
  },
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

/** 备料弹窗 */
const [PrepModal, prepModalApi] = useVbenModal({
  confirmText: '确认备料',
  destroyOnClose: true,
  onBeforeClose: () => {
    outBoundDocId.value = '';
    outboundDocNumber.value = '';
    return true;
  },

  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

/** 关闭出入库弹窗 */
const [CloseModal, closeModalApi] = useVbenModal({
  confirmText: '取消出库',
  destroyOnClose: true,
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
  onBeforeClose: () => {
    outBoundDocId.value = '';
    outboundDocNumber.value = '';
    return true;
  },
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      applyTime.value = {
        applyStartTime: '',
        applyEndTime: '',
      };

      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[75px]',
    },
    collapsed: isEmpty(props.attr?.collapsed) ? true : props.attr?.collapsed,
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? true
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows || 2,
    schema: useGridFormSchema(),
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    checkboxConfig: {
      highlight: true,
    },
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          params.applyStartTime = applyTime.value.applyStartTime;
          params.applyEndTime = applyTime.value.applyEndTime;

          if (params.origDocTypeCodeList) {
            params.origDocTypeCodeList = params.origDocTypeCodeList.join(',');
          }

          if (params.applyUserList) {
            params.applyUserList = params.applyUserList.join(',');
          }

          return await getPendingOutBoundDocPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      keyField: 'supplierId',
    },
    // showOverflow: false,
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    applyUserList: props.params?.applyUserList?.split(',') || [],
    materialUserList: props.params?.materialUserList?.split(',') || [],
    origDocNumberList: props.params?.origDocNumberList?.split(',') || [],
    origDocTypeCodeList: props.params?.origDocTypeCodeList?.split(',') || [],
    outBoundDocNumberList:
      props.params?.outBoundDocNumberList?.split(',') || [],
    preparationStateList: props.params?.preparationStateList?.split(',') || [],
    isRectify: isEmpty(props.params?.isRectify) ? '' : props.params?.isRectify,
  });
  WS.on(wsType, gridApi.query);
});

function openOutboundModal(row: RowType) {
  outBoundDocId.value = row.outBoundDocId;
  outboundDocNumber.value = row.outBoundDocNumber;
  formModalApi
    .setState({
      title: `确认出库`,
    })
    .open();
}

const openPrepModal = (docId: string, docNumber: string) => {
  formModalApi.close();

  outBoundDocId.value = docId;
  outboundDocNumber.value = docNumber;
  prepModalApi
    .setState({
      title: `确认备料`,
    })
    .open();
};

// 取消出库
async function onCancelOutbound(row: RowType) {
  outBoundDocId.value = row.outBoundDocId;
  outboundDocNumber.value = row.outBoundDocNumber;
  origDocTypeCode.value = row.origDocTypeCode;
  closeModalApi
    .setState({
      title: `取消出库`,
    })
    .open();
}

/** 获取备料单据 */
const getPrepDocDetailHandle = async (
  outBoundDocId: string,
  outBoundDocNumber: string,
) => {
  try {
    const prepDocDetailRes = await getPrepDocDetailByOutDoc({
      outBoundDocId,
      outBoundDocNumber,
      isQueryItem: true,
    });
    return prepDocDetailRes;
  } catch {
    ElMessage.error('获取备料单据失败');
    return null;
  }
};

/** 备料单据返仓 */
const rollbackPrepDocHandle = async (row: RowType) => {
  outBoundDocId.value = row.outBoundDocId;
  outboundDocNumber.value = row.outBoundDocNumber;

  if (
    await ElMessageBox.confirm('确定返仓备料单据吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    const prepDocDetailRes = await getPrepDocDetailHandle(
      outBoundDocId.value,
      outboundDocNumber.value,
    );
    if (prepDocDetailRes) {
      await rollbackPrepDoc(prepDocDetailRes.prepDocId);
      ElMessage.success('返仓成功');
      gridApi.query();
    }
  }
};

function onActionClick(e: OnActionClickParams<RowType>) {
  switch (e.code) {
    case 'close': {
      onCancelOutbound(e.row);
      break;
    }
    case 'outbound': {
      openOutboundModal(e.row);
      break;
    }
    case 'prep': {
      openPrepModal(e.row.outBoundDocId, e.row.outBoundDocNumber);
      break;
    }
    case 'return': {
      rollbackPrepDocHandle(e.row);
      break;
    }
  }
}

/** 数据导出 */
async function exportPendingOutBoundDocHandle() {
  try {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    formValues.applyStartTime = applyTime.value.applyStartTime;
    formValues.applyEndTime = applyTime.value.applyEndTime;
    const response = await exportPendingOutBoundDoc(formValues);
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  }
}

function onOutboundSuccess() {
  formModalApi.close();
  gridApi.query();
}

function onOutboundLoading(loading: boolean) {
  formModalApi.setState({ loading });
}

function onPrepSuccess() {
  prepModalApi.close();
  gridApi.query();
}

function onPrepLoading(loading: boolean) {
  prepModalApi.setState({ loading });
}

function onCloseBoundLoading(loading: boolean) {
  closeModalApi.setState({ loading });
}

function onCloseBoundSuccess() {
  closeModalApi.close();
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="w-10/12">
      <Form
        ref="modalFormRef"
        :out-bound-doc-id="outBoundDocId"
        :out-bound-doc-number="outboundDocNumber"
        @bound-success="onOutboundSuccess"
        @bound-loading="onOutboundLoading"
        @open-prep-modal="openPrepModal"
        @handle-cancel="formModalApi.close()"
      />
    </FormModal>
    <PrepModal class="w-10/12">
      <PrepForm
        :out-bound-doc-id="outBoundDocId"
        :out-bound-doc-number="outboundDocNumber"
        @bound-success="onPrepSuccess"
        @bound-loading="onPrepLoading"
        @handle-cancel="prepModalApi.close()"
      />
    </PrepModal>

    <CloseModal class="w-3/5">
      <CloseBound
        :in-out-bound-doc-id="outBoundDocId"
        :in-out-bound-doc-number="outboundDocNumber"
        :orig-doc-type-code="origDocTypeCode"
        @close-bound-loading="onCloseBoundLoading"
        @close-bound-success="onCloseBoundSuccess"
        @handle-cancel="closeModalApi.close()"
      />
    </CloseModal>
    <Grid>
      <template #form-applyTime>
        <ElDatePicker
          v-model="applyTime.applyStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, applyTime.applyStartTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="applyTime.applyEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, applyTime.applyStartTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            circle
            @click="exportPendingOutBoundDocHandle"
            v-access:code="'wm:outbound:export'"
          >
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
