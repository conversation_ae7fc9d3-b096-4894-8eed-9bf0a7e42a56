<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { MaterialCategoryTreeType } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElCascader,
  ElDatePicker,
  ElMessage,
  ElTag,
} from 'element-plus';

import {
  exportInOutReqItemInBound,
  getInOutReqItemPageInBound,
  getMaterialCategoryTree,
} from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';

import { docStatusDict } from '../config/list';
import FormEdit from '../modules/FormEdit.vue';
import FormView from '../modules/FormView.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});

const exportLoading = ref(false);
/** 物料细类选择器配置 */
const propsConfig = { multiple: true, emitPath: false };
/** 物料细类数据 */
const materialCategoryData = ref<any[]>([]);
/** 物料细类选择的数据 */
const materialCategory = ref(
  props.params?.materialCategoryList?.split(',') || [],
);
/** 提交时间 */
const submitTime = ref({
  submitStartTime: props.params?.submitStartTime,
  submitEndTime: props.params?.submitEndTime,
});

/** 完成时间 */
const finishTime = ref({
  finishStartTime: props.params?.finishStartTime,
  finishEndTime: props.params?.finishEndTime,
});
/** 模态框组件*/
const [ViewModal, viewModalApi] = useVbenModal({
  connectedComponent: FormView,
  destroyOnClose: true,
});
const [EditModal, editModalApi] = useVbenModal({
  connectedComponent: FormEdit,
  destroyOnClose: true,
});
/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    submitTime.value = {
      submitStartTime: '',
      submitEndTime: '',
    };
    finishTime.value = {
      finishStartTime: '',
      finishEndTime: '',
    };
    materialCategory.value = [];
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};

/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    handleReset,
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || true,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getInOutReqItemPageInBound({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            ...submitTime.value,
            ...finishTime.value,
            materialCategoryList: materialCategory.value,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions,
});
/** 查看 */
const onView = (row: any) => {
  viewModalApi
    .setState({
      title: '其他出库申请单详情',
    })
    .setData({
      inOutReqDocId: row.inOutReqDocId,
      inOutReqDocNumber: row.inOutReqDocNumber,
      processInstanceId: row.processInstanceId,
      docStatus: row.docStatus,
      refreshList: () => {
        gridApi.query();
      },
    })
    .open();
};
/** 新增 */
const onAdd = () => {
  editModalApi
    .setState({
      title: '新增出库申请',
    })
    .setData({
      refreshList: () => {
        gridApi.query();
      },
    })
    .open();
};
/** 导出 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportInOutReqItemInBound({
      ...formValues,
      ...submitTime.value,
      ...finishTime.value,
      materialCategoryList: materialCategory.value,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};
/** 获取数据 */
const getData = async () => {
  try {
    // 获取物料细类数据
    const data = await getMaterialCategoryTree();
    // 处理数据
    const convertMaterialData = (item: any) => {
      return {
        label: item.categoryName,
        value: item.categoryCode,
        children: item.children
          ? item.children.map((child: MaterialCategoryTreeType) =>
              convertMaterialData(child),
            )
          : [],
      };
    };
    // 执行转换
    materialCategoryData.value = data.map((item) => convertMaterialData(item));
  } catch {
    ElMessage.error('获取数据失败');
  }
};
onMounted(async () => {
  await gridApi.formApi.setValues({
    inOutReqDocNumberList:
      props.params?.inOutReqDocNumberList?.split(',') || [],
    submitUserList: props.params?.submitUserList?.split(',') || [],
    docStatusList: props.params?.docStatusList?.split(',') || [],
    docCodeList: props.params?.docCodeList?.split(',') || [],
    materialIdList: props.params?.materialIdList?.split(',') || [],
    materialAttributeList:
      props.params?.materialAttributeList?.split(',') || [],
    materialTypeList: props.params?.materialTypeList?.split(',') || [],
  });
  await getData();
});
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <ViewModal class="h-full w-10/12" />
    <EditModal class="h-full w-10/12" />
    <Grid>
      <template #toolbar-actions>
        <ElButton
          type="primary"
          @click="onAdd"
          v-access:code="'wm:inboundreq:submit'"
        >
          新增
        </ElButton>
      </template>
      <template #form-materialCategoryList>
        <ElCascader
          class="w-full"
          :props="propsConfig"
          v-model="materialCategory"
          :options="materialCategoryData"
          :max-collapse-tags="1"
          collapse-tags
          filterable
          collapse-tags-tooltip
          clearable
        />
      </template>
      <template #form-submitTime>
        <ElDatePicker
          v-model="submitTime.submitStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, submitTime.submitEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="submitTime.submitEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                submitTime.submitStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #form-finishTime>
        <ElDatePicker
          v-model="finishTime.finishStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, finishTime.finishEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="finishTime.finishEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                finishTime.finishStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #submitUserName="{ row }">
        <span>{{ row.submitUserName }}</span>
        <span v-if="row.submitUserDeptName">
          ({{ row.submitUserDeptName }})
        </span>
      </template>
      <template #docStatusLabel="{ row }">
        <ElTag size="small" :type="docStatusDict[row.docStatus]">
          {{ row.docStatusLabel }}
        </ElTag>
      </template>
      <template #CellOperation="{ row }">
        <ElButton link size="small" @click="onView(row)" type="info">
          查看
        </ElButton>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            :loading="exportLoading"
            circle
            @click="exportHandle"
            v-access:code="'wm:inboundreq:export:list:item'"
          >
            <template #icon>
              <IconFont name="xiazai" />
            </template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
