import type { OnActionClickParams, VxeGridProps } from '@girant/adapter';

import { h } from 'vue';

import MaterialEditField from '../../../components/material-edit-field/index.vue';
// 定义 row 的类型
interface RowType {
  materialId: string;
  materialSpecs: string;
  baseUnitLabel: number;
  warehouseId: string;
  locationId: string;
  batchNumber: boolean;
  inventory: string;
  quantity: string[];
  inventoryAfter: string[];
}

/** 物料信息 */
export const useGridOptions = (
  onActionClick: (e: OnActionClickParams) => void,
  onMaterialClick: (row: RowType, materialId: string) => void,
): VxeGridProps => {
  return {
    columns: [
      { title: '序号', type: 'seq', width: 50 },
      {
        editRender: {},
        field: 'materialId',
        slots: {
          default: ({ row }) => {
            return h(MaterialEditField, {
              modelValue: row.materialId,
              editable: false,
              pictureFileId: row.pictureFileId,
            });
          },
          edit: ({ $table, row }) => {
            return h(MaterialEditField, {
              modelValue: row.materialId,
              onChange: async (materialId, selectedItems) => {
                onMaterialClick(row, materialId);
                if (!materialId) {
                  $table.setRow(row, {
                    materialId: undefined,
                    materialSpecs: undefined,
                    baseUnitLabel: undefined,
                    warehouseId: undefined,
                    locationId: undefined,
                    batchNumber: undefined,
                    quantity: undefined,
                  });
                  return;
                }
                const materialDetailRes = selectedItems;
                const rowData = {
                  ...materialDetailRes,
                  materialId: {
                    materialId,
                    materialName: materialDetailRes?.materialName,
                  },
                };
                $table.setRow(row, rowData);
              },
              valueKey: 'materialId',
            });
          },
        },
        title: '物料选择',
        width: 180,
      },
      {
        field: 'materialCode',
        title: '物料编号',
        width: 140,
      },
      {
        field: 'materialSpecs',
        title: '规格型号',
        width: 180,
        showOverflow: false,
      },
      {
        field: 'baseUnitLabel',
        title: '基本单位',
        width: 110,
      },
      {
        slots: {
          default: 'warehouseId',
        },
        field: 'warehouseId',
        title: '仓库',
        minWidth: 180,
      },
      {
        slots: {
          default: 'locationId',
        },
        field: 'locationId',
        title: '库位',
        minWidth: 180,
      },
      {
        slots: {
          default: 'batchNumber',
        },
        field: 'batchNumber',
        title: '批次号',
        width: 200,
      },
      {
        slots: {
          default: 'inventory',
        },
        field: 'inventory',
        title: '现库存量',
        width: 110,
      },
      {
        slots: {
          default: 'quantity',
        },
        field: 'quantity',
        title: '调整数量',
        width: 180,
      },
      {
        slots: {
          default: 'inventoryAfter',
        },
        field: 'inventoryAfter',
        title: '调整后数量',
        width: 110,
      },
      {
        align: 'center',
        cellRender: {
          attrs: {
            onClick: onActionClick,
          },
          name: 'CellOperation',
          options: ['delete'],
        },
        title: '操作',
        width: 60,
        fixed: 'right',
      },
    ],
    editRules: {
      materialId: [
        { message: '物料不能为空', required: true, trigger: 'blur' },
      ],
      warehouseId: [
        { message: '仓库不能为空', required: true, trigger: 'blur' },
      ],
      locationId: [
        { message: '库位不能为空', required: true, trigger: 'blur' },
      ],
      batchNumber: [
        {
          validator: (value: any) => {
            if (value.row.quantity < 0 && !value.row.batchNumber) {
              return new Error('调整数量为负数时,批次号不能为空');
            }
          },
          trigger: 'blur',
        },
      ],
      quantity: [
        { message: '调整数量不能为空', required: true, trigger: 'blur' },
        {
          validator: ({ cellValue }) => {
            if (cellValue === 0) {
              return new Error('调整数量不能为0');
            }
          },
          trigger: 'blur',
        },
      ],
    },
    minHeight: 150,
  };
};
