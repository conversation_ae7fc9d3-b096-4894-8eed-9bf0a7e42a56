<script setup lang="ts">
import { ref } from 'vue';

// import MaterialForm from './material-form/index.vue';
import LockDetailsForm from './lock-details-form/index.vue';
import LockForm from './lock-form/index.vue';
import UnLockDetailsForm from './unlock-details-form/index.vue';

const props = defineProps({
  isView: {
    type: Boolean,
    default: false,
  },
  /** 库存锁库ID*/
  blockId: {
    type: String,
    default: '',
  },
  /** 物料id */
  materialId: {
    type: String,
    default: '',
  },
  /** 是否有效 */
  isValid: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(['submitSuccess']);

/** 锁库表单ref */
const lockFormRef = ref();
/** 提交表单 新增 */
const onSubmit = async () => {
  lockFormRef.value!.formApi.validateAndSubmitForm();
};

defineExpose({
  onSubmit,
  props,
});
</script>

<template>
  <LockForm
    v-if="!isView"
    ref="lockFormRef"
    @submit-success="emit('submitSuccess')"
  />
  <div v-else>
    <LockDetailsForm :block-id="blockId" />
    <UnLockDetailsForm v-if="!isValid" :block-id="blockId" />
  </div>
</template>
