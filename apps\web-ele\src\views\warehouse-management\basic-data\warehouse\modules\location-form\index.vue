<script setup lang="ts">
import type { LocationInfoApi } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { ElCard, ElMessage, ElScrollbar } from 'element-plus';

import {
  getLocationDetail,
  lockLocation,
  unlockLocation,
} from '#/api/warehouse-management';

import { confirm } from '../method';
import locationFormEdit from './form-edit/index.vue';
import locationFormView from './form-view/index.vue';

const props = defineProps({
  isView: {
    type: Boolean,
    default: false,
  },
  /** 库位id */
  locationId: {
    type: String,
    default: '',
  },
  /** 库位编码 */
  locationCode: {
    type: String,
    default: '',
  },
});
const emits = defineEmits(['formSubmitSuccess']);
/** 库位表单 编辑*/
const locationEditRef = ref<InstanceType<typeof locationFormEdit>>();
/** 库位信息 */
const locationInfo = ref<LocationInfoApi.LocationDetail>();
/** 是否锁定 */
const isLock = ref(false);
/** 编辑 */
const isEdit = ref(false);
/** 查看 */
const thisView = ref(props.isView);
/** 当前id */
const currentId = ref(props.locationId);
/** 当前code */
const currentCode = ref(props.locationCode);
const loading = ref(false);

/** 获取库位信息 */
const getLocationInfo = async () => {
  try {
    loading.value = true;
    const resLocation = await getLocationDetail(
      currentId.value,
      currentCode.value,
    );
    locationInfo.value = resLocation;
    isLock.value = resLocation?.isLock;
  } catch {
    ElMessage.error('获取库位信息失败');
  } finally {
    loading.value = false;
  }
};

/** 提交表单 */
const submit = () => {
  // 判断是否有id 有id则是编辑 否则是新增
  if (currentId.value || currentCode.value) {
    locationEditRef.value?.onSubmitEdit();
  } else {
    locationEditRef.value?.onSubmit();
  }
};
/** 取消表单 */
const cancel = () => {
  if (currentId.value || currentCode.value) {
    // 切换回查看状态
    thisView.value = true;
  } else {
    // 清空表单
    locationEditRef.value?.clearForm();
  }
};

/** 编辑按钮 */
const edit = () => {
  isEdit.value = true;
  thisView.value = false;
};
/** 锁定 */
const lock = async () => {
  try {
    await confirm('确认锁定吗？', '提示');
    loading.value = true;
    await lockLocation(currentId.value);
    ElMessage.success('锁定成功');
    getLocationInfo();
    emits('formSubmitSuccess');
  } catch (error) {
    console.error(error);
    // ElMessage.error('锁定失败');
  } finally {
    loading.value = false;
  }
};
/** 解锁 */
const unlock = async () => {
  try {
    await confirm('确认解锁吗？', '提示');
    loading.value = true;
    await unlockLocation(currentId.value);
    ElMessage.success('解锁成功');
    getLocationInfo();
    emits('formSubmitSuccess');
  } catch {
    ElMessage.error('解锁失败');
  } finally {
    loading.value = false;
  }
};
/** 提交成功 */
const submitSuccess = (locationId: string, locationCode: string) => {
  if (locationId) {
    currentId.value = locationId;
  }
  if (locationCode) {
    currentCode.value = locationCode;
  }
  // 切换回查看状态
  thisView.value = true;
  emits('formSubmitSuccess');
};
onMounted(() => {
  if (currentId.value || currentCode.value) {
    getLocationInfo();
  }
});
defineExpose({
  submit,
  cancel,
  edit,
  lock,
  unlock,
  submitSuccess,
  locationInfo,
  isLock,
  locationRef: locationEditRef,
});
</script>

<template>
  <ElCard
    v-loading="loading"
    :is-footer="false"
    class="flex h-full flex-col justify-between !pb-[30px]"
    shadow="never"
    body-class="h-full !p-[10px] w-full"
    style="border: 1px solid"
  >
    <ElScrollbar>
      <locationFormView
        v-if="thisView"
        :location-id="currentId"
        :location-code="currentCode"
        :is-lock="isLock"
      />
      <locationFormEdit
        v-else
        :location-id="currentId"
        :location-code="currentCode"
        ref="locationEditRef"
        @user-form-submit-success="submitSuccess"
      />
    </ElScrollbar>

    <div class="flex min-h-[40px] justify-end">
      <ElButton
        type="primary"
        @click="edit"
        v-if="thisView"
        v-access:code="'wm:location:edit:mod'"
      >
        编辑
      </ElButton>
      <ElButton
        type="primary"
        @click="unlock"
        v-if="isLock && thisView"
        v-access:code="'wm:location:lock:unlock'"
      >
        解锁
      </ElButton>
      <ElButton
        type="primary"
        @click="lock"
        v-if="!isLock && thisView"
        v-access:code="'wm:location:lock:lock'"
      >
        锁库位
      </ElButton>
      <ElButton
        type="primary"
        @click="submit"
        v-if="!thisView && (currentId || currentCode)"
        v-access:code="'wm:location:edit:mod'"
      >
        提交
      </ElButton>
      <ElButton
        type="primary"
        @click="submit"
        v-if="!thisView && (!currentId || !currentCode)"
        v-access:code="'wm:location:edit:add'"
      >
        提交
      </ElButton>
      <ElButton type="info" @click="cancel" v-if="!thisView"> 取消 </ElButton>
    </div>
  </ElCard>
</template>
