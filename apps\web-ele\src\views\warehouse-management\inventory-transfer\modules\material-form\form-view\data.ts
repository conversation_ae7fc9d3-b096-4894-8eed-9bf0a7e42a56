import type { VxeTableGridOptions } from '@girant/adapter';

export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      slots: {
        default: 'materialName',
      },
      title: '物料',
      field: 'materialName',
      minWidth: 200,
    },
    {
      title: '规格类型',
      field: 'materialSpecs',
      minWidth: 200,
    },
    {
      title: '基本单位',
      field: 'baseUnitLabel',
      minWidth: 100,
    },
    {
      title: '数量',
      field: 'transferQuantity',
      minWidth: 100,
    },
  ];
}
