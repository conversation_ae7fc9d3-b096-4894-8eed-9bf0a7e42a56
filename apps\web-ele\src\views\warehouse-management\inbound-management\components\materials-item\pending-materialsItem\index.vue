<script setup lang="ts">
import type { PropType } from 'vue';

import type { InBoundDocApi } from '#/api/warehouse-management';
import type { WarehouseListForMaterialListApi } from '#/api/warehouse-management/index';

import { computed, nextTick, onMounted, ref } from 'vue';

import { Delete, Plus } from '@element-plus/icons-vue';
import { ElButton, ElMessage, ElMessageBox } from 'element-plus';

import Info from '../components/Info.vue';
import WarehouseItem from './warehouse-item/index.vue';

const props = defineProps({
  // 物料信息（包含仓库、库位信息）
  materialItemData: {
    type: Object as PropType<InBoundDocApi.InBoundItem>,
    default: () => ({}),
  },
  // 源单据类型标识
  origDocTypeCode: {
    type: String,
    default: '',
  },
  warehouseListForMaterial: {
    type: Object as PropType<WarehouseListForMaterialListApi.WarehouseListForMaterialList>,
    default: () => ({}),
  },
});

const emits = defineEmits(['handleMaterialCode']);

export interface LocationItemType {
  locationId: string;
  unitPrice: number;
  batchNumber: string;
  applyQuantity: number;
}

export interface WarehouseItemDataType {
  warehouseId: string;
  locationList: LocationItemType[];
  timestamp: number;
}

// 仓库item数据列表
const warehouseItemList = ref<WarehouseItemDataType[]>([]);

// 默认仓库ID,没有就拿仓库列表第一个
const defaultWarehouseId = ref<string>('');

// 已选中的仓库id列表
const currentSelectedWarehouseIdList = ref<Array<string>>([]);

// 仓库改变
const warehouseChange = () => {
  currentSelectedWarehouseIdList.value = getAllSelectedWarehouseId();
};

// 获取全部已选中的仓库id
const getAllSelectedWarehouseId = () => {
  return warehouseItemRef.value.map((item) => item.currentWarehouseId);
};

export interface SelectWarehouseListType {
  disabled: boolean;
  label: string;
  value: string;
  /* 可用量 */
  availableInventory: number;
  /* 库存量 */
  inventory: number;
}

// 仓库下拉框数据：处理可入库的仓库列表，不能选择已选中的仓库
const selectWarehouseList = computed<SelectWarehouseListType[]>(() => {
  return props.warehouseListForMaterial.warehouseList.map((item) => {
    return {
      disabled: currentSelectedWarehouseIdList.value.includes(
        item.warehouseId.toString(),
      ),
      label: item.warehouseName,
      value: item.warehouseId,
      availableInventory: item.availableInventory,
      inventory: item.inventory,
    };
  });
});

onMounted(() => {
  defaultWarehouseId.value =
    props.warehouseListForMaterial.warehouseList.find(
      (item) =>
        item.warehouseId === props.warehouseListForMaterial.mainWarehouseId,
    )?.warehouseId ||
    props.warehouseListForMaterial.warehouseList[0]?.warehouseId ||
    '';
  currentSelectedWarehouseIdList.value = [defaultWarehouseId.value];

  // 初始化一条仓库item数据
  const initWarehouseItemData = {
    warehouseId: defaultWarehouseId.value,
    // 时间戳
    timestamp: Date.now(),
    locationList: [
      {
        applyQuantity: props.materialItemData.applyQuantitySum,
        locationId: '',
        batchNumber: '',
        unitPrice: 0,
      },
    ],
  };
  warehouseItemList.value = [initWarehouseItemData];
});

// 填入数量
const entryQuantitySum = ref(0);
// 计算填入数量
const getEntryQuantitySum = async () => {
  const fillQuantityList = warehouseItemRef.value.map(
    (item) => item.currentWarehouseFillQuantity,
  );
  entryQuantitySum.value = fillQuantityList.reduce(
    (acc: number, item: number) => acc + item,
    0,
  );
};
// 申请数量与填入数量对比，数量不相同不能提交，返回true/false
const isApplyQuantitySumEqual = computed(() => {
  return entryQuantitySum.value === props.materialItemData.applyQuantitySum;
});

// 所有仓库项ref
const warehouseItemRef = ref<InstanceType<typeof WarehouseItem>[]>([]);

// 校验
const validateFormData = async () => {
  const validateFormDataRes = await Promise.all(
    warehouseItemRef.value.map((item) => item.validateFormData()),
  );
  return validateFormDataRes;
};

// 获取所有仓库项的表单数据
const getWarehouseItemData = async (hasWarehouseId: boolean = false) => {
  if (warehouseItemRef.value.length === 0) {
    return [];
  }
  const formData = await Promise.all(
    warehouseItemRef.value.map((item) => item.getFormData(hasWarehouseId)),
  );
  if (formData.length === 0) {
    entryQuantitySum.value = 0;
    return;
  }
  // 扁平化
  const flatFormData = formData.flat();
  return flatFormData;
};

// 增加仓库item
const addWarehouseItem = async () => {
  if (
    await ElMessageBox.confirm('确定添加仓库吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    warehouseItemList.value.push({
      warehouseId: '',
      timestamp: Date.now(),
      locationList: [
        {
          applyQuantity: 0,
          locationId: '',
          batchNumber: '',
          unitPrice: 0,
        },
      ],
    });
  }
};

// 删除仓库item
const deleteWarehouseItem = async (timestamp: number) => {
  if (
    await ElMessageBox.confirm('确定删除吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    warehouseItemList.value = warehouseItemList.value.filter(
      (item) => item.timestamp !== timestamp,
    );

    nextTick(() => {
      getEntryQuantitySum();
      warehouseChange();
    });
  }
};

// 将表格数据处理成需要提交的数据，这里需要校验表格
const getSubData = async () => {
  const validateFormDataRes = await validateFormData();
  if (validateFormDataRes.includes(false)) {
    ElMessage.error('请先检查表格数据');
    return false;
  }
  const formData = await getWarehouseItemData(true);

  const subData = formData.map((item: any) => {
    return {
      ...item,
      materialId: props.materialItemData.materialId,
    };
  });

  return subData;
};

const handleMaterialCode = () => {
  emits('handleMaterialCode', props.materialItemData.materialId);
};

defineExpose({
  getSubData,
  isApplyQuantitySumEqual,
});
</script>
<template>
  <div class="rounded-lg bg-white">
    <Info
      :material-item-data="materialItemData"
      @handle-material-code="handleMaterialCode"
    >
      <template #extra>
        <div class="flex items-center text-base text-gray-800">
          <span>申请入库 / 填入数量：</span>
          <span class="font-medium">{{
            materialItemData.applyQuantitySum
          }}</span>
          <span class="mx-1">/</span>
          <span
            class="font-medium"
            :class="[
              entryQuantitySum === materialItemData.applyQuantitySum
                ? 'text-green-500'
                : 'text-red-500',
            ]"
          >
            {{ entryQuantitySum }}
          </span>
          <span class="ml-1 text-gray-500">
            ({{ materialItemData.baseUnitLabel }})
          </span>
        </div>
      </template>
    </Info>

    <div class="mt-2 space-y-3">
      <div class="relative">
        <div class="absolute right-0 top-[-25px]">
          <ElButton
            type="primary"
            link
            @click="addWarehouseItem"
            v-if="
              warehouseItemList.length <
              warehouseListForMaterial?.warehouseList?.length
            "
          >
            <el-icon class="!text-base">
              <Plus />
            </el-icon>
            添加仓库
          </ElButton>
        </div>
        <div class="mt-4 space-y-3">
          <template v-for="item in warehouseItemList" :key="item.timestamp">
            <WarehouseItem
              ref="warehouseItemRef"
              :warehouse-item-data="item"
              :select-warehouse-list="selectWarehouseList"
              @entry-quantity-change="getEntryQuantitySum"
              @warehouse-change="warehouseChange"
              :material-id="materialItemData.materialId"
              :default-location-id="
                warehouseListForMaterial?.mainLocationId || ''
              "
            >
              <template #delete-wrapper v-if="warehouseItemList.length > 1">
                <ElButton
                  type="danger"
                  link
                  size="small"
                  @click="deleteWarehouseItem(item.timestamp)"
                  title="删除仓库"
                >
                  <el-icon class="!text-base">
                    <Delete />
                  </el-icon>
                </ElButton>
              </template>
            </WarehouseItem>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
