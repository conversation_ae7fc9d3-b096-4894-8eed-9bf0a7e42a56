<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import MaterialForm from '#/views/warehouse-management/basic-data/material/modules/material-form/index.vue';

/** 共享数据 */
const shareData = ref();

const [Modal, modalApi] = useVbenModal({
  onClosed() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      shareData.value = modalApi.getData<Record<string, any>>();
    }
  },
});
</script>

<template>
  <Modal>
    <MaterialForm :material-id="shareData.materialId" />
  </Modal>
</template>
