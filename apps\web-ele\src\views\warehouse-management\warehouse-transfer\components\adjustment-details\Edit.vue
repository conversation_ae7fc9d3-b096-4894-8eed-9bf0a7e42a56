<script setup lang="ts">
import type { OnActionClickParams } from '@girant/adapter';

import type {
  LocationInfoApi,
  TransferQueryApi,
} from '#/api/warehouse-management';

import { nextTick, onMounted, ref, toRef, watch } from 'vue';

// @ts-ignore
import { DynamicTable } from '@girant-web/dynamic-table-component';
import { ElMessage } from 'element-plus';

import {
  getLocationList,
  getTransferDocDetail,
} from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import LocationAbatchNumber from '../LocationAbatchNumber.vue';
import { useGridOptions } from './Edit';

const props = defineProps({
  /** 调拨单号 */
  transferDocNumber: {
    type: String,
    default: '',
  },
  /** 调拨单id */
  transferDocId: {
    type: String,
    default: '',
  },
  /** 仓库id */
  warehouseId: {
    type: String,
    default: '',
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const currentWarehouseId = ref<string>('');
onMounted(() => {
  currentWarehouseId.value = props.warehouseId;
});

const transferDocDetail = ref<TransferQueryApi.GetTransferDocDetailResponse>(
  {} as TransferQueryApi.GetTransferDocDetailResponse,
);

const locationList = ref<LocationInfoApi.LocationDetail[]>([]);

const getLocationListHandle = async (warehouseId: string) => {
  const res = await getLocationList({
    warehouseId,
    isEnable: true,
    isLock: false,
  });
  locationList.value = res;
  return res;
};

watch(
  () => props.warehouseId,
  async (newVal) => {
    currentWarehouseId.value = newVal;
    locationList.value = [];
    gridTable.value.setTableData([]);
    if (!newVal) {
      return;
    }
    await getLocationListHandle(newVal);
  },
);

// 获取详情
const getTransferDocDetailHandle = async () => {
  try {
    const transferDocDetailRes = await getTransferDocDetail({
      transferDocId: props.transferDocId,
      transferDocNumber: props.transferDocNumber,
      isQueryItem: true,
    });
    transferDocDetail.value = transferDocDetailRes;
    return transferDocDetailRes;
  } catch {
    ElMessage.error('获取调拨单据失败');
    return {} as TransferQueryApi.GetTransferDocDetailResponse;
  }
};

/** 表格ref */
const gridTable = ref<DynamicTable>();
const GridApi = ref<any>();

onMounted(() => {
  nextTick(async () => {
    const [, VbenGridApi] = gridTable.value.getGridTableIns();
    VbenGridApi.value = VbenGridApi;
    GridApi.value = VbenGridApi.grid;

    if (props.isEdit) {
      if (props.transferDocId || props.transferDocNumber) {
        await getTransferDocDetailHandle();
        currentWarehouseId.value = transferDocDetail.value.warehouseId;
        await getLocationListHandle(currentWarehouseId.value);
        const data = transferDocDetail.value.transferItemList.map((item) => {
          return {
            ...item,
            materialId: {
              materialId: item.materialId,
              materialName: item.materialName,
            },
            locationAbatchNumber: `${item.oldLocationId}#${item.batchNumber}`,
          };
        });
        gridTable.value.setTableData(data);
      } else {
        ElMessage.error('调拨单号或调拨单ID不能为空');
      }
    } else {
      gridTable.value.setTableData([]);
    }
  });
});

const isEditRow = (row: any) => {
  return GridApi.value.isEditByRow(row);
};

/** 操作 */
function onActionClick(e: OnActionClickParams) {
  switch (e.code) {
    case 'delete': {
      gridTable.value.removeRow(e.row);
      break;
    }
  }
}

/** 表单表格*/
const gridOptions = useGridOptions({
  onActionClick,
  warehouseId: toRef(currentWarehouseId, 'value'),
});

/** 获取表单数据 */
const getFormData = async (): Promise<
  false | TransferQueryApi.TransferItem[]
> => {
  const validateRes = await gridTable.value.tableValidate();
  if (validateRes) {
    return false;
  }

  const data = await gridTable.value.getTableFullData();
  if (data.length === 0) {
    return [] as TransferQueryApi.TransferItem[];
  }

  // 提取出需要的字段
  const result = data?.map((item: any) => {
    const {
      materialId,
      transferQuantity,
      targetLocationId,
      oldLocationId,
      batchNumber,
    } = item;
    return {
      materialId: materialId?.materialId,
      transferQuantity,
      oldLocationId,
      targetLocationId,
      batchNumber,
    };
  });
  return result;
};

const locationAbatchNumberChange = ({
  row,
  value,
}: {
  row: any;
  value: any;
}) => {
  row.locationAbatchNumber = value.value;
  row.oldLocationId = value.locationId;
  row.batchNumber = value.batchNumber;
  row.inventory = value.inventory;
  // $table.setRow(row, { transferQuantity: null });
};

defineExpose({
  getFormData,
});
</script>
<template>
  <FormCard :is-footer="false" title="调拨明细">
    <DynamicTable
      ref="gridTable"
      :grid-options="gridOptions"
      class="border"
      :grid-table-data="[]"
      :table-options="{
        gridClass: 'pr-0 pl-0 pt-0',
      }"
    >
      <template #locationAbatchNumber="{ row }">
        <LocationAbatchNumber
          v-if="row.materialId?.materialId && currentWarehouseId"
          :model-value="row.locationAbatchNumber"
          :material-id="row.materialId?.materialId"
          :warehouse-id="currentWarehouseId"
          @change="
            locationAbatchNumberChange({
              row,
              value: $event,
            })
          "
        />
        <span v-else>请先选择物料</span>
      </template>

      <template #targetLocationId="{ row }">
        <ElSelect
          :disabled="
            currentWarehouseId && row.materialId?.materialId && !isEditRow(row)
          "
          v-model="row.targetLocationId"
          placeholder="请选择目标库位"
        >
          <ElOption
            :disabled="item.locationId === row.oldLocationId"
            v-for="item in locationList"
            :key="item.locationId"
            :label="item.locationName"
            :value="item.locationId"
          />
        </ElSelect>
      </template>
    </DynamicTable>
  </FormCard>
</template>
<style scoped>
:deep(.vxe-grid--bottom-wrapper > div:first-child) {
  display: flex;
  justify-content: center;
  margin-top: 0;
}
</style>
