<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

// @ts-ignore
import { DynamicTable } from '@girant-web/dynamic-table-component';
import { ElButton, ElInputNumber, ElMessage, ElPopconfirm } from 'element-plus';

import { getWareTransferDocDetail } from '#/api';
import FormCard from '#/components/form-card/Index.vue';

import { useGridOptions, viewMaterialName } from './data';

const props = defineProps({
  /** 库存调拨单据ID */
  transferDocId: {
    type: String,
    default: '',
  },
  /** 库存调拨单据编号 */
  transferDocNumber: {
    type: String,
    default: '',
  },
  /** 调出仓库 */
  oldWarehouseId: {
    type: String,
    default: '',
  },
  /** 调入仓库 */
  targetWarehouseId: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
/** 表格ref */
const gridTable = ref<DynamicTable>();
// 创建响应式的仓库ID变量（用于跟踪变化）
const warehouseIds = ref({
  old: props.oldWarehouseId,
  target: props.targetWarehouseId,
});
/** 编辑表单表格*/
const gridOptions = ref(useGridOptions(warehouseIds.value));

onMounted(() => {
  if (props.transferDocId || props.transferDocNumber) {
    getData();
  }
});

/** 获取拆卸单据信息 */
const getData = async () => {
  try {
    loading.value = true;
    // 获取拆卸单据信息
    const data = await getWareTransferDocDetail(
      props.transferDocId,
      props.transferDocNumber,
      true,
    );
    // 处理数据
    data.transferItemList?.forEach((item: any) => {
      item.materialId = {
        materialId: item.materialId,
        materialName: viewMaterialName(item),
      };
    });
    warehouseIds.value.old = data.oldWarehouseId;
    warehouseIds.value.target = data.targetWarehouseId;
    // 赋值
    await gridTable.value.setTableData(data.transferItemList);
    useGridOptions(warehouseIds.value);
  } catch (error) {
    console.error(error);
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

/** 监听仓库变化 */
watch(
  () => [props.oldWarehouseId, props.targetWarehouseId],
  ([newOld, newTarget]) => {
    warehouseIds.value.old = newOld ?? '';
    warehouseIds.value.target = newTarget ?? '';
    gridOptions.value = useGridOptions(warehouseIds.value);
  },
  { immediate: true },
);

/** 操作 */
const onActionClick = (row: any) => {
  gridTable.value.removeRow(row);
};

/** 校验 */
const validateForm = async () => {
  // 校验表单
  const isValid = await gridTable.value.tableValidate();
  if (isValid) {
    return false;
  }
  return true;
};

/** 提交 */
const getFormData = async () => {
  const data = await gridTable.value.getTableData();
  // 提取出需要的字段
  const result = data.map((item: any) => ({
    materialId: item.materialId.materialId,
    transferQuantity: item.transferQuantity,
  }));
  return result;
};

/** 对外开放方法 */
defineExpose({
  getFormData,
  validateForm,
});
</script>

<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>调拨明细</span>
    </template>

    <DynamicTable ref="gridTable" :grid-options="gridOptions" class="border">
      <template #transferQuantity="{ row }">
        <ElInputNumber v-model="row.transferQuantity" :min="1" />
      </template>
      <template #CellOperation="{ row }">
        <ElPopconfirm title="删除" @confirm="onActionClick(row)">
          <template #reference>
            <ElButton link size="small" type="danger"> 删除 </ElButton>
          </template>
        </ElPopconfirm>
      </template>
    </DynamicTable>
  </FormCard>
</template>
