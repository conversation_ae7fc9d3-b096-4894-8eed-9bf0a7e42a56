/**
 * 日期格式化工具
 * @description 提供常用的日期格式化、转换和计算方法
 */

/**
 * 将日期格式化为指定格式的字符串
 * @param date 日期对象或日期字符串
 * @param format 目标格式，默认'YYYY-MM-DD'
 * @returns 格式化后的日期字符串
 */
export const formatDate = (
  date: Date | string,
  format = 'YYYY-MM-DD',
): string => {
  if (!date) return '';
  const targetDate = typeof date === 'string' ? new Date(date) : date;
  const {
    getFullYear: year,
    getMonth: month,
    getDate: day,
    getHours: hour,
    getMinutes: minute,
    getSeconds: second,
  } = targetDate;
  const pad = (num: number) => num.toString().padStart(2, '0');

  return format
    .replace('YYYY', year.call(targetDate).toString())
    .replace('MM', pad(month.call(targetDate) + 1))
    .replace('DD', pad(day.call(targetDate)))
    .replace('HH', pad(hour.call(targetDate)))
    .replace('mm', pad(minute.call(targetDate)))
    .replace('ss', pad(second.call(targetDate)));
};

/**
 * 将字符串转换为Date对象
 * @param dateString 日期字符串，支持格式：YYYY-MM-DD、YYYY/MM/DD、YYYY-MM-DD HH:mm:ss等
 * @returns 转换后的Date对象，如果转换失败返回当前日期
 */
export const stringToDate = (dateString: string): Date => {
  if (!dateString) return new Date();

  if (dateString.includes(' ')) {
    return new Date(dateString.replaceAll('-', '/'));
  }

  if (dateString.includes('-')) {
    const parts = dateString.split('-').map(Number);
    if (parts.length === 3) {
      const [year, month, day] = parts;
      if (!Number.isNaN(year) && !Number.isNaN(month) && !Number.isNaN(day)) {
        return new Date(year as number, (month as number) - 1, day as number);
      }
    }
  }

  if (dateString.includes('/')) {
    const parts = dateString.split('/').map(Number);
    if (parts.length === 3) {
      const [year, month, day] = parts;
      if (!Number.isNaN(year) && !Number.isNaN(month) && !Number.isNaN(day)) {
        return new Date(year as number, (month as number) - 1, day as number);
      }
    }
  }

  return new Date(dateString);
};

/**
 * 获取两个日期之间的天数差
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 天数差的绝对值
 */
export const getDaysDifference = (
  startDate: Date | string,
  endDate: Date | string,
): number => {
  const sDate = typeof startDate === 'string' ? new Date(startDate) : startDate;
  const eDate = typeof endDate === 'string' ? new Date(endDate) : endDate;
  const timeDiff = Math.abs(eDate.getTime() - sDate.getTime());
  return Math.ceil(timeDiff / (1000 * 3600 * 24));
};

/**
 * 给日期添加指定的天数
 * @param date 基准日期
 * @param days 要添加的天数，可以是负数
 * @returns 添加天后的新日期
 */
export const addDays = (date: Date | string, days: number): Date => {
  const targetDate = typeof date === 'string' ? new Date(date) : new Date(date);
  targetDate.setDate(targetDate.getDate() + days);
  return targetDate;
};

/**
 * 判断一个日期是否在另一个日期之前
 * @param date1 第一个日期
 * @param date2 第二个日期，默认为当前日期
 * @returns 如果date1在date2之前返回true，否则返回false
 */
export const isBefore = (
  date1: Date | string,
  date2: Date | string = new Date(),
): boolean => {
  if (!date2) return false;
  const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
  const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
  return d1.getTime() < d2.getTime();
};

/**
 * 判断一个日期是否在另一个日期之后
 * @param date1 第一个日期
 * @param date2 第二个日期，默认为当前日期
 * @returns 如果date1在date2之后返回true，否则返回false
 */
export const isAfter = (
  date1: Date | string,
  date2: Date | string = new Date(),
): boolean => {
  console.log(date1, date2);
  if (!date2) return false;
  const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
  const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
  return d1.getTime() > d2.getTime();
};

/**
 * 判断两个日期是否相同（忽略时间部分）
 * @param date1 第一个日期
 * @param date2 第二个日期
 * @returns 如果两个日期相同返回true，否则返回false
 */
export const isSameDate = (
  date1: Date | string,
  date2: Date | string,
): boolean => {
  const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
  const d2 = typeof date2 === 'string' ? new Date(date2) : date2;

  return (
    d1.getFullYear() === d2.getFullYear() &&
    d1.getMonth() === d2.getMonth() &&
    d1.getDate() === d2.getDate()
  );
};

/**
 * 获取当前日期所在周的第一天（默认为周一）
 * @param date 基准日期，默认为当前日期
 * @param weekStartDay 一周的第一天是星期几，0表示周日，1表示周一，默认为1
 * @returns 所在周的第一天的日期
 */
export const getFirstDayOfWeek = (
  date: Date | string = new Date(),
  weekStartDay = 1,
): Date => {
  const targetDate = typeof date === 'string' ? new Date(date) : new Date(date);
  const day = targetDate.getDay();
  const diff = (day < weekStartDay ? 7 : 0) + day - weekStartDay;

  targetDate.setDate(targetDate.getDate() - diff);
  return targetDate;
};

/**
 * 获取当前日期所在月的第一天
 * @param date 基准日期，默认为当前日期
 * @returns 所在月的第一天的日期
 */
export const getFirstDayOfMonth = (date: Date | string = new Date()): Date => {
  const targetDate = typeof date === 'string' ? new Date(date) : new Date(date);
  targetDate.setDate(1);
  return targetDate;
};

/**
 * 获取当前日期所在月的最后一天
 * @param date 基准日期，默认为当前日期
 * @returns 所在月的最后一天的日期
 */
export const getLastDayOfMonth = (date: Date | string = new Date()): Date => {
  const targetDate = typeof date === 'string' ? new Date(date) : new Date(date);
  targetDate.setMonth(targetDate.getMonth() + 1);
  targetDate.setDate(0);
  return targetDate;
};

/**
 * 获取一个月有多少天
 * @param year 年份
 * @param month 月份（1-12）
 * @returns 该月的天数
 */
export const getDaysInMonth = (year: number, month: number): number => {
  if (Number.isNaN(year) || Number.isNaN(month)) {
    throw new TypeError('Invalid year or month');
  }
  return new Date(year, month, 0).getDate();
};

/**
 * 将Unix时间戳转换为Date对象
 * @param timestamp 时间戳（毫秒）
 * @returns 转换后的Date对象
 */
export const timestampToDate = (timestamp: number): Date => {
  return new Date(timestamp);
};

/**
 * 将Date对象转换为Unix时间戳（毫秒）
 * @param date 日期对象
 * @returns 时间戳（毫秒）
 */
export const dateToTimestamp = (date: Date | string): number => {
  const targetDate = typeof date === 'string' ? new Date(date) : date;
  return targetDate.getTime();
};

/**
 * 检查一个日期是否是闰年
 * @param date 日期对象或年份数字
 * @returns 如果是闰年返回true，否则返回false
 */
export const isLeapYear = (date: Date | number | string): boolean => {
  if (typeof date === 'number') {
    return (date % 4 === 0 && date % 100 !== 0) || date % 400 === 0;
  }

  const year =
    typeof date === 'string'
      ? new Date(date).getFullYear()
      : date.getFullYear();

  return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
};

/**
 * 将时间转换成年月日中文格式
 * @param date 日期对象或日期字符串
 * @param options 配置选项
 * @param options.showWeekday 可选择显示星期，二零二三年五月三十日 星期五
 * @param options.useSimple 简化中文格式：2023 年 5 月 30 日
 * @returns 中文格式的日期字符串
 */
export const toChineseDate = (
  date: Date | string,
  options: {
    showWeekday?: boolean;
    useSimple?: boolean;
  } = {},
): string => {
  const { showWeekday = false, useSimple = false } = options;
  const targetDate = typeof date === 'string' ? new Date(date) : date;

  const year = targetDate.getFullYear();
  const month = targetDate.getMonth() + 1;
  const day = targetDate.getDate();
  const weekday = targetDate.getDay();

  const chineseNumbers = [
    '零',
    '一',
    '二',
    '三',
    '四',
    '五',
    '六',
    '七',
    '八',
    '九',
    '十',
  ];
  const chineseWeekdays = ['日', '一', '二', '三', '四', '五', '六'];

  const numberToChinese = (num: number): string => {
    if (useSimple) return num.toString();

    if (num < 10) {
      return chineseNumbers[num] as string;
    } else if (num === 10) {
      return '十';
    } else if (num < 20) {
      return `十${chineseNumbers[num % 10]}`;
    } else if (num < 100) {
      const tens = Math.floor(num / 10);
      const ones = num % 10;
      return `${tens === 1 ? '十' : chineseNumbers[tens]}十${ones === 0 ? '' : chineseNumbers[ones]}`;
    } else {
      return [...num.toString()]
        .map((digit) => chineseNumbers[Number.parseInt(digit, 10)])
        .join('');
    }
  };

  const chineseYear = `${numberToChinese(year)}年`;
  const chineseMonth = `${numberToChinese(month)}月`;
  const chineseDay = `${numberToChinese(day)}日`;

  let result = `${chineseYear}${chineseMonth}${chineseDay}`;

  if (showWeekday) {
    result += ` 星期${chineseWeekdays[weekday]}`;
  }

  return result;
};
