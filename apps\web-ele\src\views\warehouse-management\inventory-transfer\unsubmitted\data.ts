import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import type { WarehouseInfoApi } from '#/api';

import { markRaw } from 'vue';

import { ElInputTag } from 'element-plus';

import { getEnableWarehouseList, getEnumByName } from '#/api';

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'transferDocNumberList',
      label: '单据编号',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const transferTypeList = data.map((item: any) => ({
            label: item.enumLabel,
            value: item.enumValue,
          }));
          return transferTypeList;
        },
        api: () => {
          return getEnumByName('WmTransferTypeEnums');
        },
        maxCollapseTags: 1,
        filterable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'docCode',
      label: '调拨类型',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.EnableWarehouse[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getEnableWarehouseList();
        },
      },
      fieldName: 'oldWarehouseIdList',
      formItemClass: 'col-span-1',
      label: '调出仓库',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getEnableWarehouseList();
        },
      },
      fieldName: 'targetWarehouseIdList',
      formItemClass: 'col-span-1',
      label: '调入仓库',
    },
  ];
}

/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      slots: {
        default: 'transferDocNumber',
      },
      title: '单据编号',
      field: 'transferDocNumber',
      minWidth: 235,
    },
    {
      slots: {
        default: 'docStatusLabel',
      },
      title: '单据状态',
      field: 'docStatusLabel',
      minWidth: 110,
    },
    {
      title: '最后修改时间',
      field: 'modifyTime',
      minWidth: 150,
    },
    {
      title: '调出仓库',
      field: 'oldWarehouseName',
      minWidth: 150,
    },
    {
      title: '调入仓库',
      field: 'targetWarehouseName',
      minWidth: 150,
    },
    {
      align: 'center',
      slots: {
        default: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      minWidth: 185,
      title: '操作',
    },
  ];
}
