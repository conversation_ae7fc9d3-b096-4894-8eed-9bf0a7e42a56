<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import InboundViewForm from '../components/inbound-view-form/index.vue';

/** 共享数据 */
const shareData = ref();

const [Modal, modalApi] = useVbenModal({
  cancelText: '关闭',
  onClosed() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      shareData.value = modalApi.getData<Record<string, any>>();
    }
  },
});
</script>

<template>
  <Modal>
    <InboundViewForm
      :in-bound-doc-id="shareData.inBoundDocId"
      :in-bound-doc-number="shareData.inBoundDocNumber"
      :doc-status="shareData.docStatus"
    />
  </Modal>
</template>
