<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import OutboundCannelForm from '#/views/warehouse-management/outbound-management/components/close-bound/form-view/index.vue';

/** 共享数据 */
const shareData = ref();

const [Modal, modalApi] = useVbenModal({
  cancelText: '关闭',
  onClosed() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      shareData.value = modalApi.getData<Record<string, any>>();
    }
  },
});
</script>

<template>
  <Modal>
    <OutboundCannelForm
      :in-out-cancel-doc-id="shareData.inOutCancelDocId"
      :in-out-cancel-doc-number="shareData.inOutCancelDocNumber"
    />
  </Modal>
</template>
