<script setup lang="ts">
import type { StaffInfoType } from '#/api/common/staff';
import type { OutBound } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { useClipboard } from '@vueuse/core';
import { ElButton, ElMessage, ElScrollbar, ElTag } from 'element-plus';

import { getStaffInfo } from '#/api/common/staff';
import { getExecCode, getInOutReqDocDetail } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';
import StepProgress from '#/components/step-progress/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  /** 其它出入库申请单id */
  inOutReqDocId: {
    type: String,
    default: '',
  },
  /** 其它出入库申请单编号*/
  inOutReqDocNumber: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
/** 当前登录用户的员工信息 */
const staffData = ref<StaffInfoType>();
/** 当前表单数据 */
const formData = ref<OutBound.InOutBoundReqDocDetail>();
/** 领料码 */
const execCode = ref<number>();
/** 调整信息表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: [],
  showDefaultActions: false,
  wrapperClass:
    'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3',
});
/** 单据状态 dictKey*/
const docStatusDict: { [key: string]: any } = {
  /** 已完成 */
  finished: {
    name: 'yiwancheng',
    color: '!text-lime-500',
  },
  /** 审核驳回 */
  reject: {
    name: 'shenhebutongguo',
    color: '!text-red-500',
  },
  /** 已关闭 */
  closed: {
    name: 'yiguanbi',
    color: '!text-gray-300',
  },
};

/**  获取数据*/
const getData = async () => {
  try {
    loading.value = true;
    const data = await getInOutReqDocDetail(
      props.inOutReqDocId,
      props.inOutReqDocNumber,
    );
    // 获取当前账号的员工信息
    staffData.value = await getStaffInfo();
    formData.value = data;
    // 设置表单schema
    formApi.setState({
      schema: useFormSchema(
        staffData.value.staffId === formData.value.materialUser,
        formData.value,
      ),
    });
    // 赋值
    formApi.setValues(data);
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
/** 获取领料码 */
const getExecCodeData = async () => {
  try {
    execCode.value = await getExecCode(undefined, props.inOutReqDocId);
  } catch {
    ElMessage.error('获取领料码失败');
  }
};
/** 复制领料码 */
const { copy } = useClipboard({
  legacy: true,
  source: execCode.value?.toString,
});
onMounted(async () => {
  if (props.inOutReqDocId) {
    await getData();
  }
});
defineExpose({
  formApi,
  Form,
});
</script>
<template>
  <div class="relative" v-loading="loading">
    <IconFont
      v-if="
        formData?.docStatus === 'reject' ||
        formData?.docStatus === 'finished' ||
        formData?.docStatus === 'closed'
      "
      :name="docStatusDict[formData.docStatus].name"
      :size="150"
      class="absolute right-20 top-14"
      :class="docStatusDict[formData.docStatus].color"
    />
    <FormCard :is-footer="false">
      <template #title>
        <span>出库单信息</span>
      </template>
      <Form>
        <template #inOutReqDocNumber="row">
          <span>{{ row.value }}</span>
          <ElTag v-if="formData?.isRectify" type="primary" class="ml-1">
            补录
          </ElTag>
        </template>
        <template #collectorUserName="row">
          <span>{{ row.value }}</span>
          <span v-if="formData?.collectorUserDeptName">
            ({{ formData?.collectorUserDeptName }})
          </span>
          <ElTag v-if="formData?.isProxyExec" type="warning" class="ml-1">
            代领
          </ElTag>
        </template>
        <template #execCode>
          <div v-if="execCode" class="flex">
            <span>
              <b>{{ execCode }}</b>
            </span>
            <ElButton
              link
              type="primary"
              @click="
                copy(execCode.toString()).then(() => {
                  ElMessage.success('复制成功');
                })
              "
              class="ml-2"
            >
              点击复制
            </ElButton>
          </div>
          <ElButton v-else link type="primary" @click="getExecCodeData">
            点击查看
          </ElButton>
        </template>
        <template #documentProcess>
          <ElScrollbar>
            <StepProgress
              :doc-number="
                inOutReqDocNumber || formData?.inOutReqDocNumber || ''
              "
              class="mt-[20px] min-w-[750px]"
            />
          </ElScrollbar>
        </template>
      </Form>
    </FormCard>
  </div>
</template>
