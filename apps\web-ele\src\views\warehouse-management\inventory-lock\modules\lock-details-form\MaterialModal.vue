<script setup lang="ts">
import { h, nextTick, onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import MaterialForm from '#/views/warehouse-management/basic-data/material/modules/material-form/index.vue';

/** 共享数据 */
const shareData = ref();
const key = ref(0);
const materialFormRef = ref();
const [Modal, modalApi] = useVbenModal({
  cancelText: '关闭',
  onClosed() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      shareData.value = modalApi.getData<Record<string, any>>();
    }
  },
  onOpened() {
    nextTick(() => {
      const schema = materialFormRef.value?.formApi.getState().schema;
      schema?.unshift({
        component: () => {
          return h('div', null, shareData.value.warehouseName);
        },
        fieldName: 'warehouseName',
        label: '仓库名称',
        formItemClass: 'col-span-1',
      });
      materialFormRef.value?.formApi.updateSchema(schema);
    });
  },
});
onMounted(() => {});
</script>

<template>
  <Modal>
    <MaterialForm
      :key="key"
      ref="materialFormRef"
      :material-id="shareData.materialId"
      :material-code="shareData.materialCode"
    />
  </Modal>
</template>
