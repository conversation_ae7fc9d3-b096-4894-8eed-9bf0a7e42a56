import type { VbenFormSchema } from '@girant/adapter';

import type { WarehouseInfoApi } from '#/api';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';

import { getOriginalDocConfigList } from '#/api';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'div',
      fieldName: 'warehouseId',
      label: '仓库名称',
    },
    {
      component: 'div',
      fieldName: 'materialId',
      label: '物料名称',
    },
    {
      component: 'Input',
      componentProps: {
        type: 'number',
        clearable: true,
      },
      fieldName: 'blockQuantity',
      label: '锁库数量',
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        type: 'number',
        clearable: true,
        options: [
          { label: '锁定', value: false },
          { label: '预留', value: true },
        ],
      },
      defaultValue: true,
      fieldName: 'lockType',
      label: '锁库类型',
      rules: 'required',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: WarehouseInfoApi.OriginalDocConfigList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.docName,
            value: item.docCode,
          }));
          return warehouseList;
        },
        api: () => {
          return getOriginalDocConfigList();
        },
        clearable: true,
        filterable: true,
      },
      dependencies: {
        rules(values) {
          if (values.lockType) {
            return 'required';
          }
          return null;
        },
        triggerFields: ['lockType'],
      },
      fieldName: 'docTypeCode',
      label: '关联单据标识',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        rules(values) {
          if (values.lockType) {
            return 'required';
          }
          return null;
        },
        triggerFields: ['lockType'],
      },
      fieldName: 'docNumber',
      label: '关联单据编号',
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      formItemClass: 'col-span-full items-start',
      componentProps: {
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
    },
    {
      component: h(UploadFiles, {
        mode: 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
        class: 'w-full',
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      label: '附件',
      formItemClass: 'col-span-full items-start',
    },
  ];
}
