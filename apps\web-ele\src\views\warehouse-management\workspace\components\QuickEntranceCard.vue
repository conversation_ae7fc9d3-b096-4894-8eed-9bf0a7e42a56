<script lang="ts">
import { defineComponent, ref } from 'vue';
import { useRouter } from 'vue-router';

import PanelCard from '#/components/panel-card/Index.vue';

export default defineComponent({
  components: { PanelCard },
  setup() {
    const router = useRouter();
    const entranceList = ref([
      {
        id: 'inbound',
        title: '入库单',
        route: '/warehouse-management/inbound-management/inbound-documents',
      },
      {
        id: 'outbound',
        title: '出库单',
        route: '/warehouse-management/outbound-management/outbound-documents',
      },
      {
        id: 'OtherOutbound',
        title: '其他入库',
        route: '/warehouse-management/other-inbound-requests/warehousing-entry',
      },
      {
        id: 'OtherOutbound',
        title: '其他出库',
        route:
          '/warehouse-management/other-outbound-requests/outbound-application-form',
      },
      {
        id: 'inventoryAdjustment',
        title: '库存调整',
        route: '/warehouse-management/inventory-adjustment/query',
      },
      {
        id: 'baseInfo',
        title: '物料配置',
        route: '/warehouse-management/basic-data/inventory',
      },
    ]);

    const handleCardClick = (route: string) => {
      router.push(route);
    };

    return {
      entranceList,
      handleCardClick,
    };
  },
});
</script>

<template>
  <PanelCard title="快捷入口">
    <template #default>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-2">
        <div
          @click="handleCardClick(item.route)"
          v-for="item in entranceList"
          :key="item.id"
          class="hover:bg-primary-100 bg-primary-50 flex h-20 cursor-pointer items-center justify-center rounded-lg text-center font-medium"
        >
          {{ item.title }}
        </div>
      </div>
    </template>
  </PanelCard>
</template>
