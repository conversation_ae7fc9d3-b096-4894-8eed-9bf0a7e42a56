<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { ElMessage, ElScrollbar } from 'element-plus';

import {
  getWareTransferDocDetail,
  saveOrModWareTransferDoc,
  submitWareTransferDoc,
} from '#/api';
import ApprovalTimeline from '#/components/approval-timeline/Index.vue';
import FormCard from '#/components/form-card/Index.vue';

import MaterialFormEdit from './material-form/form-edit/index.vue';
import MaterialFormView from './material-form/form-view/index.vue';
import { dialog } from './method';
import TransferFormEdit from './transfer-form/form-edit/index.vue';
import TransferFormView from './transfer-form/form-view/index.vue';

const props = defineProps({
  isView: {
    type: Boolean,
    default: false,
  },
  /** 库存调拨单据ID */
  transferDocId: {
    type: String,
    default: '',
  },
  /** 库存调拨单据编号 */
  transferDocNumber: {
    type: String,
    default: '',
  },
  /** 库存调拨类型值 */
  docCode: {
    type: String,
    default: 'WM0081',
  },
  /** 审核流程实例ID */
  processInstanceId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['submitSuccess']);

const loading = ref(false);
/** 调拨信息ref*/
const transferFormRef = ref();
/** 原料明细ref*/
const materialFormRef = ref();
/** 审核流程实例ID */
const processId = ref(props.processInstanceId);
/** 调出仓库 */
const oldWarehouseId = ref();
/** 调入仓库 */
const targetWarehouseId = ref();

onMounted(async () => {
  try {
    // 如果没有审核流程，发送请求获取
    if (!processId.value) {
      const res = await getWareTransferDocDetail(
        props.transferDocId,
        props.transferDocNumber,
      );
      processId.value = res?.processInstanceId;
    }
  } catch (error) {
    console.error(error);
  }
});

/** 监听调出仓库变化 */
const oldWarehouseChange = async (warehouseId: string) => {
  oldWarehouseId.value = warehouseId;
};

/** 监听调入仓库变化 */
const targetWarehouseChange = async (warehouseId: string) => {
  targetWarehouseId.value = warehouseId;
};

const validateForm = async () => {
  // 校验调整信息表单 // 校验物料信息表单
  const [verification, verification2] = await Promise.all([
    transferFormRef.value?.validateForm(),
    materialFormRef.value?.validateForm(),
  ]);

  // 获取表单数据
  const data = await getFormData();
  if (data.transferItemList.length === 0) {
    ElMessage.error('请填写明细信息');
    return false;
  }
  if (!verification || !verification2) {
    ElMessage.error('请填写完整表单');
    return false;
  }

  // 检查物料是否重复
  const seenIds = new Set();
  for (const item of data.transferItemList) {
    // 检查当前materialId是否已存在
    if (seenIds.has(item.materialId)) {
      // 发现重复，返回true和重复的ID
      ElMessage.error('物料存在重复，请确保所有物料唯一');
      return false;
    }
    // 将当前ID添加到集合中
    seenIds.add(item.materialId);
  }

  return true;
};

/** 获取表单数据 */
const getFormData = async () => {
  const [data, data2] = await Promise.all([
    transferFormRef.value?.getFormData(),
    materialFormRef.value?.getFormData(),
  ]);
  return {
    ...data,
    transferItemList: data2,
  };
};

/** 提交表单 新增*/
const onSubmit = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    const isUpdate = !!props.transferDocId;
    if (await dialog('确定提交单据吗？', '提示')) {
      loading.value = true;
      await submitWareTransferDoc({
        transferDocId: isUpdate ? props.transferDocId : '',
        ...data,
      });
    }
    ElMessage.success(isUpdate ? '提交成功' : '新增成功');
    emit('submitSuccess');
  } catch (error) {
    console.error(error);
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};

/** 提交暂存 */
const onSave = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    const isUpdate = !!props.transferDocId;
    if (await dialog('确定提交暂存吗？', '提示')) {
      loading.value = true;
      await saveOrModWareTransferDoc({
        transferDocId: isUpdate ? props.transferDocId : '',
        ...data,
      });
    }
    ElMessage.success('提交暂存成功');
    emit('submitSuccess');
  } catch (error) {
    console.error(error);
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};

defineExpose({
  onSubmit,
  onSave,
  validateForm,
  loading,
});
</script>

<template>
  <TransferFormView
    v-if="isView"
    ref="transferFormRef"
    :transfer-doc-id="props.transferDocId"
    :transfer-doc-number="props.transferDocNumber"
  />
  <TransferFormEdit
    v-else
    ref="transferFormRef"
    :transfer-doc-id="props.transferDocId"
    :transfer-doc-number="props.transferDocNumber"
    @change-old-warehouse="oldWarehouseChange"
    @change-target-warehouse="targetWarehouseChange"
  />

  <MaterialFormView
    v-if="isView"
    ref="materialFormRef"
    :transfer-doc-id="props.transferDocId"
    :transfer-doc-number="props.transferDocNumber"
  />

  <MaterialFormEdit
    v-else
    ref="materialFormRef"
    :transfer-doc-id="props.transferDocId"
    :transfer-doc-number="props.transferDocNumber"
    :old-warehouse-id="oldWarehouseId"
    :target-warehouse-id="targetWarehouseId"
  />

  <FormCard :is-footer="false" v-if="isView">
    <template #title>
      <span>审核流程</span>
    </template>
    <ElScrollbar>
      <ApprovalTimeline v-if="processId" :process-instance-id="processId" />
    </ElScrollbar>
  </FormCard>
</template>
