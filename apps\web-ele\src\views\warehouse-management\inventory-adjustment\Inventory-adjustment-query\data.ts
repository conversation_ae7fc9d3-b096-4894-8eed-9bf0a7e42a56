import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import type { dictItemListType } from '#/api/common/dict';

import { h, markRaw } from 'vue';

import { ElInputTag } from 'element-plus';

import { getDictItemList } from '#/api/common/dict';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'invcAdjustDocNumberList',
      label: '单据编号',
      formItemClass: 'col-span-1',
    },
    {
      component: h(DeptStaffTree, {
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'submitUserList',
      label: '提交人',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'submitTime',
      formItemClass: 'col-span-2 w-full',
      label: '提交时间',
    },
    {
      component: h(DeptStaffTree, {
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'executorUserList',
      label: '执行人',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'execTime',
      formItemClass: 'col-span-2 w-full',
      label: '执行时间',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          // 过滤待提交 value=00
          const filterList = warehouseList.filter(
            (item) => item.value !== '00',
          );
          return filterList;
        },
        api: () => {
          return getDictItemList('publicDocStatus');
        },
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'docStatusList',
      label: '单据状态',
      formItemClass: 'col-span-1',
    },
  ];
}

/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      title: '单据编号',
      field: 'invcAdjustDocNumber',
      minWidth: 180,
    },
    {
      slots: {
        default: 'docStatusLabel',
      },
      title: '单据状态',
      field: 'docStatusLabel',
      minWidth: 110,
    },
    {
      title: '提交人',
      field: 'submitUserName',
      minWidth: 100,
    },
    {
      title: '提交时间',
      field: 'submitTime',
      minWidth: 115,
    },
    {
      title: '执行人',
      field: 'executorUserName',
      minWidth: 100,
    },
    {
      title: '执行时间',
      field: 'execTime',
      minWidth: 115,
    },

    {
      align: 'center',
      slots: {
        default: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      minWidth: 180,
      title: '操作',
    },
  ];
}
