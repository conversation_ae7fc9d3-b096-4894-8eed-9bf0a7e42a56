<script setup lang="ts">
import { ref } from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';

import {
  saveOrModInvcAdjustDoc,
  submitInvcAdjustDoc,
} from '#/api/warehouse-management';

import AdjustFormEdit from './adjust-form/form-edit/index.vue';
import MaterialFormEdit from './material-form/form-edit/index.vue';

const props = defineProps({
  /** 库存调整单据编号 */
  invcAdjustDocNumber: {
    type: String,
    default: '',
  },
  /** 库存调整单据id */
  invcAdjustDocId: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['submitSuccess']);
const loading = ref(false);
/** 调整信息ref*/
const adjustFormRef = ref();
/** 物料信息ref*/
const materialFormRef = ref();

/** 校验表单 */
const validateForm = async () => {
  // 校验调整信息表单 // 校验物料信息表单
  const [verification, verification2] = await Promise.all([
    adjustFormRef.value?.validateForm(),
    materialFormRef.value?.validateForm(),
  ]);
  // 获取表单数据
  const data = await getFormData();
  if (data.invcAdjustItemList.length === 0) {
    ElMessage.error('请填写物料信息');
    return false;
  }
  if (!verification || !verification2) {
    ElMessage.error('请填写完整表单');
    return false;
  }
  return true;
};

/** 获取表单数据 */
const getFormData = async () => {
  const [data, data2] = await Promise.all([
    adjustFormRef.value?.getFormData(),
    materialFormRef.value?.getFormData(),
  ]);
  return {
    ...data,
    invcAdjustItemList: data2,
  };
};

/** 提交表单 新增*/
const onSubmit = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    const isUpdate = !!props.invcAdjustDocId;
    await ElMessageBox.confirm('确定提交单据吗？', '提示', {
      type: 'warning',
    });
    loading.value = true;
    await submitInvcAdjustDoc({
      invcAdjustDocId: isUpdate ? props.invcAdjustDocId : '',
      ...data,
    });
    ElMessage.success(isUpdate ? '提交成功' : '新增成功');
    emit('submitSuccess');
  } catch (error) {
    console.error(error);
    // ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};

/** 提交暂存 */
const onSave = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    await ElMessageBox.confirm('确定提交暂存吗？', '提示', {
      type: 'warning',
    });
    loading.value = true;
    await saveOrModInvcAdjustDoc(data);
    ElMessage.success('提交暂存成功');
    emit('submitSuccess');
  } catch (error) {
    console.error(error);
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};
defineExpose({
  validateForm,
  onSave,
  onSubmit,
  loading,
});
</script>
<template>
  <div v-loading="loading">
    <AdjustFormEdit
      ref="adjustFormRef"
      :invc-adjust-doc-id="invcAdjustDocId"
      :invc-adjust-doc-number="invcAdjustDocNumber"
    />
    <MaterialFormEdit
      ref="materialFormRef"
      :invc-adjust-doc-number="invcAdjustDocNumber"
      :invc-adjust-doc-id="invcAdjustDocId"
    />
  </div>
</template>
