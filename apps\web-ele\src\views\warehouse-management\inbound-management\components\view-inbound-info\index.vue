<script setup lang="ts">
import { computed, defineAsyncComponent, markRaw } from 'vue';

const props = defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
  docStatus: {
    default: '',
    type: String,
  },
});

const InboundInfo = computed(() => {
  switch (props.docStatus) {
    // 待入库
    case '00': {
      return markRaw(defineAsyncComponent(() => import('./pending/index.vue')));
    }

    // 取消审核中/已关闭
    case '05':
    case '99': {
      return markRaw(defineAsyncComponent(() => import('./closed/index.vue')));
    }

    // 已入库
    case '10': {
      return markRaw(defineAsyncComponent(() => import('./already/index.vue')));
    }

    default: {
      return markRaw(defineAsyncComponent(() => import('./pending/index.vue')));
    }
  }
});
</script>

<template>
  <component
    :is="InboundInfo"
    :in-bound-doc-id="inBoundDocId"
    :in-bound-doc-number="inBoundDocNumber"
    v-bind="$attrs"
  />
</template>
