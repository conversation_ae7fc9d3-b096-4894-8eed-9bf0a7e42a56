<script setup lang="ts">
import { computed, defineAsyncComponent, markRaw } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import ViewCloseBoundModal from './ViewCloseBoundModal.vue';

const props = defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
  docStatus: {
    default: '',
    type: String,
  },
});

/** 关闭出入库弹窗 */
const [CloseModal, closeModalApi] = useVbenModal({
  connectedComponent: ViewCloseBoundModal,
  destroyOnClose: true,
});

const InboundInfo = computed(() => {
  switch (props.docStatus) {
    // 取消审核中/已关闭
    case 'cancelAudit':
    case 'closed': {
      return markRaw(defineAsyncComponent(() => import('./closed/index.vue')));
    }
    // 待入库
    case 'pending': {
      return markRaw(defineAsyncComponent(() => import('./pending/index.vue')));
    }

    // 已入库
    case 'stocked': {
      return markRaw(defineAsyncComponent(() => import('./already/index.vue')));
    }

    default: {
      return markRaw(defineAsyncComponent(() => import('./pending/index.vue')));
    }
  }
});

const handleViewInOutCancelDoc = (data: any) => {
  closeModalApi
    ?.setState({ title: '取消单详情' })
    .setData({
      inOutCancelDocId: data.inOutCancelDocId,
      inOutCancelDocNumber: data.inOutCancelDocNumber,
    })
    .open();
};
</script>

<template>
  <component
    :is="InboundInfo"
    :in-bound-doc-id="inBoundDocId"
    :in-bound-doc-number="inBoundDocNumber"
    @view-in-out-cancel-doc="handleViewInOutCancelDoc"
    v-bind="$attrs"
  />

  <CloseModal class="w-3/5" />
</template>
