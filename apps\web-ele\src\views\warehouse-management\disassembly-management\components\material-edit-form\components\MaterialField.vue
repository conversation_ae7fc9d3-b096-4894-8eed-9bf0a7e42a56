<script setup lang="ts">
import { ref, watch } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';

import MaterialSelect from '#/components/material-select/Index.vue';

const props = defineProps({
  /**
   * 用于双向数据绑定的当前选中值
   * 默认值为 null，表示没有选中任何值
   */
  modelValue: {
    type: [Object, null],
    default: null,
  },
  /** 图片ID */
  pictureFileId: {
    type: String,
    default: '',
  },
  /** 是否可被编辑 */
  editable: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(['update:modelValue', 'change', 'focus', 'blur']);

/** 图片Id */
const pictureFileId = ref(props.pictureFileId);

/** 监听props pictureFileId改变 */
watch(
  () => props.pictureFileId,
  (newVal) => {
    pictureFileId.value = newVal;
  },
);

const onChange = (materialId: any, selectedItem: any) => {
  emits('change', materialId, selectedItem);
  pictureFileId.value = selectedItem.pictureFileId;
};
</script>

<template>
  <div class="flex size-full flex-row items-center gap-2">
    <span class="min-w-[120px] flex-1" v-if="props.editable">
      <MaterialSelect
        value-key="materialId"
        :model-value="props.modelValue"
        @change="onChange"
      />
    </span>
    <span class="min-w-[120px] flex-1" v-else>
      {{ props.modelValue?.materialName ?? '' }}
    </span>
    <ImageViewer
      v-if="pictureFileId"
      :img-id="pictureFileId"
      img-css="size-7 flex-shrink-0"
      :show-thumbnail="false"
    />
  </div>
</template>
