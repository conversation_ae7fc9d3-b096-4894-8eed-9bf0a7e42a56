<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

// @ts-ignore
import { DynamicTable } from '@girant-web/dynamic-table-component';
import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElInputNumber,
  ElLink,
  ElMessage,
  ElPopconfirm,
  ElPopover,
} from 'element-plus';

import {
  getAssemblyDocDetail,
  getProdBomDetail,
  getProdBomDetailById,
} from '#/api';
import FormCard from '#/components/form-card/Index.vue';

import { useColumns, useGridOptions, viewMaterialName } from './data';

const props = defineProps({
  /** 组装拆卸单据ID */
  assemblyDocId: {
    type: String,
    default: '',
  },
  /** 组装拆卸单据编号*/
  assemblyDocNumber: {
    type: String,
    default: '',
  },
  /** 拆卸成品BomID */
  bomId: {
    type: String,
    default: '',
  },
  /** 拆卸成品现在数量 */
  currentQuantity: {
    type: Number,
    default: 1,
  },
  /** 拆卸成品旧数量 */
  oldQuantity: {
    type: Number,
    default: 0,
  },
});

const loading = ref(false);
/** 表格ref */
const gridTable = ref<DynamicTable>();
/** 母件Bom物料列表 */
const bomMaterialList = ref([]);

onMounted(() => {
  if (props.assemblyDocId || props.assemblyDocNumber) {
    getData();
  }
});

/** 获取拆卸单据信息 */
const getData = async () => {
  try {
    loading.value = true;
    // 获取拆卸单据信息
    const data = await getAssemblyDocDetail(
      props.assemblyDocId,
      props.assemblyDocNumber,
      true,
    );
    // 获取母件Bom信息
    const prodBomInfo = await getProdBomDetail({
      materialId: data.productMaterialId,
      isSonDetail: true,
    });
    // 保存bom物料列表
    bomMaterialList.value = prodBomInfo.children.map((item: any) => ({
      materialId: item.materialId,
      materialSpecs: item.materialSpecs,
      baseUnitLabel: item.baseUnitLabel,
      quantity: item.usageQuantity,
    }));
    // 创建物料ID映射表，用于快速查找
    const bomMaterialMap = new Map(
      bomMaterialList.value.map((item: any) => [item.materialId, true]),
    );
    // 处理数据
    data.assemblyMaterialList?.forEach((item: any) => {
      item.materialId = {
        materialId: item.materialId,
        materialName: viewMaterialName(item),
      };
      // 检查物料是否在BOM列表中存在，并添加isEditable属性
      item.isEditable = !bomMaterialMap.has(item.materialId.materialId);
    });
    // 赋值
    await gridTable.value.setTableData(data.assemblyMaterialList);
    // 刷新差异值
    refreshDifference();
  } catch (error) {
    console.error(error);
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

/** 刷新所有Bom差异 */
const refreshDifference = async () => {
  const tableData = await gridTable.value.getTableFullData();
  tableData.forEach((item: any) => {
    changeQuantity(item, item.quantity);
  });
};

/** 编辑表单表格*/
const gridOptions = useGridOptions();

/** 差异表单 */
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useColumns(),
    data: [],
    minHeight: 150,
    border: true,
    pagerConfig: {
      enabled: false,
    },
  },
});

/** 显示差异表单 */
const showPopover = async () => {
  const tableData = await gridTable.value.getTableFullData();
  // 获取表单内容
  const processedTableData = tableData
    .map((item: any) => {
      const baseData = {
        materialId: item.materialId.materialId,
        materialName: item.materialId.materialName,
        usageQuantity: 0,
        quantity: item.quantity,
        difference: item.difference,
      };

      // 关联 bomMaterialList 中的 usageQuantity
      const matchedBomItem: any = bomMaterialList.value.find(
        (bomItem: any) => bomItem.materialId === item.materialId.materialId,
      );

      if (matchedBomItem) {
        baseData.usageQuantity =
          matchedBomItem.quantity * props.currentQuantity;
      }

      return baseData;
    })
    .filter((item: any) => item.difference && item.difference !== 0);
  // 设置表格内容
  gridApi.setGridOptions({
    data: processedTableData,
  });
};

/** 获取Bom物料信息 */
const fetchBomMaterialList = async () => {
  if (!props.bomId) {
    // 清空物料内容
    gridTable.value.setTableData([{}]);
    return;
  }

  loading.value = true;
  try {
    const prodBomInfo = await getProdBomDetailById(props.bomId);
    // 保存bom物料列表
    bomMaterialList.value = prodBomInfo.children.map((item: any) => ({
      materialId: item.materialId,
      materialSpecs: item.materialSpecs,
      baseUnitLabel: item.baseUnitLabel,
      quantity: item.usageQuantity,
    }));
    // 赋值
    const materialList = prodBomInfo.children.map((item: any) => ({
      ...item,
      materialId: {
        materialId: item.materialId,
        materialName: viewMaterialName(item),
      },
      quantity: item.usageQuantity * (props.currentQuantity ?? 1),
      difference: 0,
      isEditable: false,
    }));
    await gridTable.value.setTableData(materialList);
  } catch (error) {
    console.error('获取BOM物料列表失败', error);
  } finally {
    loading.value = false;
  }
};

/** 监听props.bomId变化，重新请求 */
watch(() => props.bomId, fetchBomMaterialList);

/** 获取物料数量 */
const fetchQuantity = async () => {
  // 获取当前表格所有数据
  const tableData = await gridTable.value.getTableFullData();

  // 计算并更新每一行的数量
  const updatedData = tableData.map((row: any) => {
    // 关键公式：新数量 = (原数量 / oldQuantity) * currentQuantity
    const newMaterialQuantity =
      (row.quantity / (props.oldQuantity ?? 1)) * (props.currentQuantity ?? 1);

    changeQuantity(row, newMaterialQuantity);
    return {
      ...row,
      quantity: newMaterialQuantity,
    };
  });

  // 更新表格数据
  gridTable.value.setTableData(updatedData);
};

/** 监听props.currentQuantity变化，重新计算物料数量 */
watch(() => props.currentQuantity, fetchQuantity);

/** 物料数量差异变化 */
const changeQuantity = (row: any, currentValue: number) => {
  if (row.isEditable ?? true) {
    row.difference = currentValue;
  } else {
    // 查找物料在bomMaterialList中的配置
    const materialItem: any = bomMaterialList.value.find(
      (item: any) => item.materialId === row.materialId?.materialId,
    );
    if (materialItem) {
      // 计算差值 = 所需数量 - 当前数量
      row.difference =
        currentValue - materialItem.quantity * (props.currentQuantity ?? 1);
    }
  }
};

const showDeleteButton = (row: any) => {
  if (row.isEditable ?? true) {
    return true;
  }

  if (row.quantity !== 0) {
    return true;
  }

  return false;
};

/** 操作 */
const onActionClick = (row: any) => {
  if (row.isEditable ?? true) {
    gridTable.value.removeRow(row);
  } else {
    row.quantity = 0;
    changeQuantity(row, 0);
  }
};

/** 校验 */
const validateForm = async () => {
  // 校验表单
  const isValid = await gridTable.value.tableValidate();
  if (isValid) {
    return false;
  }
  return true;
};

/** 提交 */
const getFormData = async () => {
  const data = await gridTable.value.getTableData();
  // 提取出需要的字段
  const result = data.map((item: any) => ({
    materialId: item.materialId.materialId,
    quantity: item.quantity,
  }));
  return result;
};

/** 对外开放方法 */
defineExpose({
  getFormData,
  validateForm,
});
</script>

<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>原料明细</span>
    </template>

    <DynamicTable ref="gridTable" :grid-options="gridOptions" class="border">
      <template #toolbar-tools>
        <ElPopover
          placement="bottom-end"
          width="600"
          trigger="click"
          @show="showPopover"
        >
          <Grid>
            <template #difference="{ row }">
              <span v-if="row.difference > 0" class="text-red-500">
                +{{ row.difference }}
              </span>
              <span v-else-if="row.difference < 0" class="text-green-500">
                {{ row.difference }}
              </span>
              <span v-else>-</span>
            </template>
          </Grid>
          <template #reference>
            <ElLink type="primary"> BOM差异分析 </ElLink>
          </template>
        </ElPopover>
      </template>
      <template #difference="{ row }">
        <span v-if="row.difference > 0" class="text-red-500">
          +{{ row.difference }}
        </span>
        <span v-else-if="row.difference < 0" class="text-green-500">
          {{ row.difference }}
        </span>
        <span v-else>-</span>
      </template>
      <template #quantity="{ row }">
        <ElInputNumber
          v-model="row.quantity"
          :min="row.isEditable === false ? 0 : 1"
          @change="
            (currentValue: number = 0) => changeQuantity(row, currentValue)
          "
        />
      </template>
      <template #CellOperation="{ row }">
        <div v-if="showDeleteButton(row)">
          <ElPopconfirm title="删除" @confirm="onActionClick(row)">
            <template #reference>
              <ElButton link size="small" type="danger"> 删除 </ElButton>
            </template>
          </ElPopconfirm>
        </div>
        <div v-else>
          <ElButton link disabled size="small" type="info"> 删除 </ElButton>
        </div>
      </template>
    </DynamicTable>
  </FormCard>
</template>
