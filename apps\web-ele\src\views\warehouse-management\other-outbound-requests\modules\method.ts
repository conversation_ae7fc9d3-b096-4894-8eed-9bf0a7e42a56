import { ElMessageBox } from 'element-plus';

import { getEnumByName } from '#/api/common/enum';

/** 单据状态出入库Tag类型*/
export const docStatusDict: { [key: string]: any } = {
  /** 待提交 */
  '00': 'warning',
  /** 待审核 */
  '10': 'primary',
  /** 待入库 / 待出库 */
  '20': 'primary',
  /** "取消审核中" */
  '25': 'warning',
  /** 已完成 */
  '30': 'success',
  /** 审核驳回 */
  '80': 'danger',
  /** 已关闭 */
  '90': 'info',
};
/** 提示框 */
export const confirm = (content: string, title: string) => {
  return new Promise((resolve) => {
    ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
      .then(() => {
        resolve(true);
      })
      .catch(() => {});
  });
};

/** 打开模态框 */
export const openModal = (
  /** 模态框API */
  formModalApi: any,
  /** 是否显示确认按钮 */
  showConfirmButton: boolean,
  /** 标题 */
  title: string,
) => {
  formModalApi
    .setState({
      showConfirmButton,
      title,
    })
    .open();
};

/** 获取其它出库类型的值 */
export const getOutType = async () => {
  const wmInOrOutEnums = await getEnumByName('WmInOrOutEnums');
  const out = wmInOrOutEnums?.find((item) => item.enumKey === 'OUT')?.enumValue;
  return out || 'OUT';
};

/**
 * 日期禁用函数
 * @param isEnd 是否是结束时间
 */
export const createDisabledDate = (isEnd: boolean, thisTime: any) => {
  return (time: Date) => {
    if (!thisTime.endTime && !isEnd) {
      return false;
    }
    // 是结束时间
    return isEnd
      ? time.getTime() < new Date(thisTime.startTime).getTime()
      : time.getTime() > new Date(thisTime.endTime).getTime();
  };
};
