<script setup lang="ts">
import type { PropType } from 'vue';

import type { InBoundDocApi } from '#/api/warehouse-management';

import Info from '../components/Info.vue';

const props = defineProps({
  // 物料信息（包含仓库、库位信息）
  materialItemData: {
    type: Object as PropType<InBoundDocApi.InBoundItem>,
    default: () => ({}),
  },
});

const emits = defineEmits(['handleMaterialCode']);

const handleMaterialCode = () => {
  emits('handleMaterialCode', props.materialItemData.materialCode);
};
</script>
<template>
  <div class="rounded-lg bg-white">
    <Info
      :material-item-data="materialItemData"
      @handle-material-code="handleMaterialCode"
    />
  </div>
</template>
