<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElButton, ElCard, ElMessage, ElTag, ElTooltip } from 'element-plus';

import {
  exportWareTransferMyDraftDoc,
  getWareTransferMyDraftDocPage,
} from '#/api';

import Form from '../modules/Form.vue';
import { deleteDoc, docStatusDict, openModal } from '../modules/method';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});

const formRef = ref<InstanceType<typeof Form>>();
const exportLoading = ref(false);
/** 库存调拨单据编号 */
const transferDocNumber = ref('');
/** 库存调拨单据ID */
const transferDocId = ref('');
/** 审核流程实例ID */
const processInstanceId = ref('');
/** 是否查看 */
const isView = ref(false);
/** 是否显示暂存 */
const isShowSave = ref(false);
/** 当前单据状态 */
const docStatus = ref('');

onMounted(async () => {
  /** 初始化搜索条件 */
  await gridApi.formApi.setValues({
    transferDocNumberList:
      props.params?.transferDocNumberList?.split(',') || [],
    docCode: props.params?.docCode?.split(',') || '',
    oldWarehouseIdList: props.params?.oldWarehouseIdList?.split(',') || [],
    targetWarehouseIdList:
      props.params?.targetWarehouseIdList?.split(',') || [],
  });
});

/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onConfirm: () => {
    formRef.value?.onSubmit();
  },
  confirmText: '提交',
  showCancelButton: true,
  showConfirmButton: true,
});

/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();

    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};

/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    handleReset,
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || false,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getWareTransferMyDraftDocPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions,
});

/** 新增 */
const onAdd = () => {
  isView.value = false;
  isShowSave.value = true;
  transferDocId.value = '';
  transferDocNumber.value = '';
  docStatus.value = '';
  openModal(formModalApi, true, '新增');
};

/** 导出数据 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportWareTransferMyDraftDoc({
      ...formValues,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};

/** 提交成功 */
const submitSuccess = () => {
  // 重新获取数据
  gridApi.query();
  formModalApi.close();
};

/** 查看 */
const onView = (row: any) => {
  isView.value = true;
  isShowSave.value = false;
  transferDocId.value = row.transferDocId;
  transferDocNumber.value = row.transferDocNumber;
  processInstanceId.value = row.processInstanceId;
  docStatus.value = row.docStatus;
  openModal(formModalApi, false, '详情');
};

/** 再次提交 */
const resubmit = async (id: string) => {
  await formModalApi.close();
  isView.value = false;
  isShowSave.value = true;
  transferDocId.value = id;
  docStatus.value = '';
  openModal(formModalApi, true, '再次提交');
};

/** 提交单据 */
const onSubmit = async (id: string) => {
  await formModalApi.close();
  isView.value = false;
  isShowSave.value = true;
  transferDocId.value = id;
  docStatus.value = '';
  openModal(formModalApi, true, '提交单据');
};

/** 删除待提交单据 */
const onDelete = (transferDocId: string) => {
  deleteDoc(transferDocId, formModalApi, gridApi);
};
</script>

<template>
  <Page auto-content-height>
    <ElCard class="h-full" body-class="h-full !p-[10px]">
      <!-- 模态框 -->
      <FormModal class="h-full w-10/12">
        <Form
          ref="formRef"
          :is-view="isView"
          @submit-success="submitSuccess"
          :transfer-doc-id="transferDocId"
          :transfer-doc-number="transferDocNumber"
          :process-instance-id="processInstanceId"
        />
        <template #center-footer>
          <ElButton v-if="isShowSave" type="primary" @click="formRef?.onSave()">
            暂存
          </ElButton>
          <ElButton
            v-if="docStatus === '00'"
            type="primary"
            @click="onSubmit(transferDocId)"
          >
            提交单据
          </ElButton>
          <ElButton
            v-if="docStatus === '80'"
            type="primary"
            @click="resubmit(transferDocId)"
          >
            再次提交
          </ElButton>
          <ElButton
            v-if="transferDocId"
            @click="onDelete(transferDocId)"
            type="danger"
          >
            删除
          </ElButton>
        </template>
      </FormModal>
      <Grid>
        <template #toolbar-actions>
          <ElButton
            v-access:code="['wm:ware:transfer:submit']"
            type="primary"
            @click="onAdd"
          >
            新增
          </ElButton>
        </template>
        <template #transferDocNumber="{ row }">
          <span v-if="row.transferDocNumber">{{ row.transferDocNumber }}</span>
          <span v-else>暂无单据编号</span>
        </template>
        <template #docStatusLabel="{ row }">
          <ElTag size="small" :type="docStatusDict[row.docStatus]">
            {{ row.docStatusLabel }}
          </ElTag>
        </template>
        <template #CellOperation="{ row }">
          <div>
            <ElButton link size="small" @click="onView(row)" type="info">
              查看
            </ElButton>
            <ElButton
              v-if="row.docStatus === '00'"
              link
              size="small"
              @click="onSubmit(row.transferDocId)"
              type="primary"
            >
              提交单据
            </ElButton>
            <ElButton
              v-if="row.docStatus === '80'"
              link
              size="small"
              @click="resubmit(row.transferDocId)"
              type="primary"
            >
              再次提交
            </ElButton>
            <ElButton
              link
              size="small"
              @click="onDelete(row.transferDocId)"
              type="danger"
            >
              删除
            </ElButton>
          </div>
        </template>
        <template #toolbar-tools>
          <ElTooltip
            class="box-item"
            effect="light"
            content="导出数据"
            placement="top-start"
          >
            <ElButton :loading="exportLoading" circle @click="exportHandle">
              <template #icon><IconFont name="xiazai" /></template>
            </ElButton>
          </ElTooltip>
        </template>
      </Grid>
    </ElCard>
  </Page>
</template>
