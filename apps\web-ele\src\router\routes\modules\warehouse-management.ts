import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      keepAlive: true,
      title: '仓库管理',
    },
    name: 'warehouseManagent',
    path: '/warehouse-management',
    children: [
      {
        meta: {
          title: '工作台',
        },
        name: 'workspace',
        path: '/warehouse-management/workspace',
        component: () =>
          import('#/views/warehouse-management/workspace/Index.vue'),
      },
      {
        meta: {
          title: '基础资料管理',
        },
        name: 'BasicData',
        path: '/warehouse-management/basic-data',
        children: [
          {
            component: () =>
              import(
                '#/views/warehouse-management/basic-data/warehouse/List.vue'
              ),
            meta: {
              title: '仓库管理',
            },
            name: 'WarehouseWarehouse',
            path: '/warehouse-management/basic-data/Warehouse',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/basic-data/material/List.vue'
              ),
            meta: {
              title: '物料配置',
            },
            name: 'MaterialConfig',
            path: '/warehouse-management/basic-data/material',
          },
        ],
      },
      {
        meta: {
          title: '库存查询',
        },
        name: 'InventoryQuery',
        path: '/warehouse-management/inventory-query',
        children: [
          {
            component: () =>
              import(
                '#/views/warehouse-management/inventory-query/inventory/List.vue'
              ),
            meta: {
              title: '即时库存',
            },
            name: 'Inventory',
            path: '/warehouse-management/inventory-query/inventory',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/inventory-query/inventory-details/List.vue'
              ),
            meta: {
              title: '即时库存明细',
            },
            name: 'Inventory-details',
            path: '/warehouse-management/inventory-query/inventory-details',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/inventory-query/safety/List.vue'
              ),
            meta: {
              title: '安全库存预警',
            },
            name: 'Safety',
            path: '/warehouse-management/inventory-query/safety',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/inventory-query/obsolete/List.vue'
              ),
            meta: {
              title: '呆料分析',
            },
            name: 'Obsolete',
            path: '/warehouse-management/inventory-query/obsolete',
          },
        ],
      },
      {
        meta: {
          title: '入库管理',
        },
        name: 'InboundManagement',
        path: '/warehouse-management/inbound-management',
        children: [
          {
            component: () =>
              import(
                '#/views/warehouse-management/inbound-management/stock-pending/List.vue'
              ),
            meta: {
              title: '待入库查询',
            },
            name: 'StockPending',
            path: '/warehouse-management/inbound-management/stock-pending',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/inbound-management/stock-already/List.vue'
              ),
            meta: {
              title: '已入库查询',
            },
            name: 'StockAlready',
            path: '/warehouse-management/inbound-management/stock-already',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/inbound-management/inbound-documents/List.vue'
              ),
            meta: {
              title: '入库单查询',
            },
            name: 'InboundDocuments',
            path: '/warehouse-management/inbound-management/inbound-documents',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/inbound-management/components/inbound-details/List.vue'
              ),
            meta: {
              title: '入库明细查询',
            },
            name: 'InboundDetails',
            path: '/warehouse-management/inbound-management/inbound-details',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/inbound-management/inbound-pending-details/List.vue'
              ),
            meta: {
              title: '待入库明细',
            },
            name: 'InboundPendingDetails',
            path: '/warehouse-management/inbound-management/inbound-pending-details',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/inbound-management/inbound-already-details/List.vue'
              ),
            meta: {
              title: '已入库明细',
            },
            name: 'InboundAlreadyDetails',
            path: '/warehouse-management/inbound-management/inbound-already-details',
          },
        ],
      },
      {
        meta: {
          title: '出库管理',
        },
        name: 'OutboundManagement',
        path: '/warehouse-management/outbound-management',
        children: [
          {
            component: () =>
              import(
                '#/views/warehouse-management/outbound-management/outbound-pending/List.vue'
              ),
            meta: {
              title: '待出库查询',
            },
            name: 'OutboundPending',
            path: '/warehouse-management/outbound-management/outbound-pending',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/outbound-management/outbound-already/List.vue'
              ),
            meta: {
              title: '已出库查询',
            },
            name: 'OutboundAlready',
            path: '/warehouse-management/outbound-management/outbound-already',
          },

          {
            component: () =>
              import(
                '#/views/warehouse-management/outbound-management/outbound-documents/List.vue'
              ),
            meta: {
              title: '出库单查询',
            },
            name: 'OutboundDocuments',
            path: '/warehouse-management/outbound-management/outbound-documents',
          },

          {
            component: () =>
              import(
                '#/views/warehouse-management/outbound-management/components/outbound-details/List.vue'
              ),
            meta: {
              title: '出库单明细查询',
            },
            name: 'OutboundDetails',
            path: '/warehouse-management/outbound-management/outbound-details',
          },

          {
            component: () =>
              import(
                '#/views/warehouse-management/outbound-management/outbound-pending-details/List.vue'
              ),
            meta: {
              title: '待出库明细查询',
            },
            name: 'OutboundPendingDetails',
            path: '/warehouse-management/outbound-management/outbound-pending-details',
          },

          {
            component: () =>
              import(
                '#/views/warehouse-management/outbound-management/outbound-already-details/List.vue'
              ),
            meta: {
              title: '已出库明细查询',
            },
            name: 'OutboundAlreadyDetails',
            path: '/warehouse-management/outbound-management/outbound-already-details',
          },

          {
            component: () =>
              import(
                '#/views/warehouse-management/outbound-management/material-pending/List.vue'
              ),
            meta: {
              title: '待提交备料',
            },
            name: 'MaterialPending',
            path: '/warehouse-management/outbound-management/material-pending',
          },

          {
            component: () =>
              import(
                '#/views/warehouse-management/outbound-management/material-query/List.vue'
              ),
            meta: {
              title: '备料单查询',
            },
            name: 'MaterialQuery',
            path: '/warehouse-management/outbound-management/material-query',
          },
        ],
      },
      {
        meta: {
          title: '库存调整',
        },
        name: 'InventoryAdjustment',
        path: '/warehouse-management/inventory-adjustment',
        children: [
          {
            component: () =>
              import(
                '#/views/warehouse-management/inventory-adjustment/query/LIst.vue'
              ),
            meta: {
              title: '库存调整查询',
            },
            name: 'Adjustment',
            path: '/warehouse-management/inventory-adjustment/query',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/inventory-adjustment/detail/List.vue'
              ),
            meta: {
              title: '库存调整明细',
            },
            name: 'Detail',
            path: '/warehouse-management/inventory-adjustment/detail',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/inventory-adjustment/unsubmitted/List.vue'
              ),
            meta: {
              title: '待提交库存调整',
            },
            name: 'Unsubmitted',
            path: '/warehouse-management/inventory-adjustment/unsubmitted',
          },
        ],
      },
      {
        meta: {
          title: '其它出库申请',
        },
        name: 'OutboundRequests',
        path: '/warehouse-management/other-outbound-requests',
        children: [
          {
            component: () =>
              import(
                '#/views/warehouse-management/other-outbound-requests/query/List.vue'
              ),
            meta: {
              title: '其它出库申请查询',
            },
            name: 'OtherOutQuery',
            path: '/warehouse-management/other-outbound-requests/query',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/other-outbound-requests/detail/List.vue'
              ),
            meta: {
              title: '其它出库申请明细',
            },
            name: 'OtherOutDetail',
            path: '/warehouse-management/other-outbound-requests/detail',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/other-outbound-requests/unsubmitted/List.vue'
              ),
            meta: {
              title: '待提交其它出库申请',
            },
            name: 'OtherOutUnsubmitted',
            path: '/warehouse-management/other-outbound-requests/unsubmitted',
          },
        ],
      },
      {
        meta: {
          title: '其它入库申请',
        },
        name: 'InboundRequests',
        path: '/warehouse-management/other-inbound-requests',
        children: [
          {
            component: () =>
              import(
                '#/views/warehouse-management/other-inbound-requests/query/List.vue'
              ),
            meta: {
              title: '其它入库申请查询',
            },
            name: 'OtherInQuery',
            path: '/warehouse-management/other-inbound-requests/query',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/other-inbound-requests/detail/List.vue'
              ),
            meta: {
              title: '其它入库申请明细',
            },
            name: 'OtherInDetail',
            path: '/warehouse-management/other-inbound-requests/detail',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/other-inbound-requests/unsubmitted/List.vue'
              ),
            meta: {
              title: '待提交其它入库申请',
            },
            name: 'OtherInUnsubmitted',
            path: '/warehouse-management/other-inbound-requests/unsubmitted',
          },
        ],
      },
      {
        meta: {
          title: '库存锁库',
        },
        name: 'InventoryLock',
        path: '/warehouse-management/inventory-lock',
        children: [
          {
            component: () =>
              import(
                '#/views/warehouse-management/inventory-lock/query/List.vue'
              ),
            meta: {
              title: '库存锁库查询',
            },
            name: 'InventoryLockQuery',
            path: '/warehouse-management/inventory-lock/query',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/inventory-lock/history/List.vue'
              ),
            meta: {
              title: '历史库存锁库查询',
            },
            name: 'InventoryLockHistory',
            path: '/warehouse-management/inventory-lock/history',
          },
        ],
      },
      {
        meta: {
          title: '仓内调拨',
        },
        name: 'WarehouseTransfer',
        path: '/warehouse-management/warehouse-transfer',
        children: [
          {
            component: () =>
              import(
                '#/views/warehouse-management/warehouse-transfer/transfer-query/List.vue'
              ),
            meta: {
              title: '调拨单查询',
            },
            name: 'TransferQuery',
            path: '/warehouse-management/warehouse-transfer/transfer-query',
          },

          {
            component: () =>
              import(
                '#/views/warehouse-management/warehouse-transfer/pending-submit/List.vue'
              ),
            meta: {
              title: '待提交调拨',
            },
            name: 'PendingSubmit',
            path: '/warehouse-management/warehouse-transfer/pending-submit',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/warehouse-transfer/pending-execution/List.vue'
              ),
            meta: {
              title: '待执行调拨',
            },
            name: 'PendingExecution',
            path: '/warehouse-management/warehouse-transfer/pending-execution',
          },
        ],
      },
      {
        meta: {
          title: '拆卸管理',
        },
        name: 'DisassemblyManagement',
        path: '/warehouse-management/disassembly-management',
        children: [
          {
            component: () =>
              import(
                '#/views/warehouse-management/disassembly-management/pending/List.vue'
              ),
            meta: {
              title: '待执行拆卸',
            },
            name: 'DisassemblyManagementPending',
            path: '/warehouse-management/disassembly-management/pending',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/disassembly-management/query/List.vue'
              ),
            meta: {
              title: '拆卸单查询',
            },
            name: 'DisassemblyManagementQuery',
            path: '/warehouse-management/disassembly-management/query',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/disassembly-management/unsubmitted/List.vue'
              ),
            meta: {
              title: '待提交拆卸',
            },
            name: 'DisassemblyManagementUnsubmitted',
            path: '/warehouse-management/disassembly-management/unsubmitted',
          },
        ],
      },
      {
        meta: {
          title: '批次号处理',
        },
        name: 'BatchNumberOperation',
        path: '/warehouse-management/batch-number-operation',
        children: [
          {
            component: () =>
              import(
                '#/views/warehouse-management/batch-number-operation/batch-number-operation-query/List.vue'
              ),
            meta: {
              title: '批次号处理查询',
            },
            name: 'BatchNumberOperationQuery',
            path: '/warehouse-management/batch-number-operation/batch-number-operation-query',
          },

          {
            component: () =>
              import(
                '#/views/warehouse-management/batch-number-operation/pending-submit/List.vue'
              ),
            meta: {
              title: '待提交批次号处理',
            },
            name: 'BatchNumberOperationPendingSubmit',
            path: '/warehouse-management/batch-number-operation/pending-submit',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/batch-number-operation/pending-execution/List.vue'
              ),
            meta: {
              title: '待处理批次号',
            },
            name: 'BatchNumberOperationPendingExecution',
            path: '/warehouse-management/batch-number-operation/pending-execution',
          },
        ],
      },
      {
        meta: {
          title: '库存调拨',
        },
        name: 'InventoryTransfer',
        path: '/warehouse-management/inventory-transfer',
        children: [
          {
            component: () =>
              import(
                '#/views/warehouse-management/inventory-transfer/query/List.vue'
              ),
            meta: {
              title: '库存调拨查询',
            },
            name: 'InventoryTransferQuery',
            path: '/warehouse-management/inventory-transfer/query',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/inventory-transfer/detail/List.vue'
              ),
            meta: {
              title: '库存调拨明细',
            },
            name: 'InventoryTransferDetail',
            path: '/warehouse-management/inventory-transfer/detail',
          },
          {
            component: () =>
              import(
                '#/views/warehouse-management/inventory-transfer/unsubmitted/List.vue'
              ),
            meta: {
              title: '待提交库存调拨',
            },
            name: 'InventoryTransferUnsubmitted',
            path: '/warehouse-management/inventory-transfer/unsubmitted',
          },
        ],
      },
    ],
  },
];

export default routes;
