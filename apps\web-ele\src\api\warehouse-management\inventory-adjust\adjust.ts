import type { Recordable } from '@vben/types';

import { warehousePath } from '#/api/path';
import { requestClient } from '#/api/request';

/** 库存调整返回 */
export namespace InventoryAdjustment {
  /** 分页基本 */
  export interface PageBase {
    [key: string]: any;
    records: Array<any>;
    total: string;
    pageSize: string;
    pageNum: string;
    pages: string;
  }
  /** 库存调整分页数据 */
  export interface InventoryAdjustment {
    /**	库存调整单据ID */
    invcAdjustDocId: string;
    /** 库存调整单据编号 */
    invcAdjustDocNumber: string;
    /** 创建人ID */
    createUser: string;
    /** 创建人姓名 */
    createUserName: string;
    /** 提交人ID */
    submitUser: string;
    /** 提交人姓名 */
    submitUserName: string;
    /** 提交时间 时间格式：yyyy-MM-dd HH:mm	*/
    submitTime: string;
    /** 执行人ID */
    executorUser: string;
    /** 执行人姓名 */
    executorUserName: string;
    /** 执行时间 时间格式：yyyy-MM-dd HH:mm	*/
    execTime: string;
    /** 审核状态，写入当前流程所在节点 */
    auditStatus: string;
    /** 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;
    /** 流程实例ID */
    processInstanceId: string;
    /** 单据状态值，字典publicDocStatus */
    docStatus: string;
    /** 单据状态标签，字典publicDocStatus */
    docStatusLabel: string;
  }
  /** 查询待我提交的库存调整单据分页数据*/
  export interface MyDraftDoc {
    /** 当前页，默认第1页 */
    pageNum: string;
    /** 分页数，默认每一页默认10条 */
    pageSize: string;
    /** 库存调整单据id */
    invcAdjustDocId: string;
    /** 库存调整单据编号*/
    invcAdjustDocNumber: string;
    /** 创建人id*/
    createUser: string;
    /** 创建人姓名*/
    createUserName: string;
    /** 创建时间，时间格式：yyyy-MM-dd HH:mm */
    createTime: string;
    /** 单据状态值*/
    docStatus: string;
    /** 单据状态标签 */
    docStatusLabel: string;
  }
  /** 查询库存调整单据分页列表 */
  export interface InventoryAdjustmentPage extends PageBase {
    records: InventoryAdjustment[];
  }
  /** 查询待我提交的库存调整单据分页列表 */
  export interface MyDraftDocPage extends PageBase {
    records: MyDraftDoc[];
  }
  /** 调整返回*/
  export interface Adjust {
    /** 单据ID */
    docId: string;
    /** 单据编号 */
    docNumber: string;
  }
  /** 库存调整单据子项数据*/
  export interface InvcAdjustItem {
    /** 库存调整单据子项id */
    invcAdjustItemId: string;
    /** 库存调整单据id */
    invcAdjustDocId: string;
    /** 物料id */
    materialId: string;
    /** 物料编号 */
    materialCode: string;
    /** 物料名称 */
    materialName: string;
    /** 物料别名 */
    materialAlias: string;
    /** 物料图片id */
    pictureFileId: string;
    /** 规格型号 */
    materialSpecs: string;
    /** 物料属性值, 字典baseMaterialAttribute */
    materialAttribute: string;
    /** 物料属性标签, 字典baseMaterialAttribute */
    materialAttributeLabel: string;
    /** 物料大类值, 字典baseMaterialType */
    materialType: string;
    /** 物料大类标签, 字典baseMaterialType */
    materialTypeLabel: string;
    /** 基本单位值, 字典baseMaterialUnit */
    baseUnit: string;
    /** 基本单位标签, 字典baseMaterialUnit */
    baseUnitLabel: string;
    /** 物料细类 */
    materialCategory: string;
    /** 物料细类名称 */
    materialCategoryName: string;
    /** 仓库id */
    warehouseId: string;
    /** 仓库编号 */
    warehouseCode: string;
    /** 仓库名称 */
    warehouseName: string;
    /** 库位id */
    locationId: string;
    /** 库位编号 */
    locationCode: string;
    /** 库位名称 */
    locationName: string;
    /** 批次号 */
    batchNumber: string;
    /** 现有库存量 */
    inventory: number;
    /** 调整数量 */
    quantity: number;
    /** 调整前库存量 */
    oldInventory: number;
    /** 调整后库存量 */
    newInventory: number;
    /** 调整原因 */
    remark: string;
  }
  /** 库存调整单据详细信息 */
  export interface InvcAdjustDoc {
    /** 库存调整单据ID */
    invcAdjustDocId: string;
    /** 库存调整单据编号 */
    invcAdjustDocNumber: string;
    /** 创建人ID */
    createUser: string;
    /** 创建人姓名 */
    createUserName: string;
    /** 提交人ID */
    submitUser: string;
    /** 提交人姓名 */
    submitUserName?: string;
    /** 执行人ID */
    executorUser: string;
    /** 执行人姓名 */
    executorUserName: string;
    /** 是否提交 */
    isSubmit: boolean;
    /** 创建时间，时间格式：yyyy-MM-dd HH:mm */
    createTime: string;
    /** 提交时间，时间格式：yyyy-MM-dd HH:mm */
    submitTime: string;
    /** 执行时间，时间格式：yyyy-MM-dd HH:mm */
    execTime: string;
    /** 单据审核状态 */
    auditStatus: string; // 可选字段
    /** 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;
    /** 审核流程实例id */
    processInstanceId: string;
    /** 单据状态值，字典publicDocStatus */
    docStatus: string;
    /** 单据状态标签，字典publicDocStatus */
    docStatusLabel: string;
    /** 备注(原因) */
    remark: string;
    /** 附件流水号 */
    serialNumber: string;
    /**	库存调整子项列表 */
    invcAdjustItemList: InvcAdjustItem[];
  }
  /** 库存调整单据明细 records数据 */
  export interface InvcAdjustItemRecords extends InvcAdjustDoc, InvcAdjustItem {
    /** 提交人部门id */
    submitUserDeptId: string;
    /** 提交人部门名称 */
    submitUserDeptName: string;
    /**	执行人部门ID */
    executorUserDeptId: string;
    /**	执行人部门名称 */
    executorUserDeptName: string;
  }
  /** 库存调整单据明细分页列表*/
  export interface InvcAdjustItemPage extends PageBase {
    records: InvcAdjustItemRecords[];
  }
}

/** 提交库存调整单据 */
export async function submitInvcAdjustDoc(params: Recordable<any>) {
  return requestClient.post<InventoryAdjustment.Adjust>(
    `${warehousePath}/wm/invc/adjust/submitInvcAdjustDoc`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}
/** 暂存或修改存调整单据 */
export async function saveOrModInvcAdjustDoc(params: Recordable<any>) {
  return requestClient.post<InventoryAdjustment.Adjust>(
    `${warehousePath}/wm/invc/adjust/saveOrModInvcAdjustDoc`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}
/** 查询待我提交的库存调整单据分页列表 (查询自己)*/
export async function getMyDraftDocPage(params: Recordable<any>) {
  return requestClient.post<InventoryAdjustment.MyDraftDocPage>(
    `${warehousePath}/wm/invc/adjust/getMyDraftDocPage`,
    params,
  );
}
/** 查询库存调整单据分页列表 */
export async function getInvcAdjustDocPage(params: Recordable<any>) {
  return requestClient.post<InventoryAdjustment.InventoryAdjustmentPage>(
    `${warehousePath}/wm/invc/adjust/getInvcAdjustDocPage`,
    params,
  );
}
/** 导出库存调整单据列表 */
export async function exportInvcAdjustDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/invc/adjust/exportInvcAdjustDoc`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
/** 导出库存调整单据明细列表 */
export async function exportInvcAdjustItem(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/invc/adjust/exportInvcAdjustItem`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
} /** 导出待我提交的库存调整单据列表 */
export async function exportMyDraftDocPage(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/invc/adjust/exportMyDraftDocPage`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
/** 执行库存调整单据 */
export async function execInvcAdjustDoc(invcAdjustDocId: string) {
  return requestClient.get<InventoryAdjustment.Adjust>(
    `${warehousePath}/wm/invc/adjust/execInvcAdjustDoc/${invcAdjustDocId}`,
  );
}
/** 批量删除待我提交的库存调整单据 */
export async function batchDelInvcAdjustDoc(invcAdjustDocIds: string[]) {
  return requestClient.get<InventoryAdjustment.Adjust>(
    `${warehousePath}/wm/invc/adjust/batchDelInvcAdjustDoc`,
    {
      params: {
        invcAdjustDocIds,
      },
    },
  );
}
/** 获取库存调整单据子项列表*/
export async function getInvcAdjustItemList(params: Recordable<any>) {
  return requestClient.post<InventoryAdjustment.InvcAdjustItem[]>(
    `${warehousePath}/wm/invc/adjust/item/getInvcAdjustItemList/`,
    params,
  );
}
/** 获取库存调整单据详细信息*/
export async function getInvcAdjustDoc(
  invcAdjustDocId: string,
  invcAdjustDocNumber?: string,
  isQueryItem?: boolean,
) {
  return requestClient.get<InventoryAdjustment.InvcAdjustDoc>(
    `${warehousePath}/wm/invc/adjust/getInvcAdjustDoc`,
    {
      params: {
        invcAdjustDocId,
        invcAdjustDocNumber,
        isQueryItem,
      },
    },
  );
}
/** 删除待我提交的库存调整单据 */
export async function delInvcAdjustDoc(invcAdjustDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/invc/adjust/delInvcAdjustDoc/${invcAdjustDocId}`,
  );
}
/** 关闭库存调整单据 */
export async function closeInvcAdjustDoc(invcAdjustDocId: string) {
  return requestClient.get<InventoryAdjustment.Adjust>(
    `${warehousePath}/wm/invc/adjust/closeInvcAdjustDoc/${invcAdjustDocId}`,
  );
}

/** 查询库存调整单据明细分页列表 */
export async function getInvcAdjustItemPage(params: Recordable<any>) {
  return requestClient.post<InventoryAdjustment.InvcAdjustItemPage>(
    `${warehousePath}/wm/invc/adjust/item/getInvcAdjustItemPage`,
    params,
  );
}
