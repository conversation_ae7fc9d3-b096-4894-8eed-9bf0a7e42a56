<script setup lang="ts">
import { ref } from 'vue';

import { ElMessage, ElTable, ElTableColumn } from 'element-plus';

import { getInvcByWarehouseIdAndMaterialId, getWarehouseDetail } from '#/api';
import TipsPopover from '#/components/tips-popover/index.vue';

const props = defineProps({
  /** 库存调拨数据 */
  transferDocDetail: {
    type: [Object, null],
    default: null,
  },
  /** 物料Id */
  materialId: {
    type: String,
    default: '',
  },
  /** 调出仓库 */
  oldWarehouseId: {
    type: String,
    default: '',
  },
  /** 调入仓库 */
  targetWarehouseId: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
/** 仓库库存数据 */
const tableData = ref<any>([]);
/** 调出仓库名称 */
const outWarehouseName = ref('');
/** 调入仓库名称 */
const inWarehouseName = ref('');

/** 查询仓库详情信息 */
const queryWarehouseDetails = async (
  oldWarehouseId: string,
  targetWarehouseId: string,
) => {
  const oldWarehouse = await getWarehouseDetail(oldWarehouseId);
  outWarehouseName.value = oldWarehouse?.warehouseName ?? '';
  const targetWarehouse = await getWarehouseDetail(targetWarehouseId);
  inWarehouseName.value = targetWarehouse?.warehouseName ?? '';
};

/** 查询的当前库存 */
const queryCurrentInventory = async (
  materialId: string,
  oldWarehouseId: string,
  targetWarehouseId: string,
) => {
  // 获取调出仓库库存
  const outWarehouse = await getInvcByWarehouseIdAndMaterialId(
    oldWarehouseId,
    materialId,
    false,
    false,
    false,
  );
  // 获取调入仓库库存
  const inWarehouse = await getInvcByWarehouseIdAndMaterialId(
    targetWarehouseId,
    materialId,
    false,
    false,
    false,
  );

  tableData.value = [
    {
      indicator: '当前库存量/可用量',
      warehouseA: {
        stock: outWarehouse?.inventory ?? 0,
        available: outWarehouse?.availableInventory ?? 0,
      },
      warehouseB: {
        stock: inWarehouse?.inventory ?? 0,
        available: inWarehouse?.availableInventory ?? 0,
      },
    },
  ];
};

/** 库存调拨物料仓库分析 */
const showParentStock = async () => {
  try {
    loading.value = true;
    if (props.transferDocDetail) {
      // 已有单据数据
    } else {
      // 新增单据
      if (
        !props.materialId ||
        !props.oldWarehouseId ||
        !props.targetWarehouseId
      ) {
        return;
      }
      // 获取仓库名
      await queryWarehouseDetails(
        props.oldWarehouseId,
        props.targetWarehouseId,
      );
      // 获取仓库实时库存情况
      await queryCurrentInventory(
        props.materialId,
        props.oldWarehouseId,
        props.targetWarehouseId,
      );
    }
  } catch {
    ElMessage.error('加载库存信息失败');
  } finally {
    loading.value = false;
  }
};

// const tableData = ref([
//   {
//     indicator: '当前库存量/可用量',
//     warehouseA: { stock: 15, available: 14 },
//     warehouseB: { stock: 437, available: 433 },
//   },
//   {
//     indicator: '调拨后库存量/可用量',
//     warehouseA: { stock: 9, available: 8, change: -6 },
//     warehouseB: { stock: 443, available: 439, change: +6 },
//   },
// ]);
</script>

<template>
  <TipsPopover
    @show="showParentStock"
    placement="bottom-start"
    icon-size="24px"
    width="430px"
  >
    <div v-loading="loading">
      <ElTable
        :data="tableData"
        header-align="center"
        style="width: 100%"
        size="small"
      >
        <!-- 指标列（行标题）-->
        <ElTableColumn label="" prop="indicator" width="160" />
        <!-- 仓库列：调出仓库 -->
        <ElTableColumn :label="outWarehouseName" prop="warehouseA" width="120">
          <template #default="scope">
            <span>
              <span
                :class="{
                  'text-red-500': scope.row.warehouseA.stock === 0,
                }"
              >
                {{ scope.row.warehouseA.stock }}
              </span>
              /
              <span
                :class="{
                  'text-red-500': scope.row.warehouseA.available === 0,
                }"
              >
                {{ scope.row.warehouseA.available }}
              </span>
            </span>
            <template v-if="scope.row.warehouseA.change !== undefined">
              (
              <span
                class="change-tag"
                :class="{
                  'text-red-500': scope.row.warehouseA.change < 0,
                  'text-green-500': scope.row.warehouseA.change > 0,
                }"
              >
                {{ scope.row.warehouseA.change > 0 ? '+' : '' }}
                {{ scope.row.warehouseA.change }}
              </span>
              )
            </template>
          </template>
        </ElTableColumn>
        <!-- 仓库列：调入仓库 -->
        <ElTableColumn :label="inWarehouseName" prop="warehouseB" width="120">
          <template #default="scope">
            <span>
              <span
                :class="{
                  'text-red-500': scope.row.warehouseB.stock === 0,
                }"
              >
                {{ scope.row.warehouseB.stock }}
              </span>
              /
              <span
                :class="{
                  'text-red-500': scope.row.warehouseB.available === 0,
                }"
              >
                {{ scope.row.warehouseB.available }}
              </span>
            </span>
            <template v-if="scope.row.warehouseB.change !== undefined">
              (
              <span
                class="change-tag"
                :class="{
                  'text-red-500': scope.row.warehouseB.change < 0,
                  'text-green-500': scope.row.warehouseB.change > 0,
                }"
              >
                {{ scope.row.warehouseB.change > 0 ? '+' : '' }}
                {{ scope.row.warehouseB.change }}
              </span>
              )
            </template>
          </template>
        </ElTableColumn>
      </ElTable>
    </div>
  </TipsPopover>
</template>
