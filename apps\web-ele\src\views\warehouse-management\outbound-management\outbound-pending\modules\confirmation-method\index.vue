<script lang="ts" setup>
import type { PropType } from 'vue';

import { onMounted, onUnmounted, ref } from 'vue';

import { ElMessage } from 'element-plus';

import {
  getExecQrCodeByOutBoundId,
  refreshExecQrCodeByOutBoundId,
} from '#/api/warehouse-management/index';
import { WS } from '#/utils/socket/common-socketio';

const props = defineProps({
  lastOutboundInfo: {
    type: Array as PropType<
      Array<{
        baseUnitLabel: string;
        entryQuantitySum: number;
        materialId: string;
        materialName: string;
        quantitySum: number;
      }>
    >,
    default: () => [],
  },
  outBoundDocId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['submitConfirmation', 'cancelConfirmation']);

/** 领料方式  00：输入单据出出库码，10：扫二维码 */
const collectorConfirmMethod = ref<string>('00');

/** 领料码 */
const collectValue = ref<string>('');

const loading = ref<boolean>(true);

/** 二维码base64 */
const qrCode = ref<string>('');

/** 二维码验证ID */
const uuidValue = ref<string>('');

/** 二维码有效期 */
const qrCodeValidDuration = ref<number>(0);

/** 二维码过期时间 */
const qrCodeExpireTime = ref<number>(0);

const timer = ref<null | ReturnType<typeof setTimeout>>(null);

/** 二维码是否过期 */
const qrCodeExpired = ref<boolean>(false);

const handleScanCode = (message: any) => {
  ElMessage.success('扫码成功');
  const content = JSON.parse(message.content);

  uuidValue.value = content.uuidValue;
  qrCodeExpired.value = false;
  qrCodeExpireTime.value = 0;
  qrCodeValidDuration.value = 0;
  clearTimer();

  emits('submitConfirmation');
};

onMounted(async () => {
  await WS.on('wm.out:scanCode', handleScanCode);
});

/** 加载二维码 */
const loadQrCode = async (refresh: boolean = false) => {
  loading.value = true;

  try {
    const outBoundDocId = props.outBoundDocId || '';
    if (!outBoundDocId) {
      ElMessage.error('出库单据ID不能为空');
      return;
    }
    const qrRes = await (refresh
      ? refreshExecQrCodeByOutBoundId(outBoundDocId)
      : getExecQrCodeByOutBoundId(outBoundDocId));

    qrCode.value = qrRes.qrCode;
    qrCodeValidDuration.value = qrRes.validDuration;
    // qrCodeValidDuration.value = 10;

    // 返回的单位是秒, 加上当前时间 再转为时间戳
    qrCodeExpireTime.value = qrCodeValidDuration.value * 1000 + Date.now();
    loading.value = false;
    qrCodeExpired.value = false;
  } catch {
    loading.value = false;
    qrCodeExpired.value = true;
  }
};

const clearTimer = () => {
  timer.value && clearInterval(timer.value);
  timer.value = null;
};

/** 切换tab */
const tabChange = async (v: string) => {
  collectValue.value = '';
  switch (v) {
    case '00': {
      clearTimer();
      break;
    }
    case '10': {
      if (qrCodeExpireTime.value > 0 && qrCodeExpireTime.value < Date.now()) {
        ElMessage.warning('二维码已过期,正在刷新二维码');
        await loadQrCode(true);
      }

      if (qrCodeValidDuration.value === 0) {
        qrCodeValidDuration.value = 0;
        qrCodeExpireTime.value = 0;
        await loadQrCode();
      }

      timer.value = setInterval(() => {
        if (qrCodeExpireTime.value > 0 && qrCodeExpireTime.value < Date.now()) {
          qrCodeExpired.value = true;
          clearTimer();
        }
      }, 1000);
      break;
    }
  }
};

// 清空数据
const clearData = () => {
  collectorConfirmMethod.value = '00';
  collectValue.value = '';
  qrCode.value = '';
  uuidValue.value = '';
  qrCodeValidDuration.value = 0;
  qrCodeExpireTime.value = 0;
  clearTimer();
};

onUnmounted(() => {
  clearData();
});

/** 获取数据 */
const getConfirmData = (): {
  collectorConfirmMethodData: string;
  collectValueData: string;
} => {
  const data = {
    collectorConfirmMethodData: '',
    collectValueData: '',
  };
  switch (collectorConfirmMethod.value) {
    case '00': {
      if (!collectValue.value) {
        ElMessage.warning('请输入领料码');
        return data;
      }
      data.collectorConfirmMethodData = collectorConfirmMethod.value;
      data.collectValueData = collectValue.value;
      break;
    }
    case '10': {
      if (!uuidValue.value) {
        ElMessage.warning('请扫描二维码');
        return data;
      }
      data.collectorConfirmMethodData = collectorConfirmMethod.value;
      data.collectValueData = uuidValue.value;
      break;
    }
  }

  return data;
};

const handleSubmit = async () => {
  if (collectorConfirmMethod.value === '00' && !collectValue.value) {
    ElMessage.warning('请输入领料码');
    return;
  }

  emits('submitConfirmation');
};

const handleCancel = () => {
  emits('cancelConfirmation');
};

/** 暴露方法 */
defineExpose({
  getConfirmData,
  clearData,
});
</script>

<template>
  <div>
    <div class="bg-primary-50/80 mb-3 rounded-lg p-2 shadow-sm">
      <div class="mb-2">
        <span class="font-bold">
          共领料
          <span class="text-primary-500">{{ lastOutboundInfo.length }}</span>
          种
        </span>
        <span>
          数量合计
          <span class="text-primary-500">
            {{
              lastOutboundInfo.reduce(
                (acc, item) => acc + item.entryQuantitySum,
                0,
              )
            }}
          </span>
        </span>
      </div>
      <div
        class="text-sm"
        v-for="item in lastOutboundInfo"
        :key="item.materialId"
      >
        {{ item.materialName }}应发 {{ item.quantitySum }}
        {{ item.baseUnitLabel }},实发 {{ item.entryQuantitySum }}
        {{ item.baseUnitLabel }}
      </div>
    </div>

    <div class="h-[250px]">
      <span class="text-sm font-bold"> 领料人确认 </span>
      <el-tabs
        v-model="collectorConfirmMethod"
        tab-position="left"
        @tab-change="tabChange"
      >
        <el-tab-pane label="领料码" name="00">
          <div class="flex h-full items-center justify-center">
            <el-input
              v-model="collectValue"
              placeholder="请输入领料码"
              class="max-w-[300px]"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="扫码" name="10">
          <div
            class="flex h-full items-center justify-center"
            v-loading="loading"
          >
            <div
              class="cursor-pointer rounded-lg bg-gray-50 p-4"
              @click="loadQrCode(true)"
            >
              <el-image
                class="h-[200px] w-[200px] object-contain"
                :src="`data:image/png;base64,${qrCode}`"
                title="点击刷新"
                v-if="!qrCodeExpired"
              />

              <el-image class="h-[200px] w-[200px] object-contain" v-else>
                <template #error>
                  <div class="flex h-full items-center justify-center">
                    已过期,点击刷新
                  </div>
                </template>
              </el-image>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="mt-4 flex justify-end gap-2">
      <el-button type="info" @click="handleCancel"> 取消 </el-button>
      <el-button
        type="primary"
        @click="handleSubmit"
        :disabled="!collectValue"
        v-show="collectorConfirmMethod === '00'"
      >
        确认出库
      </el-button>
    </div>
  </div>
</template>
