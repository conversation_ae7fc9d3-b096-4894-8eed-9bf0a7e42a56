<script setup lang="ts">
import { computed, defineAsyncComponent, markRaw, ref } from 'vue';

import SelectMaterial from './select-material/View.vue';

const props = defineProps({
  docCode: {
    type: String,
    default: '',
  },
  batchnumDocId: {
    type: String,
    default: '',
  },
  batchnumDocNumber: {
    type: String,
    default: '',
  },
});

// 当前操作
const currentOperation = ref<string>(props.docCode || 'WM0050');

const selectMaterialRef = ref();
const batchNumberOperationRef = ref();

const BatchNumberOperation = computed(() => {
  switch (currentOperation.value) {
    case 'WM0050': {
      return markRaw(
        defineAsyncComponent(() => import('./merge-batch-number/View.vue')),
      );
    }
    case 'WM0051': {
      return markRaw(
        defineAsyncComponent(() => import('./split-batch-number/View.vue')),
      );
    }
    default: {
      return markRaw(
        defineAsyncComponent(() => import('./merge-batch-number/View.vue')),
      );
    }
  }
});
</script>

<template>
  <div class="h-full">
    <SelectMaterial
      ref="selectMaterialRef"
      :batchnum-doc-id="batchnumDocId"
      :batchnum-doc-number="batchnumDocNumber"
    />

    <div class="border-primary-500 rounded-lg border p-2">
      <BatchNumberOperation
        ref="batchNumberOperationRef"
        :batchnum-doc-id="batchnumDocId"
        :batchnum-doc-number="batchnumDocNumber"
      />
    </div>
  </div>
</template>
