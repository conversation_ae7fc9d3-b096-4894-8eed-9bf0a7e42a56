<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getInvcAdjustDoc } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  /** 库存调整单据id */
  invcAdjustDocId: {
    type: String,
    default: '',
  },
  /** 库存调整单据编号 */
  invcAdjustDocNumber: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
/** 调整信息表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});

/** 校验表单 */
const validateForm = async () => {
  const verification = await formApi.validate();
  // 等待文件上传完成
  const serialNumber: any = await formApi?.getFieldComponentRef('serialNumber');
  const isCompleted = await serialNumber?.getCompleteStatus();
  if (!isCompleted) {
    ElMessage.warning('请等待附件上传完成');
    return;
  }
  return verification.valid;
};
/** 获取表单数据 */
const getFormData = async () => {
  const data = await formApi.getValues();
  return data;
};

/** 根据库存调整单据id获取数据 */
const getData = async () => {
  try {
    loading.value = true;
    const data = await getInvcAdjustDoc(
      props.invcAdjustDocId,
      props.invcAdjustDocNumber,
    );
    // 赋值
    formApi.setValues(data);
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  if (props.invcAdjustDocId || props.invcAdjustDocNumber) {
    getData();
  }
});
defineExpose({
  getFormData,
  validateForm,
  formApi,
});
</script>

<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>调整单信息</span>
    </template>
    <Form />
  </FormCard>
</template>
