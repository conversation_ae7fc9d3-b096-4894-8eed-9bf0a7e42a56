import { ElMessage, ElMessageBox } from 'element-plus';

import {
  closeInvcAdjustDoc,
  delInvcAdjustDoc,
  execInvcAdjustDoc,
  getInvcAdjustDoc,
  submitInvcAdjustDoc,
} from '#/api/warehouse-management';

/** 单据状态Tag类型*/
export const docStatusDict: { [key: string]: any } = {
  /** 待提交 */
  '00': 'warning',
  /** 待审核 */
  '10': 'primary',
  /** 审核驳回 */
  '20': 'danger',
  /** 审核通过待执行 */
  '30': 'primary',
  /** 已完成 */
  '40': 'success',
  /** 已关闭 */
  '90': 'info',
};
/** 提示框 */
export const confirm = (content: string, title: string) => {
  return new Promise((resolve) => {
    ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
      .then(() => {
        resolve(true);
      })
      .catch(() => {});
  });
};

/** 确认执行 */
export const confirmExecute = async (
  invcAdjustDocId: string,
  formModalApi: any,
  gridApi: any,
) => {
  try {
    if (await confirm('确定执行吗？', '提示')) {
      await execInvcAdjustDoc(invcAdjustDocId);
    }
    ElMessage.success('执行成功');
    formModalApi.close();
    gridApi.query();
  } catch {
    ElMessage.error('执行失败');
  }
};
/** 取消单据 */
export const cancelDoc = async (
  invcAdjustDocId: string,
  formModalApi: any,
  gridApi: any,
) => {
  try {
    if (await confirm('确定取消吗？', '提示')) {
      await closeInvcAdjustDoc(invcAdjustDocId);
    }
    ElMessage.success('取消成功');
    formModalApi.close();
    gridApi.query();
  } catch {
    ElMessage.error('取消失败');
  }
};

/** 删除单据 */
export const deleteDoc = async (
  invcAdjustDocId: string,
  formModalApi: any,
  gridApi: any,
) => {
  try {
    if (await confirm('确定删除吗？', '提示')) {
      await delInvcAdjustDoc(invcAdjustDocId);
      ElMessage.success('删除成功');
      formModalApi.close();
      gridApi.query();
    }
  } catch {
    ElMessage.error('删除失败');
  }
};

/** 提交单据 （直接提交）*/
export const submitDoc = async (
  invcAdjustDocId: string,
  formModalApi: any,
  gridApi: any,
) => {
  try {
    if (await confirm('确定提交吗？', '提示')) {
      // 获取单据数据
      const data = await getInvcAdjustDoc(invcAdjustDocId, '', true);
      await submitInvcAdjustDoc(data);
      ElMessage.success('提交成功');
      formModalApi.close();
      gridApi.query();
    }
  } catch {
    ElMessage.error('提交失败');
  }
};

/** 打开模态框 */
export const openModal = (
  /** 模态框API */
  formModalApi: any,
  /** 是否显示确认按钮 */
  showConfirmButton: boolean,
  /** 标题 */
  title: string,
) => {
  formModalApi
    .setState({
      showConfirmButton,
      title,
    })
    .open();
};
/**
 * 日期禁用函数
 * @param isEnd 是否是结束时间
 */
export const createDisabledDate = (isEnd: boolean, thisTime: any) => {
  return (time: Date) => {
    if (!thisTime.endTime && !isEnd) {
      return false;
    }
    // 是结束时间
    return isEnd
      ? time.getTime() < new Date(thisTime.startTime).getTime()
      : time.getTime() > new Date(thisTime.endTime).getTime();
  };
};
