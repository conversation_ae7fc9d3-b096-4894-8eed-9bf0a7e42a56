<script setup lang="ts">
import { ref, watch } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElLink } from 'element-plus';

const props = defineProps({
  /** 物料名称 */
  materialName: {
    type: String,
    default: '',
  },
  /** 物料编号 */
  materialCode: {
    type: String,
    default: '',
  },
  /** 图片ID */
  pictureFileId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['click']);

/** 图片Id */
const pictureFileId = ref(props.pictureFileId);

/** 监听props pictureFileId改变 */
watch(
  () => props.pictureFileId,
  (newVal) => {
    pictureFileId.value = newVal;
  },
);

const onClick = () => {
  emits('click');
};
</script>

<template>
  <div class="flex size-full flex-row items-center gap-2">
    <span class="min-w-[120px] flex-1">
      <ElLink type="primary" @click="onClick">
        <span>{{ props.materialName }}</span>
        <span v-if="props.materialCode">（{{ props.materialCode }}）</span>
      </ElLink>
    </span>
    <ImageViewer
      v-if="pictureFileId"
      :img-id="pictureFileId"
      img-css="size-7 flex-shrink-0"
      :show-thumbnail="false"
    />
  </div>
</template>
