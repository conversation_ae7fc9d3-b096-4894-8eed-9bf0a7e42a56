<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';

import { CountTo } from '@vben/common-ui';

import {
  getOutActualMaterialItem,
  getOutActualMaterialItemNum,
  getOutBoundDocNum,
} from '#/api/warehouse-management';
import TimeSelect from '#/components/time-select/Index.vue';
import { WS } from '#/utils/socket/common-socketio';
import { fetchPeriodTime } from '#/utils/trans-period-time';

export default defineComponent({
  components: { CountTo, TimeSelect },
  emits: ['cardClick'],
  setup(_props, { emit }) {
    const wsType = [
      'wm.inbound.docstatus.pending.add',
      'wm.inbound.docstatus.finished',
      'wm.inbound.docstatus.cancelAudit',
      'wm.inbound.docstatus.pending.cancelReject',
      'wm.inbound.docstatus.close',
      'wm.inbound.export.pending',
      'wm.inbound.export.finished',
    ];

    const DOC_STATUS = 'collected';

    const timeSelectRef = ref();

    const outBoundDocLoading = ref(true);
    const actualLoading = ref(true);
    const quantityLoading = ref(true);
    const outBoundDocNum = ref(0); // 单据
    const actualItemNum = ref(0); // 物料类别
    const quantitySum = ref(0); // 物料数量

    const fetchOutBoundDocNum = async (params: Object) => {
      try {
        const outBoundDocNumResult = await getOutBoundDocNum({
          docStatusList: DOC_STATUS,
          ...params,
        });
        outBoundDocNum.value = outBoundDocNumResult.outBoundDocNum || 0;
      } catch (error) {
        console.error('获取已出库单据数据失败:', error);
      } finally {
        outBoundDocLoading.value = false;
      }
    };

    const fetchActualItemNum = async (params: Object) => {
      try {
        const actualItemNumResult = await getOutActualMaterialItemNum(params);
        actualItemNum.value = actualItemNumResult.materialNum || 0;
      } catch (error) {
        console.error('获取已出库物料项数据失败:', error);
      } finally {
        actualLoading.value = false;
      }
    };

    const fetchActualMaterialNum = async (params: Object) => {
      try {
        const quantitySumResult = await getOutActualMaterialItem(params);
        quantitySum.value = quantitySumResult.quantitySum || 0;
      } catch (error) {
        console.error('获取已出库物料数量数据失败:', error);
      } finally {
        quantityLoading.value = false;
      }
    };

    const fetchData = async () => {
      const params = timeSelectRef.value?.getValues();
      await fetchOutBoundDocNum(params);
      await fetchActualItemNum(params);
      await fetchActualMaterialNum(params);
    };

    const searchChange = (_values: any) => {
      fetchData();
    };

    const handleCardClick = async (name: string) => {
      try {
        const periodData = timeSelectRef.value.getValues();
        const times = await fetchPeriodTime(
          periodData.executorTimePeriodType,
          periodData.executorTimePeriodUnit,
          periodData.executorTimeInterval,
        );

        emit('cardClick', {
          name,
          params: {
            docStatusList: DOC_STATUS,
            executorStartTime: times.timeStart,
            executorEndTime: times.timeEnd,
          },
          attrs: {
            wrapperClass:
              'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4',
            collapsed: true,
            collapsedRows: 2,
            showCollapseButton: true,
          },
        });
      } catch (error) {
        console.error('获取周期时间失败:', error);
      }
    };

    onMounted(async () => {
      await WS.on(wsType, fetchData);

      fetchData();
    });

    return {
      outBoundDocNum,
      actualItemNum,
      quantitySum,
      searchChange,
      timeSelectRef,
      quantityLoading,
      actualLoading,
      outBoundDocLoading,
      handleCardClick,
    };
  },
});
</script>
<template>
  <div
    class="hover:bg-primary-100 bg-primary-50 cursor-pointer rounded-lg px-3 py-2 text-black"
  >
    <div class="grid grid-cols-2 gap-4">
      <div class="pb-2 font-bold">已出库</div>
      <TimeSelect ref="timeSelectRef" @time-change="searchChange" />
    </div>
    <div class="mt-1 flex items-center justify-around text-sm">
      <div class="text-center" @click="handleCardClick('outbound-documents')">
        <div class="flex h-7 items-end justify-center">
          <span class="text-xl font-bold">
            <CountTo
              v-loading="outBoundDocLoading"
              :start-val="0"
              :end-val="outBoundDocNum"
              :duration="1500"
              separator=""
            />
          </span>
          <span class="mb-1 ml-1 text-xs">张</span>
        </div>
        <p>单据</p>
      </div>
      <div class="text-center" @click="handleCardClick('outbound-details')">
        <div class="flex h-7 items-end justify-center">
          <span class="text-xl font-bold">
            <CountTo
              v-loading="actualLoading"
              :start-val="0"
              :end-val="actualItemNum"
              :duration="1500"
              separator=""
            />
          </span>
          <span class="mb-1 ml-1 text-xs">项</span>
        </div>
        <p>物料项</p>
      </div>
      <div class="text-center" @click="handleCardClick('outbound-details')">
        <div class="flex h-7 items-end justify-center">
          <span class="text-xl font-bold">
            <CountTo
              v-loading="quantityLoading"
              :start-val="0"
              :end-val="quantitySum"
              :duration="1500"
              separator=""
            />
          </span>
        </div>
        <p>物料数量</p>
      </div>
    </div>
  </div>
</template>
