<script setup lang="ts">
import MaterialForm from '#/views/warehouse-management/basic-data/material/modules/material-form/index.vue';

import DetailsForm from './details-form/index.vue';
import SituationForm from './situation-form/index.vue';

const props = defineProps({
  /** 仓库id */
  warehouseId: {
    type: String,
    default: '',
  },
  /** 物料id */
  materialId: {
    type: String,
    default: '',
  },
});

defineExpose({
  props,
});
</script>

<template>
  <div>
    <MaterialForm :material-id="materialId" />
    <SituationForm :material-id="materialId" :warehouse-id="warehouseId" />
    <DetailsForm :material-id="materialId" :warehouse-id="warehouseId" />
  </div>
</template>
