<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getInOutReqDocDetailInBound } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  /** 其它出入库申请单id */
  inOutReqDocId: {
    type: String,
    default: '',
  },
  /** 其它出入库申请单编号*/
  inOutReqDocNumber: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
/** 文件上传ref*/
const fileRef = ref();

/** 调整信息表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass:
    'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3',
});

/** 校验表单 */
const validateForm = async () => {
  const verification = await formApi.validate();
  // 等待文件上传完成
  const serialNumber: any = await formApi?.getFieldComponentRef('serialNumber');
  const isCompleted = await serialNumber?.getCompleteStatus();
  if (!isCompleted) {
    ElMessage.warning('请等待附件上传完成');
    return;
  }
  return verification.valid;
};
/** 获取表单数据 */
const getFormData = async () => {
  const data = await formApi.getValues();
  return data;
};
/** 根据其它出入库申请单编号id获取数据 */
const getData = async () => {
  try {
    loading.value = true;
    const data = await getInOutReqDocDetailInBound(
      props.inOutReqDocId,
      props.inOutReqDocNumber,
    );
    // 设置表单schema
    formApi.setState({ schema: useFormSchema(data.docStatus === '80') });
    // 赋值
    formApi.setValues(data);
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
onMounted(async () => {
  if (props.inOutReqDocId || props.inOutReqDocNumber) {
    getData();
  }
});
defineExpose({
  getFormData,
  validateForm,
  fileRef,
  formApi,
});
</script>
<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>其它入库申请信息</span>
    </template>
    <Form />
  </FormCard>
</template>
