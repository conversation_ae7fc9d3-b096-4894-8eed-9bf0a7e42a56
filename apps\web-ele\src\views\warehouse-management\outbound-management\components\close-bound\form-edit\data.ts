import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { z } from '@girant/adapter';

/** 表单 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        const val = props.modelValue;
        const showText = val || '/';
        return h('div', null, showText);
      },
      fieldName: 'outBoundDocNumber',
      label: '出库单号',
    },
    {
      component: (props: any) => {
        const val = props.modelValue;
        const showText = val || '/';
        return h('div', null, showText);
      },
      fieldName: 'origDocTypeName',
      label: '出库类型',
    },

    {
      component: 'Input',
      fieldName: 'materialUserName',
      label: '物料使用人',
    },
    {
      component: 'Input',
      fieldName: 'remarkOptionList',
      label: '取消原因',
      formItemClass: 'col-span-full',
      rules: 'selectRequired',
    },
    {
      component: 'Textarea',
      componentProps: {
        autosize: { minRows: 3 },
        maxlength: 500,
        placeholder: '请输入',
        showWordLimit: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.remark) {
            return z.string().max(500, { message: '长度不超过500' });
          }
          return null;
        },
        triggerFields: ['remark'],
      },
      fieldName: 'remark',
      formItemClass: 'col-span-full',
      label: '取消原因说明',
    },

    {
      component: 'Upload',
      fieldName: 'serialNumber',
      formItemClass: 'col-span-full',
      label: '附件',
    },
  ];
}
