<script setup lang="ts">
import type { PropType } from 'vue';

import type { materialConfig } from '#/api/warehouse-management';

import { ref } from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';

import { modMaterialConfig } from '#/api/warehouse-management';

import ConfigFormEdit from './config-form/form-edit/index.vue';
import ConfigFormView from './config-form/form-view/index.vue';
import MaterialForm from './material-form/index.vue';

const props = defineProps({
  /** 物料id */
  materialId: {
    type: String,
    default: '',
  },
  isView: {
    type: Boolean,
    default: false,
  },
  /** 选择的物料配置 */
  materialConfigData: {
    type: Object as PropType<materialConfig.materialPageRecords>,
    default: () => ({}),
  },
});
const emit = defineEmits(['submitSuccess']);
const loading = ref(false);
const materialRef = ref<InstanceType<typeof MaterialForm>>();
const configRef = ref<InstanceType<typeof ConfigFormEdit>>();

/** 确认框 */
const confirm = (content: string, title: string) => {
  return new Promise((resolve) => {
    ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
      .then(() => {
        resolve(true);
      })
      .catch(() => {});
  });
};
/** 校验表单 */
const validateForm = async () => {
  // 校验表单
  const [verification, verification2] = await Promise.all([
    materialRef.value?.formApi.validate(),
    configRef.value?.formApi.validate(),
  ]);
  if (!verification?.valid || !verification2?.valid) {
    ElMessage.error('请填写完整表单');
    return false;
  }
  // 获取表单数据
  const configData = await configRef.value?.getAllFormValues();
  // 校验安全预警仓库的选择是否重复
  const hasDuplicate = configData?.safetyInventoryWarnList?.some(
    (item: { warehouseId: string }, index: any, arr: any[]) => {
      return arr.findIndex((i) => i.warehouseId === item.warehouseId) !== index;
    },
  );
  // 校验呆滞期分析仓库的选择是否重复
  const hasDuplicate2 = configData?.slowMovingAnalysisList?.some(
    (item: { warehouseId: string }, index: any, arr: any[]) => {
      return arr.findIndex((i) => i.warehouseId === item.warehouseId) !== index;
    },
  );
  if (hasDuplicate) {
    ElMessage.error('安全预警仓库不能重复');
    return false;
  }
  if (hasDuplicate2) {
    ElMessage.error('呆滞期分析仓库不能重复');
    return false;
  }
  return true;
};
/** 提交表单 */
const onSubmit = async () => {
  try {
    // 校验表单
    if (!(await validateForm())) return;
    // 获取表单数据
    const [data, data2] = await Promise.all([
      materialRef.value?.formApi.getValues(),
      configRef.value?.getAllFormValues(),
    ]);
    const isUpdate = !!props.materialId;
    const confirmText = isUpdate ? '确认提交编辑吗？' : '确认提交新增吗？';
    if (await confirm(confirmText, '提示')) {
      loading.value = true;
      await modMaterialConfig({
        materialId: isUpdate ? props.materialId : data!.materialId,
        ...data2,
      });
      ElMessage.success(isUpdate ? '编辑成功' : '添加成功');
      emit('submitSuccess');
    }
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

defineExpose({
  onSubmit,
  validateForm,
  props,
  materialRef,
  configRef,
});
</script>

<template>
  <div v-loading="loading">
    <MaterialForm ref="materialRef" :material-id="materialId" />
    <ConfigFormView ref="configRef" :material-id="materialId" v-if="isView" />
    <ConfigFormEdit
      ref="configRef"
      :material-id="materialId"
      :material-config-data="materialConfigData"
      v-else
    />
  </div>
</template>
