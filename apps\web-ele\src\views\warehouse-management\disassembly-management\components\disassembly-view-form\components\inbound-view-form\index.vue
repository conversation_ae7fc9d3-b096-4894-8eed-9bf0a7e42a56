<script setup lang="ts">
import ColseForm from '#/views/warehouse-management/inbound-management/inbound-documents/modules/inbound-info/index.vue';
import CompletedForm from '#/views/warehouse-management/inbound-management/stock-already/modules/inbound-info/index.vue';
import MaterialForm from '#/views/warehouse-management/inbound-management/stock-already/modules/materials-info/index.vue';
import PendingForm from '#/views/warehouse-management/inbound-management/stock-pending/modules/inbound-info/index.vue';
import PendingMaterialForm from '#/views/warehouse-management/inbound-management/stock-pending/modules/materials-info/index.vue';

const props = defineProps({
  inBoundDocId: {
    type: String,
    default: '',
  },
  inBoundDocNumber: {
    type: String,
    default: '',
  },
  docStatus: {
    type: String,
    default: '',
  },
});
</script>

<template>
  <PendingForm
    v-if="props.docStatus === '00'"
    :in-bound-doc-id="props.inBoundDocId"
    :in-bound-doc-number="props.inBoundDocNumber"
  />

  <CompletedForm
    v-if="props.docStatus === '10'"
    :in-bound-doc-id="props.inBoundDocId"
    :in-bound-doc-number="props.inBoundDocNumber"
  />

  <ColseForm
    v-if="props.docStatus === '05' || props.docStatus === '99'"
    :in-bound-doc-id="props.inBoundDocId"
    :in-bound-doc-number="props.inBoundDocNumber"
  />

  <PendingMaterialForm
    v-if="props.docStatus === '00'"
    :in-bound-doc-id="props.inBoundDocId"
    :in-bound-doc-number="props.inBoundDocNumber"
    :is-view="true"
  />
  <MaterialForm
    v-else
    :in-bound-doc-id="props.inBoundDocId"
    :in-bound-doc-number="props.inBoundDocNumber"
  />
</template>
