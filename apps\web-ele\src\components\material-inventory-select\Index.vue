<script setup lang="ts">
import { getInventoryPage } from '#/api';
import RemoteSearchSelect from '#/components/remote-search-select/Index.vue';

defineOptions({
  name: 'MaterialInventorySelect',
});
const props = defineProps({
  /**
   * 用于双向数据绑定的当前选中值
   * 支持多种类型：
   * - 单选时可以是字符串、数字或对象
   * - 多选时为数组
   * 默认值为 null，表示没有选中任何值
   */
  modelValue: {
    type: [String, Number, Object, Array, null],
    default: null,
  },
  /**
   * 输入框的占位符文本
   * 当输入框为空时显示该文本，提示用户输入内容
   * 默认值为 '请输入关键字搜索'
   */
  placeholder: {
    type: String,
    default: '请输入关键字搜索',
  },
  /**
   * 每页显示的数据条数
   * 用于分页查询时控制每页展示的记录数量
   * 默认值为 10
   */
  pageSize: {
    type: Number,
    default: 20,
  },
  /**
   * 是否支持多选
   * 如果为 true，则允许用户选择多个选项
   * 如果为 false，则只能选择一个选项
   * 默认值为 false
   */
  multiple: {
    type: Boolean,
    default: false,
  },
  /**
   * 仓库ID
   * 用于查询该仓库下的物料库存
   * 默认值为 ''
   */
  warehouseId: {
    type: String,
    default: '',
  },
});
const emits = defineEmits(['update:modelValue', 'change', 'focus', 'blur']);
/** 获取物料 */
const fetchMaterialInventory =
  () =>
  async ({
    keyword,
    pageNum,
    pageSize,
  }: {
    keyword: string;
    pageNum: number;
    pageSize: number;
  }) => {
    return await getInventoryPage({
      materialName: keyword,
      warehouseIdList: props.warehouseId,
      pageNum,
      pageSize,
    });
  };

const onChange = (materialIds: any) => {
  emits('change', materialIds);
};
</script>

<template>
  <RemoteSearchSelect
    label-key="materialName"
    :model-value="modelValue"
    :placeholder="placeholder"
    :page-size="pageSize"
    :multiple="multiple"
    :fetch-method="fetchMaterialInventory()"
    @change="onChange"
  >
    <template #item="{ item }">
      <el-tag type="primary" v-if="!item.isStandard">非标</el-tag>
      <span class="font-bold">{{ item.materialName }}</span>
      <span>（{{ item.materialCategoryName }}）</span>
      <span class="text-gray-400"> 【{{ item.materialCode }}】 </span>
      <p class="text-gray-400">{{ item.materialSpecs }}</p>
    </template>
  </RemoteSearchSelect>
</template>
