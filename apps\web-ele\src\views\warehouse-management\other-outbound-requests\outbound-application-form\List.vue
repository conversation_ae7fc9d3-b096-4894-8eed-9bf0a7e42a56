<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import { onMounted, ref } from 'vue';

import { useAccess } from '@vben/access';
import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElButton, ElDatePicker, ElMessage, ElTag } from 'element-plus';

import {
  exportInOutReqDoc,
  getInOutReqDocPage,
} from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';

import { docStatusDict } from '../config/list';
import FormEdit from '../modules/FormEdit.vue';
import FormView from '../modules/FormView.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});
const { hasAccessByCodes } = useAccess();
const formRef = ref();
const exportLoading = ref(false);
/** 其它出入库申请单编号 */
const inOutReqDocNumber = ref('');
/** 其它出入库申请单id */
const inOutReqDocId = ref('');
/** 审核流程实例ID */
const processInstanceId = ref('');
const isView = ref(false);
/** 当前单据状态 */
const docStatus = ref('');
/** 提交时间 */
const submitTime = ref({
  submitStartTime: props.params?.submitStartTime,
  submitEndTime: props.params?.submitEndTime,
});

/** 完成时间 */
const finishTime = ref({
  finishStartTime: props.params?.finishStartTime,
  finishEndTime: props.params?.finishEndTime,
});

/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onConfirm: () => {
    formRef.value?.onSubmit();
  },
  confirmText: '提交',
  showCancelButton: true,
  showConfirmButton: hasAccessByCodes(['wm:outboundreq:submit']),
});
/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    submitTime.value = {
      submitStartTime: '',
      submitEndTime: '',
    };
    finishTime.value = {
      finishStartTime: '',
      finishEndTime: '',
    };
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};
/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    handleReset,
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || true,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getInOutReqDocPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            ...submitTime.value,
            ...finishTime.value,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions,
});

/** 查看 */
const onView = (row: any) => {
  isView.value = true;
  docStatus.value = row.docStatus;
  inOutReqDocId.value = row.inOutReqDocId;
  inOutReqDocNumber.value = row.inOutReqDocNumber;
  processInstanceId.value = row.processInstanceId;
  formModalApi
    .setState({
      showConfirmButton: false,
      title: '其他出库申请单详情',
    })
    .open();
};
/** 新增 */
const onAdd = () => {
  isView.value = false;
  inOutReqDocId.value = '';
  inOutReqDocNumber.value = '';
  formModalApi
    .setState({
      showConfirmButton: hasAccessByCodes(['wm:outboundreq:submit']),
      title: '新增出库申请',
    })
    .open();
};

/** 导出 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportInOutReqDoc({
      ...formValues,
      ...submitTime.value,
      ...finishTime.value,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};

/** 提交成功 */
const submitSuccess = () => {
  // 重新获取数据
  gridApi.query();
  formModalApi.close();
};

onMounted(async () => {
  await gridApi.formApi.setValues({
    inOutReqDocNumberList:
      props.params?.inOutReqDocNumberList?.split(',') || [],
    submitUserList: props.params?.submitUserList?.split(',') || [],
    materialUserList: props.params?.materialUserList?.split(',') || [],
    docStatusList: props.params?.docStatusList?.split(',') || [],
    docCodeList: props.params?.docCodeList?.split(',') || [],
  });
});
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <FormModal class="h-full w-8/12">
      <FormView
        v-if="isView"
        :in-out-req-doc-number="inOutReqDocNumber"
        :in-out-req-doc-id="inOutReqDocId"
        :process-instance-id="processInstanceId"
      />
      <FormEdit
        ref="formRef"
        v-else
        @submit-success="submitSuccess"
        :in-out-req-doc-number="inOutReqDocNumber"
        :in-out-req-doc-id="inOutReqDocId"
      />
      <template #center-footer>
        <ElButton
          v-if="!inOutReqDocNumber"
          type="primary"
          @click="formRef?.onSave()"
          v-access:code="'wm:outboundreq:submit'"
        >
          暂存
        </ElButton>
      </template>
    </FormModal>
    <Grid>
      <template #toolbar-actions>
        <ElButton
          type="primary"
          @click="onAdd"
          v-access:code="'wm:outboundreq:submit'"
        >
          新增
        </ElButton>
      </template>
      <template #form-submitTime>
        <ElDatePicker
          v-model="submitTime.submitStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, submitTime.submitEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="submitTime.submitEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                submitTime.submitStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #form-finishTime>
        <ElDatePicker
          v-model="finishTime.finishStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, finishTime.finishEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="finishTime.finishEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                finishTime.finishStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #submitUserName="{ row }">
        <span>{{ row.submitUserName }}</span>
        <span v-if="row.submitUserDeptName">
          ({{ row.submitUserDeptName }})
        </span>
      </template>
      <template #materialUserName="{ row }">
        <span>{{ row.materialUserName }}</span>
        <span v-if="row.materialUserDeptName">
          ({{ row.materialUserDeptName }})
        </span>
      </template>
      <template #docStatusLabel="{ row }">
        <ElTag size="small" :type="docStatusDict[row.docStatus]">
          {{ row.docStatusLabel }}
        </ElTag>
      </template>
      <template #CellOperation="{ row }">
        <ElButton link size="small" @click="onView(row)" type="info">
          查看
        </ElButton>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            :loading="exportLoading"
            circle
            @click="exportHandle"
            v-access:code="'wm:outboundreq:export'"
          >
            <template #icon>
              <IconFont name="xiazai" />
            </template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
