import type { VbenFormSchema } from '@girant/adapter';

import type { WarehouseInfoApi } from '#/api';

import { h, ref } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
import { ElInputNumber } from 'element-plus';

import { getEnableWarehouseList, getOriginalDocConfigList } from '#/api';
import {
  getInventoryPage,
  getMaterialDetail,
} from '#/api/warehouse-management';
import RemoteSearchSelect from '#/components/remote-search-select/Index.vue';
/** 当前选择的仓库 */
const warehouseId = ref('');
/** 获取库存分页列表 用来获取仓库下的物料*/
export const fetchInventory = async ({
  keyword,
  pageNum,
  pageSize,
}: {
  keyword: string;
  pageNum: number;
  pageSize: number;
  warehouseIdList: string;
}) => {
  if (!warehouseId.value) {
    return {
      records: [],
    };
  }
  const resData = await getInventoryPage({
    materialName: keyword,
    pageNum,
    pageSize,
    warehouseIdList: warehouseId.value,
    minAvailableInventory: 1,
  });
  resData.records?.filter((item: any) => item.availableInventory > 0);
  resData.records = resData.records?.map((item: any) => {
    item.materialCode = `${item.materialCode}(安全库存：${item.availableInventory})`;
    return item;
  });
  return resData;
};

/** 新增锁库 */
export function useFormSchema(): VbenFormSchema[] {
  /** 锁库数量 */
  const blockQuantity = ref(1);
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        filterable: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getEnableWarehouseList();
        },
      },
      dependencies: {
        trigger: (values) => {
          warehouseId.value = values.warehouseId;
          values.materialId = null;
        },
        triggerFields: ['warehouseId'],
      },
      fieldName: 'warehouseId',
      formItemClass: 'col-span-1',
      label: '仓库',
      rules: 'selectRequired',
    },
    {
      component: h(RemoteSearchSelect, {
        fetchMethod: fetchInventory,
        valueKey: 'materialId',
        labelKey: 'materialName',
        subLabelKey: 'materialCode',
        placeholder: '请先选择仓库',
        pageSize: 5,
      }),
      modelPropName: 'modelValue',
      dependencies: {
        trigger: (values) => {
          if (!values.materialId) {
            values.baseUnitLabel = '';
            values.materialSpecs = '';
            return;
          }
          // 获取物料信息
          getMaterialDetail(values.materialId).then((res) => {
            values.baseUnitLabel = res.baseUnitLabel;
            values.materialSpecs = res.materialSpecs;
          });
        },
        triggerFields: ['materialId'],
      },
      fieldName: 'materialId',
      label: '物料名称',
      rules: 'required',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue);
      },
      fieldName: 'baseUnitLabel',
      label: '基本单位',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue);
      },
      fieldName: 'materialSpecs',
      label: '规格型号',
    },
    {
      component: (props: any) => {
        return h('div', {}, [
          h(
            ElInputNumber,
            {
              modelValue: blockQuantity.value,
              onUpdate:modelValue: (value: number) => {
                blockQuantity.value = value;
                // 向外部发送更新事件
                props['onUpdate:modelValue']?.(value);
              },
              min: 1,
              precision: 0,
            },
            {},
          ),
        ]);
      },
      fieldName: 'blockQuantity',
      defaultValue: 1,
      formItemClass: 'col-span-full',
      label: '锁库数量',
      rules: 'required',
    },
    // {
    //   component: 'Input',
    //   componentProps: {
    //     type: 'number',
    //     clearable: true,
    //   },
    //   fieldName: 'blockQuantity',
    //   label: '锁库数量',
    //   rules: 'required',
    // },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: WarehouseInfoApi.OriginalDocConfigList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.docName,
            value: item.docCode,
          }));
          return warehouseList;
        },
        api: () => {
          return getOriginalDocConfigList();
        },
        clearable: true,
        filterable: true,
      },
      fieldName: 'docTypeCode',
      label: '关联单据标识',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'docNumber',
      label: '关联单据编号',
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      formItemClass: 'col-span-full items-start',
      componentProps: {
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
    },
    {
      component: h(UploadFiles, {
        mode: 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
        class: 'w-full',
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      label: '附件',
      formItemClass: 'col-span-full items-start',
    },
  ];
}
