<script setup lang="ts">
import { ref } from 'vue';

import { ElMessage, ElScrollbar } from 'element-plus';

import {
  saveOrModInvcAdjustDoc,
  submitInvcAdjustDoc,
} from '#/api/warehouse-management';
import ApprovalTimeline from '#/components/approval-timeline/Index.vue';
import FormCard from '#/components/form-card/Index.vue';

import AdjustFormEdit from './adjust-form/form-edit/index.vue';
import AdjustFormView from './adjust-form/form-view/index.vue';
import MaterialFormEdit from './material-form/form-edit/index.vue';
import MaterialFormView from './material-form/form-view/index.vue';
import { confirm } from './method';

const props = defineProps({
  isView: {
    type: Boolean,
    default: false,
  },
  /** 库存调整单据编号 */
  invcAdjustDocNumber: {
    type: String,
    default: '',
  },
  /** 库存调整单据id */
  invcAdjustDocId: {
    type: String,
    default: '',
  },
  /** 当前单据状态*/
  docStatus: {
    type: String,
    default: '',
  },
  /** 审核流程实例ID */
  processInstanceId: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['submitSuccess']);
const loading = ref(false);
/** 调整信息ref*/
const adjustFormRef = ref();
/** 物料信息ref*/
const materialFormRef = ref();

/** 校验表单 */
const validateForm = async () => {
  // 校验调整信息表单 // 校验物料信息表单
  const [verification, verification2] = await Promise.all([
    adjustFormRef.value?.validateForm(),
    materialFormRef.value?.validateForm(),
  ]);
  // 获取表单数据
  const data = await getFormData();
  if (data.invcAdjustItemList.length === 0) {
    ElMessage.error('请填写物料信息');
    return false;
  }
  if (!verification || !verification2) {
    ElMessage.error('请填写完整表单');
    return false;
  }
  return true;
};

/** 获取表单数据 */
const getFormData = async () => {
  const [data, data2] = await Promise.all([
    adjustFormRef.value?.getFormData(),
    materialFormRef.value?.getFormData(),
  ]);
  return {
    ...data,
    invcAdjustItemList: data2,
  };
};

/** 提交表单 新增*/
const onSubmit = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    const isUpdate = !!props.invcAdjustDocId;
    if (await confirm('确定提交单据吗？', '提示')) {
      loading.value = true;
      await submitInvcAdjustDoc({
        invcAdjustDocId: isUpdate ? props.invcAdjustDocId : '',
        ...data,
      });
    }
    ElMessage.success(isUpdate ? '提交成功' : '新增成功');
    emit('submitSuccess');
  } catch (error) {
    console.error(error);
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};

/** 提交暂存 */
const onSave = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    if (await confirm('确定提交暂存吗？', '提示')) {
      loading.value = true;
      await saveOrModInvcAdjustDoc(data);
    }
    ElMessage.success('提交暂存成功');
    emit('submitSuccess');
  } catch (error) {
    console.error(error);
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};
defineExpose({
  validateForm,
  onSave,
  onSubmit,
  loading,
});
</script>
<template>
  <div v-loading="loading">
    <AdjustFormView
      ref="adjustFormRef"
      v-if="isView"
      :invc-adjust-doc-number="invcAdjustDocNumber"
      :invc-adjust-doc-id="invcAdjustDocId"
    />
    <AdjustFormEdit
      v-else
      ref="adjustFormRef"
      :invc-adjust-doc-id="invcAdjustDocId"
      :invc-adjust-doc-number="invcAdjustDocNumber"
    />
    <MaterialFormView
      ref="materialFormRef"
      v-if="isView"
      :invc-adjust-doc-id="invcAdjustDocId"
      :invc-adjust-doc-number="invcAdjustDocNumber"
      :doc-status="docStatus"
    />
    <MaterialFormEdit
      v-else
      ref="materialFormRef"
      :invc-adjust-doc-number="invcAdjustDocNumber"
      :invc-adjust-doc-id="invcAdjustDocId"
    />
    <FormCard :is-footer="false" v-if="isView">
      <template #title>
        <span>审核流程</span>
      </template>
      <ElScrollbar>
        <ApprovalTimeline :process-instance-id="processInstanceId" />
      </ElScrollbar>
    </FormCard>
  </div>
</template>
