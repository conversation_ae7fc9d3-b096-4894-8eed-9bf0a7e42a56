<script setup lang="ts">
import type { WarehouseInfoApi } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { ElCard, ElMessage, ElScrollbar } from 'element-plus';

import {
  getWarehouseDetail,
  lockWarehouse,
  unlockWarehouse,
} from '#/api/warehouse-management';

import { confirm } from '../method';
import WarehouseFormEdit from './form-edit/index.vue';
import WarehouseFormView from './form-view/index.vue';

const props = defineProps({
  isView: {
    type: Boolean,
    default: false,
  },
  /** 仓库id */
  warehouseId: {
    type: String,
    default: '',
  },
  /** 仓库编码 */
  warehouseCode: {
    type: String,
    default: '',
  },
});
const emits = defineEmits(['formSubmitSuccess']);
/** 仓库表单 */
const wareRef = ref<InstanceType<typeof WarehouseFormEdit>>();
/** 仓库信息 */
const wareInfo = ref<WarehouseInfoApi.WarehouseDetail>();
/** 是否锁定 */
const isLock = ref(false);
/** 编辑 */
const isEdit = ref(false);
/** 查看 */
const thisView = ref(props.isView);
/** 当前id */
const currentId = ref(props.warehouseId);
/** 当前code */
const currentCode = ref(props.warehouseCode);
const loading = ref(false);
/** 获取仓库信息 */
const getWarehouseInfo = async () => {
  try {
    loading.value = true;
    const resWarehouse = await getWarehouseDetail(
      currentId.value,
      currentCode.value,
    );
    wareInfo.value = resWarehouse;
    isLock.value = resWarehouse.isLock;
  } catch {
    ElMessage.error('获取仓库信息失败');
  } finally {
    loading.value = false;
  }
};

/** 提交表单 */
const submit = async () => {
  // 判断是否有id 有id则是编辑 否则是新增
  if (currentId.value || currentCode.value) {
    wareRef.value?.onSubmitEdit();
  } else {
    wareRef.value?.onSubmit();
  }
};
/** 取消表单 */
const cancel = () => {
  if (currentId.value) {
    // 切换回查看状态
    thisView.value = true;
  } else {
    // 清空表单
    wareRef.value?.clearForm();
  }
};

/** 编辑按钮 */
const edit = () => {
  isEdit.value = true;
  thisView.value = false;
};
/** 锁定 */
const lock = async () => {
  try {
    await confirm('确认锁定吗？', '提示');
    loading.value = true;
    await lockWarehouse(currentId.value);
    ElMessage.success('锁定成功');
    getWarehouseInfo();
    emits('formSubmitSuccess');
  } catch (error) {
    console.error(error);
    // ElMessage.error('锁定失败');
  } finally {
    loading.value = false;
  }
};
/** 解锁 */
const unlock = async () => {
  try {
    await confirm('确认解锁吗？', '提示');
    loading.value = true;
    await unlockWarehouse(currentId.value);
    ElMessage.success('解锁成功');
    getWarehouseInfo();
    emits('formSubmitSuccess');
  } catch {
    ElMessage.error('解锁失败');
  } finally {
    loading.value = false;
  }
};
/** 提交成功 */
const submitSuccess = (warehouseId: string, warehouseCode: string) => {
  if (warehouseId) {
    currentId.value = warehouseId;
  }
  if (warehouseCode) {
    currentCode.value = warehouseCode;
  }
  // 切换回查看状态
  thisView.value = true;
  emits('formSubmitSuccess');
};
onMounted(() => {
  if (currentId.value || currentCode.value) {
    getWarehouseInfo();
  }
});
defineExpose({
  submit,
  cancel,
  edit,
  lock,
  unlock,
  submitSuccess,
  wareInfo,
  isLock,
  wareRef,
});
</script>

<template>
  <ElCard
    v-loading="loading"
    :is-footer="false"
    class="flex h-full flex-col justify-between !pb-[30px]"
    shadow="never"
    body-class="h-full !p-[10px] w-full"
    style="border: 1px solid"
  >
    <ElScrollbar noresize>
      <WarehouseFormView
        :warehouse-id="currentId"
        :warehouse-code="currentCode"
        v-if="thisView"
        :is-lock="isLock"
      />
      <WarehouseFormEdit
        ref="wareRef"
        :warehouse-id="currentId"
        :warehouse-code="currentCode"
        v-else
        @user-form-submit-success="submitSuccess"
      />
    </ElScrollbar>

    <div class="flex min-h-[40px] justify-end">
      <ElButton type="primary" @click="edit" v-if="thisView"> 编辑 </ElButton>
      <ElButton type="primary" @click="unlock" v-if="isLock && thisView">
        解锁
      </ElButton>
      <ElButton type="primary" @click="lock" v-if="!isLock && thisView">
        锁定
      </ElButton>
      <ElButton type="primary" @click="submit" v-if="!thisView">
        提交
      </ElButton>
      <ElButton type="info" @click="cancel" v-if="!thisView"> 取消 </ElButton>
    </div>
  </ElCard>
</template>
