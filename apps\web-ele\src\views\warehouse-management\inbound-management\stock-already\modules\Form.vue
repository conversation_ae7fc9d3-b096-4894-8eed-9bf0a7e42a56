<script lang="ts" setup>
import InboundInfo from './inbound-info/index.vue';
import MaterialsInfo from './materials-info/index.vue';

defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
});

const emits = defineEmits(['handleCancel']);

const handleCancel = () => {
  emits('handleCancel');
};
</script>

<template>
  <div class="relative mb-12 h-full">
    <InboundInfo
      :in-bound-doc-id="inBoundDocId"
      :in-bound-doc-number="inBoundDocNumber"
    />
    <MaterialsInfo
      :in-bound-doc-id="inBoundDocId"
      :in-bound-doc-number="inBoundDocNumber"
    />

    <div
      class="fixed bottom-0 right-0 z-10 h-[50px] w-full rounded-b-lg border-t bg-white p-2 text-right"
    >
      <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
    </div>
  </div>
</template>
