import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { warehousePath } from '../../path';

export namespace OutboundPendingApi {
  export interface OutboundPendingPageParams {
    /** 出库单据号列表，多个用英文逗号分隔 */
    outBoundDocNumberList: string[];
    /** 申请人id列表，精确查询 */
    applyUserList: string[];
    /** 源单据类型标识列表，精确查询，根据查询来源单据配置列表接口的出参docCode字段。 */
    origDocTypeCodeList: string[];
    /** 源单据编号列表，精确查询 */
    origDocNumberList: string[];
    /** 是否补录，不填则展示全部，true-补录，false-非补录 */
    isRectify: boolean;
    /** 申请起始时间，时间格式：yyyy-MM-dd HH:mm */
    applyTime: string[];
    /** 使用人id列表，精确查询 */
    materialUserList: string[];
    /** 使用人部门id列表，精确查询 */
    materialUserDeptList: string[];
    /** 备料状态值列表，字典WmPreparationStateEnums */
    preparationStateList: string[];
    /** 申请起始时间，时间格式：yyyy-MM-dd HH:mm */
    applyStartTime?: string;
    /** 申请终止时间，时间格式：yyyy-MM-dd HH:mm */
    applyEndTime?: string;
    /** 当前页，默认第1页 */
    pageNum?: number;
    /** 分页数，默认每一页默认10条 */
    pageSize?: number;
  }

  export interface OutboundDocRecord {
    /* 出库单据ID */
    outBoundDocId: string;

    /* 出库单据编号 */
    outBoundDocNumber: string;

    /* 源单据类型标识 */
    origDocTypeCode: string;

    /* 源单据类型名称 */
    origDocTypeName: string;

    /* 源单据ID */
    origDocId: string;

    /* 源单据编号 */
    origDocNumber: string;

    /* 申请人ID */
    applyUser: string;

    /* 申请人姓名 */
    applyUserName: string;

    /* 申请人部门ID */
    applyUserDeptId: string;

    /* 申请人部门名称 */
    applyUserDeptName: string;

    /* 使用人ID */
    materialUser: string;

    /* 使用人姓名 */
    materialUserName: string;

    /* 使用人部门ID */
    materialUserDeptId: string;

    /* 使用人部门名称 */
    materialUserDeptName: string;

    /* 是否补录 */
    isRectify: boolean;

    /* 提交时间，时间格式：yyyy-MM-dd HH:mm */
    applyTime: Record<string, unknown>;

    /* 备料状态值，枚举WmPreparationStateEnums */
    preparationState: string;

    /* 备料状态标签，枚举WmPreparationStateEnums */
    preparationStateLabel: string;
  }

  export interface OutboundDocDetailParams {
    /** 出库单据id */
    outBoundDocId: string;
    /** 出库单据编号 */
    outBoundDocNumber?: string;
    /** 是否查询子项，默认不查询，true-查询，false-不查询 */
    isQueryItem?: boolean;
  }

  export interface OutboundDocDetailByOutCancelDocParams {
    /** 出库取消申请单据id */
    outCancelDocId: string;
    /** 出库取消申请单据编号 */
    outCancelDocNumber?: string;
    /** 是否查询子项，默认不查询，true-查询，false-不查询 */
    isQueryItem?: boolean;
  }

  /* 共有的子项数据 */
  export interface CommonItem {
    /* 仓库id */
    warehouseId: string;

    /* 仓库编号 */
    warehouseCode: string;

    /* 仓库名称 */
    warehouseName: string;

    /* 库位id */
    locationId: string;

    /* 库位编号 */
    locationCode: string;

    /* 库位名称 */
    locationName: string;

    /* 批次号 */
    batchNumber: string;

    /* 均价（单价），默认不可见 */
    unitPrice: number;
  }

  /** 申请明细子项 */
  export interface ApplyItem extends CommonItem {
    /* 申请数量 */
    applyQuantity: number;
  }

  /* 实际明细子项 */
  export interface ActualItem extends CommonItem {
    /* 出入库实际数量 */
    actualQuantity: number;
  }

  /** 出库单据子项 */
  export interface OutboundItem {
    /* 申请明细列表 */
    applyItemList: ApplyItem[];

    /* 实际明细列表，为空时代表此物料没有做出入库 */
    actualItemList: ActualItem[];
    /* 物料ID */
    materialId: string;

    /* 物料编号 */
    materialCode: string;

    /* 物料名称 */
    materialName: string;

    /* 物料图片ID */
    pictureFileId: string;

    /* 基本单位值，字典baseMaterialUnit */
    baseUnit: string;

    /* 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;

    /* 物料属性值，字典baseMaterialAttribute */
    materialAttribute: string;

    /* 物料属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;

    /* 物料大类值，字典baseMaterialType */
    materialType: string;

    /* 物料大类标签，字典baseMaterialType */
    materialTypeLabel: string;

    /* 物料细类 */
    materialCategory: string;

    /* 物料细类名称 */
    materialCategoryName: string;

    /* 规格型号 */
    materialSpecs: string;

    /* 是否标准物料，true-标准物料, false-非标准物料 */
    isStandard: boolean;

    /* 申请总数 */
    applyQuantitySum: number;

    /* 实际总数 */
    actualQuantitySum: number;
  }

  /** 出库单据详情 */
  export interface OutboundDocDetail {
    /* 出库单据子项列表 */
    outBoundItemList: OutboundItem[];

    /* 出库单据ID */
    outBoundDocId: string;

    /* 出库单据编号 */
    outBoundDocNumber: string;

    /* 源单据类型标识 */
    origDocTypeCode: string;

    /* 源单据类型名称 */
    origDocTypeName: string;

    /* 源单据ID */
    origDocId: string;

    /* 源单据编号 */
    origDocNumber: string;

    /* 申请人ID */
    applyUser: string;

    /* 申请人姓名 */
    applyUserName: string;

    /* 申请人部门ID */
    applyUserDeptId: string;

    /* 申请人部门名称 */
    applyUserDeptName: string;

    /* 领料人ID */
    collectorUser: string;

    /* 领料人姓名 */
    collectorUserName: string;

    /* 领料人部门ID */
    collectorUserDeptId: string;

    /* 领料人部门名称 */
    collectorUserDeptName: string;

    /* 执行仓管员ID */
    executorUser: string;

    /* 执行仓管员姓名 */
    executorUserName: string;

    /* 执行仓管员部门ID */
    executorUserDeptId: string;

    /* 执行仓管员部门名称 */
    executorUserDeptName: string;

    /* 使用人ID */
    materialUser: string;

    /* 使用人姓名 */
    materialUserName: string;

    /* 使用人部门ID */
    materialUserDeptId: string;

    /* 使用人部门名称 */
    materialUserDeptName: string;

    /* 领料人确认方式值，枚举WmInOutConfirmMethodEnums */
    collectorConfirmMethod: string;

    /* 领料人确认方式标签，枚举WmInOutConfirmMethodEnums */
    collectorConfirmMethodLabel: string;

    /* 仓管员确认方式值，枚举WmInOutManagerConfirmMethodEnums */
    executorConfirmMethod: string;

    /* 仓管员确认方式标签，枚举WmInOutManagerConfirmMethodEnums */
    executorConfirmMethodLabel: string;

    /* 是否补录 */
    isRectify: boolean;

    /* 是否代领 */
    isProxyExec: boolean;

    /* 提交时间，时间格式：yyyy-MM-dd HH:mm */
    applyTime: Record<string, unknown>;

    /* 执行出库时间，时间格式：yyyy-MM-dd HH:mm */
    executorTime: Record<string, unknown>;

    /* 是否自动完成出库 */
    isAutoIo: boolean;

    /* 备料状态值，枚举WmPreparationStateEnums */
    preparationState: string;

    /* 备料状态标签，枚举WmPreparationStateEnums */
    preparationStateLabel: string;

    /* 单据状态值，字典wmOutDocStatus */
    docStatus: string;

    /* 单据状态标签，字典wmOutDocStatus */
    docStatusLabel: string;

    /* 备注 */
    remark: string;

    /* 附件流水号 */
    serialNumber: string;

    /* 取消原因选项列表 */
    remarkOptionList: {
      /* 选项ID */
      optionId: string;
      /* 选项名称 */
      optionName: string;
    }[];
  }

  /** 出库单领料人确认二维码 */
  export interface ExecQrCodeByOutBoundDocId {
    /* 二维码图片，Base64编码 */
    qrCode: string;

    /* 验证ID */
    uuidValue: string;

    /* 有效时长，单位秒 */
    validDuration: number;
  }

  /** 实际明细列表 */
  export interface ExecOutboundActualItem {
    /* 出库数量 */
    actualQuantity: number;

    /* 批次号 */
    batchNumber: string;

    /* 库位ID */
    locationId: string;

    /* 物料ID */
    materialId: string;

    /* 仓库ID */
    warehouseId: string;
  }

  /** 执行出库单据参数 */
  export interface ExecOutboundParams {
    /* 出库单据ID */
    outBoundDocId: number;

    /* 领料人确认方式值，枚举WmInOutConfirmMethodEnums */
    collectorConfirmMethod: string;

    /* 确认值，当确认方式为选择输入领料码时，确认值为领料人确认码，当选择扫二维码时，确认值为领料验证ID。 */
    collectValue: string;

    /* 备注 */
    remark?: string;

    /* 附件流水号 */
    serialNumber?: string;

    /* 实际明细列表 */
    actualItemList?: ExecOutboundActualItem[];
  }
}

/**
 * 查询待出库单据分页列表
 */
export async function getPendingOutBoundDocPage(
  params: OutboundPendingApi.OutboundPendingPageParams,
) {
  return requestClient.post<OutboundPendingApi.OutboundDocRecord>(
    `${warehousePath}/wm/outBound/pending/getOutBoundDocPage`,
    { ...params },
  );
}

/**
 * 获取出库单据详细信息
 */
export function getOutboundDocDetail(
  params: OutboundPendingApi.OutboundDocDetailParams,
) {
  const { outBoundDocId, outBoundDocNumber, isQueryItem = false } = params;
  return requestClient.get<OutboundPendingApi.OutboundDocDetail>(
    `${warehousePath}/wm/outBound/getOutboundDocDetail?outBoundDocId=${outBoundDocId}&outBoundDocNumber=${outBoundDocNumber}&isQueryItem=${isQueryItem}`,
  );
}

/**
 * 根据出库取消申请单据获取出库单据详细信息
 */
export function getOutBoundDocDetailByOutCancelDoc(
  params: OutboundPendingApi.OutboundDocDetailByOutCancelDocParams,
) {
  const { outCancelDocId, outCancelDocNumber, isQueryItem = false } = params;
  return requestClient.get<OutboundPendingApi.OutboundDocDetail>(
    `${warehousePath}/wm/outBound/getOutBoundDocDetailByOutCancelDoc?outCancelDocId=${outCancelDocId}&outCancelDocNumber=${outCancelDocNumber}&isQueryItem=${isQueryItem}`,
  );
}

/**
 * 根据源单据获取出库单据详细信息
 */
export function getOutBoundDocDetailByOrigDoc(
  origDocId: string,
  origDocNumber?: string,
  isQueryItem?: boolean,
) {
  return requestClient.get<OutboundPendingApi.OutboundDocDetail>(
    `${warehousePath}/wm/outBound/getOutBoundDocDetailByOrigDoc`,
    {
      params: {
        origDocId,
        origDocNumber,
        isQueryItem,
      },
    },
  );
}

/** 导出待出库单据列表*/
export async function exportPendingOutBoundDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/pending/exportPendingOutBoundDoc`,
    {
      ...params,
    },
    {
      responseReturn: 'raw',
      responseType: 'blob',
    },
  );
}

/**
 * 获取出库单领料码
 */
export function getExecCode(outBoundDocId?: string, outReqDocId?: string) {
  return requestClient.get<number>(`${warehousePath}/wm/outBound/getExecCode`, {
    params: {
      outBoundDocId,
      outReqDocId,
    },
  });
}

/**
 * 获取出库单领料码
 */
export function getManagerCode(outBoundDocId: string) {
  return requestClient.get<number>(
    `${warehousePath}/wm/outBound/getManagerCode/${outBoundDocId}`,
  );
}

/**
 * 生成领料人确认二维码
 */
export function getExecQrCodeByOutBoundId(outBoundDocId: string) {
  return requestClient.get<OutboundPendingApi.ExecQrCodeByOutBoundDocId>(
    `${warehousePath}/wm/outBound/verify/qrcode/getExecQrCodeByOutBoundId/${outBoundDocId}`,
  );
}

/**
 * 刷新领料人确认二维码
 */
export function refreshExecQrCodeByOutBoundId(outBoundDocId: string) {
  return requestClient.get<OutboundPendingApi.ExecQrCodeByOutBoundDocId>(
    `${warehousePath}/wm/outBound/verify/qrcode/refreshExecQrCodeByOutBoundId/${outBoundDocId}`,
  );
}

/**
 * 领料确认二维码验证
 */
export function validateQrcode(uuidValue: string) {
  return requestClient.get(
    `${warehousePath}/wm/outBound/verify/validateQrcode?uuidValue=${uuidValue}`,
  );
}

/**
 * 执行出库单据
 */
export async function execOutbound(
  params: OutboundPendingApi.ExecOutboundParams,
) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/execOutbound`,
    {
      ...params,
    },
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/**
 * 关闭出库单据
 */
export function closeOutbound(outBoundDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/outBound/closeOutbound/${outBoundDocId}`,
  );
}
