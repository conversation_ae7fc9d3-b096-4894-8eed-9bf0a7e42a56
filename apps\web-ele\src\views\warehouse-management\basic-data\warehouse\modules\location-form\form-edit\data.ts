import type { VbenFormSchema } from '@girant/adapter';

import type { WarehouseInfoApi } from '#/api/warehouse-management';

import { getWarehouseList } from '#/api/warehouse-management';
/** 基础资料 编辑 */
export function useFormSchema(isEdit: boolean): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      fieldName: 'warehouseId',
      label: '所属仓库',
      componentProps: {
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getWarehouseList({});
        },
        clearable: true,
        filterable: true,
      },
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'locationCode',
      label: '库位编号',
      componentProps: {
        clearable: true,
        disabled: isEdit,
        placeholder: '为空时系统自动生成',
      },
    },
    {
      component: 'Input',
      fieldName: 'locationName',
      label: '库位名称',
      componentProps: {
        clearable: true,
      },
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      fieldName: 'isTemp',
      label: '是否临时库位',
      componentProps: {
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
      defaultValue: false,
    },
    {
      component: 'RadioGroup',
      fieldName: 'isPrepMaterial',
      label: '是否可备料',
      componentProps: {
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
      defaultValue: true,
    },
    {
      component: 'RadioGroup',
      fieldName: 'isEnable',
      label: '是否停用',
      componentProps: {
        options: [
          {
            label: '是',
            value: false,
          },
          {
            label: '否',
            value: true,
          },
        ],
      },
      defaultValue: true,
    },
    {
      component: 'Input',
      fieldName: 'locationDesc',
      label: '所在位置',
      formItemClass: 'col-span-full',
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注',
      formItemClass: 'col-span-full items-start',
      componentProps: {
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
    },
  ];
}
