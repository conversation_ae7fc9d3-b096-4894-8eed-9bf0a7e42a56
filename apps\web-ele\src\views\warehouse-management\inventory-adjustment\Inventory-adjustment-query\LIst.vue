<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { InventoryAdjustment } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElDatePicker,
  ElMessage,
  ElMessageBox,
  ElTag,
  ElTooltip,
} from 'element-plus';

import {
  closeInvcAdjustDoc,
  execInvcAdjustDoc,
  exportInvcAdjustDoc,
  getInvcAdjustDocPage,
} from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';

import { docStatusDict } from '../config/list';
import ViewForm from '../modules/FormView.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});
const exportLoading = ref(false);
/** 提交时间 */
const submitTime = ref({
  submitStartTime: props.params?.submitStartTime,
  submitEndTime: props.params?.submitEndTime,
});

/** 执行时间 */
const execTime = ref({
  execStartTime: props.params?.execStartTime,
  execEndTime: props.params?.execEndTime,
});

const [ViewModal, viewModalApi] = useVbenModal({
  connectedComponent: ViewForm,
});

/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    submitTime.value = {
      submitStartTime: '',
      submitEndTime: '',
    };
    execTime.value = {
      execStartTime: '',
      execEndTime: '',
    };
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};

/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    handleReset,
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || true,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getInvcAdjustDocPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            ...submitTime.value,
            ...execTime.value,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<InventoryAdjustment.InventoryAdjustmentPage>,
});
/** 刷新列表 */
const refreshList = () => {
  gridApi.query();
};
/** 查看 */
const onView = (row: any) => {
  console
  viewModalApi
    .setState({
      showConfirmButton: false,
      title: '调整单据详情',
    })
    .setData({
      invcAdjustDocId: row.invcAdjustDocId,
      invcAdjustDocNumber: row.invcAdjustDocNumber,
      docStatus: row.docStatus,
      processInstanceId: row.processInstanceId,
      refreshList: () => {
        refreshList();
      },
    })
    .open();
};

/** 确认执行 */
const confirmExecute = async (invcAdjustDocId: string) => {
  try {
    await ElMessageBox.confirm('确定执行吗？', '提示', {
      type: 'warning',
    });
    await execInvcAdjustDoc(invcAdjustDocId);
    ElMessage.success('执行成功');
    gridApi.query();
  } catch (error) {
    console.error(error);
  }
};
/** 取消单据 */
const cancelDoc = async (invcAdjustDocId: string) => {
  try {
    await ElMessageBox.confirm('确定取消吗？', '提示', {
      type: 'warning',
    });
    await closeInvcAdjustDoc(invcAdjustDocId);
    ElMessage.success('取消成功');
    gridApi.query();
  } catch (error) {
    console.error(error);
  }
};
/** 导出 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportInvcAdjustDoc({
      ...formValues,
      ...submitTime.value,
      ...execTime.value,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};

onMounted(async () => {
  await gridApi.formApi.setValues({
    invcAdjustDocNumberList:
      props.params?.invcAdjustDocNumberList?.split(',') || [],
    submitUserList: props.params?.submitUserList?.split(',') || [],
    executorUserList: props.params?.executorUserList?.split(',') || [],
    docStatusList: props.params?.docStatusList?.split(',') || [],
  });
});
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <ViewModal class="h-full w-10/12" />
    <Grid>
      <!-- <template #toolbar-actions>
        <ElButton
          type="primary"
          @click="onAdd"
          v-access:code="'wm:inventory:adjust:submit'"
        >
          新增
        </ElButton>
      </template> -->
      <template #form-submitTime>
        <ElDatePicker
          v-model="submitTime.submitStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, submitTime.submitEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="submitTime.submitEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                submitTime.submitStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #form-execTime>
        <ElDatePicker
          v-model="execTime.execStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, execTime.execEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="execTime.execEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, execTime.execStartTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #docStatusLabel="{ row }">
        <ElTag size="small" :type="docStatusDict[row.docStatus]">
          {{ row.docStatusLabel }}
        </ElTag>
      </template>
      <template #CellOperation="{ row }">
        <ElButton link size="small" @click="onView(row)" type="info">
          查看
        </ElButton>
        <ElButton
          link
          size="small"
          type="primary"
          v-if="row.docStatus === '30'"
          @click="confirmExecute(row.invcAdjustDocId)"
          v-access:code="'wm:inventory:adjust:exec'"
        >
          确认执行
        </ElButton>
        <ElButton
          link
          size="small"
          type="danger"
          v-if="row.docStatus === '30'"
          @click="cancelDoc(row.invcAdjustDocId)"
          v-access:code="'wm:inventory:adjust:close'"
        >
          取消单据
        </ElButton>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            :loading="exportLoading"
            circle
            @click="exportHandle"
            v-access:code="'wm:inventory:adjust:export'"
          >
            <template #icon>
              <IconFont name="xiazai" />
            </template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
