<script setup lang="ts">
import type { InventoryLock } from '#/api/warehouse-management';

import { h, nextTick, onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '@girant/adapter';
import { ElButton, ElMessage, ElTag } from 'element-plus';

import { getInvcBlock } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';
import MaterialForm from '#/views/warehouse-management/basic-data/material/modules/material-form/index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  /** 库存锁库id */
  blockId: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
/** 物料编号 */
const materialCode = ref('');
/** 物料id */
const materialId = ref('');
const materialFormRef = ref();
/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  showCancelButton: true,
  showConfirmButton: false,
});
/** 锁库原因选项列表*/
const remarkOptionList =
  ref<InventoryLock.InvcBlockDetail['remarkOptionList']>();
/** 查看物料信息 */
const onViewMaterial = () => {
  formModalApi
    .setState({
      title: '查看物料信息',
    })
    .open();
  nextTick(() => {
    const schema = materialFormRef.value.formApi.getState().schema;
    schema.unshift({
      component: (props: any) => {
        return h('div', null, props.modelValue);
      },
      fieldName: 'warehouseName',
      label: '仓库名称',
      formItemClass: 'col-span-1',
    });
    materialFormRef.value.formApi.updateSchema(schema);
  });
};
/** 锁库信息表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 xl:grid-cols-3',
});
/** 根据库存锁库id获取库存锁库详细信息 */
const getMaterialInfo = async (blockId: string) => {
  try {
    loading.value = true;
    const res = await getInvcBlock(blockId);
    materialCode.value = res.materialCode;
    materialId.value = res.materialId;
    remarkOptionList.value = res.remarkOptionList;
    formApi.setValues(res);
  } catch {
    ElMessage.error('获取库存锁库详细信息失败');
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  if (props.blockId) {
    getMaterialInfo(props.blockId);
  }
});
defineExpose({
  Form,
  formApi,
});
</script>
<template>
  <!-- 模态框 -->
  <FormModal class="h-full w-8/12">
    <MaterialForm
      class="min-w-[600px]"
      :material-id="materialId"
      :material-code="materialCode"
      ref="materialFormRef"
    />
  </FormModal>
  <FormCard title="锁库信息" :is-footer="false" class="min-w-[600px]">
    <Form v-loading="loading">
      <template #materialName="row">
        <ElButton link type="primary" @click="onViewMaterial">
          {{ row.value }}({{ materialCode }})
        </ElButton>
      </template>
      <template #remark="row">
        <div class="mb-2 flex w-full flex-wrap gap-2">
          <ElTag
            type="primary"
            v-for="item in remarkOptionList"
            :key="item.optionId"
          >
            {{ item.optionName }}
          </ElTag>
        </div>
        <span class="text-sm">{{ row.value }}</span>
      </template>
    </Form>
  </FormCard>
</template>
