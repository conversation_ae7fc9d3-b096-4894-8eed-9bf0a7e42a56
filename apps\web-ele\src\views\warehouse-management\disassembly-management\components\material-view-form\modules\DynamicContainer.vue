<script setup lang="ts">
import { computed, defineAsyncComponent, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

/** 共享数据 */
const data = ref();

const [Modal, modalApi] = useVbenModal({
  onClosed() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
    }
  },
});

const currentComponent = computed(() => {
  switch (data.value.type) {
    case 'material': {
      return defineAsyncComponent(
        () =>
          import(
            '#/views/warehouse-management/basic-data/material/modules/material-form/index.vue'
          ),
      );
    }
    default: {
      return null;
    }
  }
});
</script>

<template>
  <Modal>
    <component :is="currentComponent" v-bind="data.attr" />
  </Modal>
</template>
