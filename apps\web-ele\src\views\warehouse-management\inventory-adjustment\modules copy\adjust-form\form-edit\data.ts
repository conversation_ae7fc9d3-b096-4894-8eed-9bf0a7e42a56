import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
/** 调整单信息 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'invcAdjustDocNumber',
      label: '单据编号',
      componentProps: {
        disabled: true,
        placeholder: '系统默认自动生成',
      },
    },
    {
      component: 'Input',
      fieldName: 'remarkOptionList',
      label: '调整原因',
      formItemClass: 'col-span-full',
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '调整原因说明',
      formItemClass: 'col-span-full items-start',
      componentProps: {
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
    },
    {
      component: h(UploadFiles, {
        mode: 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      label: '附件',
      formItemClass: 'col-span-full items-start',
    },
  ];
}
