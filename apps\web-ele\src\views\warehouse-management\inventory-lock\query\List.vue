<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { InventoryLock } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElButton, ElDatePicker, ElMessage, ElTooltip } from 'element-plus';

import {
  exportInvcBlock,
  getInvcBlockPage,
  unlock,
} from '#/api/warehouse-management';

import Form from '../modules/Form.vue';
import { confirm, openModal } from '../modules/method';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});
const isView = ref(false);
/** 物料id */
const materialId = ref<string>('');
const formRef = ref<InstanceType<typeof Form>>();
/** 库存锁库ID*/
const blockId = ref('');
/** 锁定时间 */
const blockTime = ref({
  blockStartTime: props.params?.blockStartTime,
  blockEndTime: props.params?.blockEndTime,
});
const createDisabledDate = (isEnd: boolean) => {
  return (time: Date) => {
    if (!blockTime.value.blockEndTime && !isEnd) {
      return false;
    }
    // 是结束时间
    return isEnd
      ? time.getTime() < new Date(blockTime.value.blockStartTime).getTime()
      : time.getTime() > new Date(blockTime.value.blockEndTime).getTime();
  };
};
const exportLoading = ref(false);
/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  onConfirm: () => {
    formRef.value?.onSubmit();
  },
  showCancelButton: true,
  showConfirmButton: true,
});
/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    blockTime.value = {
      blockStartTime: '',
      blockEndTime: '',
    };
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};
/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    handleReset,
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || false,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getInvcBlockPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            ...blockTime.value,
            isValid: true,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<InventoryLock.InvcBlockPage>,
});

/** 查看 */
const onView = (row: any) => {
  materialId.value = row.materialId;
  blockId.value = row.blockId;
  isView.value = true;
  openModal(formModalApi, false, '查看');
};
/** 新增 */
const onAdd = () => {
  isView.value = false;
  blockId.value = '';
  openModal(formModalApi, true, '新增');
};
/** 提交解锁 */
const submitUnlock = async (blockId: string) => {
  try {
    await confirm('确认解锁吗？', '提示');
    await unlock(blockId);
    ElMessage.success('解锁成功');
    gridApi.query();
    formModalApi.close();
  } catch {
    ElMessage.error('解锁失败');
  }
};
/** 导出 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportInvcBlock({
      ...formValues,
      ...blockTime.value,
      isValid: true,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};

/** 提交成功 */
const submitSuccess = () => {
  // 重新获取数据
  gridApi.query();
  formModalApi.close();
};
onMounted(async () => {
  await gridApi.formApi.setValues({
    materialCodeList: props.params?.materialCodeList?.split(',') || [],
    warehouseIdList: props.params?.warehouseIdList?.split(',') || [],
    blockUserList: props.params?.blockUserList?.split(',') || [],
  });
});
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <FormModal class="h-full w-8/12">
      <Form
        ref="formRef"
        :is-view="isView"
        :block-id="blockId"
        :material-id="materialId"
        :is-valid="true"
        @submit-success="submitSuccess"
      />
      <template #prepend-footer>
        <ElButton v-if="isView" type="primary" @click="submitUnlock(blockId)">
          解锁
        </ElButton>
      </template>
    </FormModal>
    <Grid>
      <template #toolbar-actions>
        <ElButton type="primary" @click="onAdd"> 新增 </ElButton>
      </template>
      <template #form-blockTime>
        <ElDatePicker
          v-model="blockTime.blockStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="createDisabledDate(false)"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="blockTime.blockEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="createDisabledDate(true)"
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton :loading="exportLoading" circle @click="exportHandle">
            <template #icon>
              <IconFont name="xiazai" />
            </template>
          </ElButton>
        </ElTooltip>
      </template>
      <template #CellOperation="{ row }">
        <ElButton link size="small" @click="onView(row)" type="info">
          查看
        </ElButton>
        <ElButton
          link
          size="small"
          type="primary"
          @click="submitUnlock(row.blockId)"
        >
          解锁
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
