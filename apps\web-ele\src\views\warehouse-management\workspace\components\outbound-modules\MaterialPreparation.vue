<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';

import { CountTo } from '@vben/common-ui';

import { getOutApplyItemNum } from '#/api/warehouse-management';
import { WS } from '#/utils/socket/common-socketio';

export default defineComponent({
  components: { CountTo },
  emits: ['cardClick'],
  setup(_props, { emit }) {
    const wsType = [
      'wm.prep.docstatus.checking',
      'wm.prep.docstatus.passed',
      'wm.prep.docstatus.reject',
      'wm.prep.docstatus.prepared',
      'wm.prep.docstatus.collected',
      'wm.prep.docstatus.return',
      'wm.prep.docstatus.closed',
      'wm.prep.export',
      'wm.prep.export.item',
    ];

    const DOC_STATUS = 'pending';

    const loading = ref(true);
    const pendingCount = ref(0); // 待备料
    const readyCount = ref(0); // 已备料

    const paramMapper = { pendingCount: '00', readyCount: '10' };

    const fetchInventoryData = async (params: Object) => {
      try {
        const response = await getOutApplyItemNum(params);
        return response || 0;
      } catch (error) {
        console.error('获取备料数据失败:', error);
        return 0;
      }
    };

    const loadData = async () => {
      try {
        const [pending, ready] = await Promise.all([
          fetchInventoryData({
            preparationStateList: paramMapper.pendingCount,
            docStatusList: DOC_STATUS,
          }),
          fetchInventoryData({
            preparationStateList: paramMapper.readyCount,
            docStatusList: DOC_STATUS,
          }),
        ]);

        pendingCount.value = pending.applyItemNum;
        readyCount.value = ready.applyItemNum;
      } finally {
        loading.value = false;
      }
    };

    const handleCardClick = (name: string, code: string) => {
      emit('cardClick', {
        name,
        params: {
          preparationStateList: code,
        },
        attrs: {
          wrapperClass:
            'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4',
          collapsed: true,
          collapsedRows: 2,
          showCollapseButton: true,
        },
      });
    };
    onMounted(() => {
      WS.on(wsType, loadData);

      loadData();
    });
    return {
      pendingCount,
      readyCount,
      loading,
      handleCardClick,
      paramMapper,
    };
  },
});
</script>
<template>
  <div
    class="hover:bg-primary-100 bg-primary-50 grid cursor-pointer grid-cols-2 gap-4 rounded-lg px-3 py-2 text-black"
  >
    <div
      class="flex h-7 items-end"
      @click="handleCardClick('material-preparation', '00')"
    >
      <span class="mb-1 text-sm">待备料</span>
      <span class="text-primary mx-1 mb-[2px] text-2xl font-bold">
        <CountTo
          v-loading="loading"
          :start-val="0"
          :end-val="pendingCount"
          :duration="1500"
          separator=""
        />
      </span>
      <span class="mb-1 text-sm">项</span>
    </div>
    <div
      class="flex h-7 items-end justify-end"
      @click="handleCardClick('material-preparation-already', '10')"
    >
      <span class="mb-1 mr-1 text-sm">已备料</span>
      <span class="mx-1 mb-[2px] text-2xl font-bold">
        <CountTo
          v-loading="loading"
          :start-val="0"
          :end-val="readyCount"
          :duration="1500"
          separator=""
        />
      </span>
      <span class="mb-1 text-sm">项</span>
    </div>
  </div>
</template>
