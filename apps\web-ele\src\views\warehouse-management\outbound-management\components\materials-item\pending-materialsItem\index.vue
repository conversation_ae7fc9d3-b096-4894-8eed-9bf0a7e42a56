<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItemData } from '../types';

import type { WarehouseListForMaterialListApi } from '#/api/warehouse-management/index';

import { computed, nextTick, onMounted, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { ElMessage, ElMessageBox } from 'element-plus';

import { getActiveWarehouseListByMaterial } from '#/api/warehouse-management/index';
import UnlockForm from '#/views/warehouse-management/inventory-lock/unlock-table/List.vue';

import Info from '../components/Info.vue';
import WarehouseItem from './warehouse-item/index.vue';

const props = defineProps({
  // 物料信息（包含仓库、库位信息）
  materialItemData: {
    type: Object as PropType<MaterialItemData.DocItem>,
    default: () => ({}),
  },

  warehouseListForMaterial: {
    type: Object as PropType<WarehouseListForMaterialListApi.WarehouseListForMaterialList>,
    default: () => ({}),
  },
  // 源单据类型标识
  origDocTypeCode: {
    type: String,
    default: '',
  },
});

const emits = defineEmits([
  'currentSelectedWarehouseListChange',
  'handleMaterialCode',
]);

export interface WarehouseItemDataType {
  warehouseCode: string;
  warehouseName: string;
  warehouseId: string;
  locationList: MaterialItemData.Item[];
  timestamp: number;
  quantitySum: number;
}

// 当前物料可出入库的仓库列表
const warehouseListForMaterialList = ref<
  WarehouseListForMaterialListApi.warehouseItem[]
>([]);

// 仓库item数据列表
const warehouseItemList = ref<WarehouseItemDataType[]>([]);

// 仓库ID,取第一个有库存的仓库
const defaultWarehouse = ref<{
  warehouseCode: string;
  warehouseId: string;
  warehouseName: string;
}>({
  warehouseCode: '',
  warehouseId: '',
  warehouseName: '',
});

// 已选中的仓库列表
const currentSelectedWarehouseList = ref<
  {
    warehouseCode: string;
    warehouseId: string;
    warehouseName: string;
  }[]
>([]);

// 自定义防抖函数
const warehouseChange = () => {
  const currentSelectedWarehouseIdList = getAllSelectedWarehouseId();

  currentSelectedWarehouseList.value = warehouseListForMaterialList.value
    .filter((item) => currentSelectedWarehouseIdList.includes(item.warehouseId))
    .map((item) => ({
      warehouseCode: item.warehouseCode,
      warehouseId: item.warehouseId,
      warehouseName: item.warehouseName,
    }));

  emits('currentSelectedWarehouseListChange');
};

// 获取全部已选中的仓库id
const getAllSelectedWarehouseId = () => {
  return warehouseItemRef.value.map((item) => item.currentWarehouseId);
};

export interface SelectWarehouseListType {
  disabled: boolean;
  label: string;
  value: string;
  warehouseName: string;
  /* 可用量 */
  availableInventory: number;
  /* 库存量 */
  inventory: number;
}

// 仓库下拉框数据：处理可入库的仓库列表，不能选择已选中的仓库
const selectWarehouseList = computed<SelectWarehouseListType[]>(() => {
  return warehouseListForMaterialList.value.map((item) => {
    return {
      disabled:
        item.inventory === 0 ||
        currentSelectedWarehouseList.value.some(
          (subItem) => subItem.warehouseId === item.warehouseId,
        ),
      label: `${item.warehouseName}-可用量${item.availableInventory}`,
      value: item.warehouseId,
      availableInventory: item.availableInventory,
      inventory: item.inventory,
      warehouseName: item.warehouseName,
    };
  });
});

// 计算总数
const getTotalQuantity = (quantityList: number[]) => {
  return quantityList.reduce((acc: number, item: number) => acc + item, 0);
};

// 当前仓库可用量
const currentWarehouseAvailableQuantity = computed(() => {
  return getTotalQuantity(
    warehouseListForMaterialList.value.map((item) => {
      return item.availableInventory;
    }),
  );
});

// 当前仓库库存量
const currentWarehouseInventoryQuantity = computed(() => {
  return getTotalQuantity(
    warehouseListForMaterialList.value.map((item) => {
      return item.inventory;
    }),
  );
});

const loading = ref(true);

onMounted(() => {
  warehouseListForMaterialList.value =
    props.warehouseListForMaterial.warehouseList;

  defaultWarehouse.value =
    // 首先尝试找到主仓库且库存大于0的项
    props.warehouseListForMaterial.warehouseList.find(
      (item) =>
        item.warehouseId === props.warehouseListForMaterial.mainWarehouseId &&
        item.inventory > 0,
    ) ??
    // 如果找不到主仓库，则找第一个库存大于0的项
    props.warehouseListForMaterial.warehouseList.find(
      (item) => item.inventory > 0,
    ) ??
    // 如果都找不到，返回空对象
    ({} as WarehouseListForMaterialListApi.warehouseItem);

  if (props.materialItemData.itemList.length > 0) {
    const mergeData: WarehouseItemDataType[] = [];
    // 聚合相同仓库的库位数据
    props.materialItemData.itemList.forEach((item, itemIndex) => {
      const index = mergeData.findIndex(
        (subItem) => subItem.warehouseId === item.warehouseId,
      );
      if (index === -1) {
        mergeData.push({
          warehouseCode: item.warehouseCode,
          warehouseName: item.warehouseName,
          warehouseId: item.warehouseId,
          locationList: [item],
          timestamp: Date.now() + itemIndex,
          quantitySum: props.materialItemData.quantitySum,
        });
      } else {
        // 合并库位数据
        const targetItem = mergeData[index];
        if (targetItem) {
          targetItem.locationList.push(item);
        }
      }
    });

    warehouseItemList.value = mergeData;
  } else {
    // 初始化仓库item数据
    const initWarehouseItemData = {
      warehouseCode: defaultWarehouse.value.warehouseCode,
      warehouseName: defaultWarehouse.value.warehouseName,
      warehouseId: defaultWarehouse.value.warehouseId,
      // 时间戳
      timestamp: Date.now(),
      locationList: [] as MaterialItemData.Item[],
      quantitySum: props.materialItemData.quantitySum,
    };
    warehouseItemList.value = [initWarehouseItemData];
  }

  currentSelectedWarehouseList.value =
    warehouseItemList.value.length > 0
      ? warehouseItemList.value.map((item) => ({
          warehouseCode: item.warehouseCode,
          warehouseId: item.warehouseId,
          warehouseName: item.warehouseName,
        }))
      : [];

  emits('currentSelectedWarehouseListChange');
  loading.value = false;
});

// 填入数量
const entryQuantitySum = ref(0);
// 计算填入数量
const getEntryQuantitySum = async () => {
  const fillQuantityList = warehouseItemRef.value.map(
    (item) => item.currentWarehouseFillQuantity,
  );
  entryQuantitySum.value = fillQuantityList.reduce(
    (acc: number, item: number) => acc + item,
    0,
  );
};

/**
 *  申请数量与填入数量对比,填入数量 <= 申请数量,返回true
 */

const comparisonQuantitySum = () => {
  const { materialCode, materialName, materialId } = props.materialItemData;
  const isResult =
    currentWarehouseInventoryQuantity.value === 0
      ? false
      : entryQuantitySum.value <= currentWarehouseAvailableQuantity.value &&
        entryQuantitySum.value <= props.materialItemData.quantitySum;

  return {
    isResult,
    message:
      currentWarehouseInventoryQuantity.value === 0
        ? `【${materialName}】暂无库存`
        : `【${materialName}】填入数量大于库存或应出数量`,
    materialCode,
    materialId,
    materialName,
  };
};

// 所有仓库项ref
const warehouseItemRef = ref<InstanceType<typeof WarehouseItem>[]>([]);

// 校验
const validateFormData = async () => {
  const validateFormDataRes = await Promise.all(
    warehouseItemRef.value.map((item) => item.validateFormData()),
  );
  return validateFormDataRes;
};

// 获取所有仓库项的表单数据
const getWarehouseItemData = async (hasWarehouseId: boolean = false) => {
  if (warehouseItemRef.value.length === 0) {
    return [];
  }
  const formData = await Promise.all(
    warehouseItemRef.value.map((item) => item.getFormData(hasWarehouseId)),
  );

  if (formData.length === 0) {
    entryQuantitySum.value = 0;
    return;
  }

  return formData;
};

// 增加仓库item
const addWarehouseItem = async () => {
  if (
    await ElMessageBox.confirm('确定添加仓库吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    warehouseItemList.value.push({
      warehouseCode: '',
      warehouseName: '',
      warehouseId: '',
      timestamp: Date.now(),
      locationList: [] as MaterialItemData.Item[],
      quantitySum: props.materialItemData.quantitySum,
    });
  }
};

// 删除仓库item
const deleteWarehouseItem = async (timestamp: number) => {
  if (
    await ElMessageBox.confirm('确定删除吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    warehouseItemList.value = warehouseItemList.value.filter(
      (item) => item.timestamp !== timestamp,
    );

    nextTick(() => {
      getEntryQuantitySum();
      warehouseChange();
    });
  }
};

// 将表格数据处理成需要提交的数据，这里需要校验表格
const getData = async () => {
  const validateFormDataRes = await validateFormData();
  if (validateFormDataRes.includes(false)) {
    throw new Error(
      `请先检查 【${props.materialItemData.materialName}】 表格数据`,
    );
  }
  const formData = await getWarehouseItemData(true);

  const subData = formData.map((item: any) => {
    return {
      ...item,
      materialId: props.materialItemData.materialId,
    };
  });

  return subData;
};

const handleMaterialCode = () => {
  emits('handleMaterialCode', props.materialItemData.materialId);
};

const getActiveWarehouseListByMaterialData = async () => {
  try {
    loading.value = true;
    const res = await getActiveWarehouseListByMaterial({
      materialId: props.materialItemData.materialId,
      docTypeCode: props.origDocTypeCode,
    });
    warehouseListForMaterialList.value = res.warehouseList;
  } catch {
    ElMessage.error('获取仓库列表失败');
  } finally {
    loading.value = false;
  }
};

/** 解锁库存抽屉 */
const [UnlockFormDrawer, unlockFormDrawerApi] = useVbenDrawer({
  placement: 'left',
  destroyOnClose: true,
  showCancelButton: true,
  showConfirmButton: false,
  title: '解锁库存',
});

const handlePurchaseApply = () => {
  ElMessage.warning('采购申请功能暂未开放');
};

const handleUnlockInventory = () => {
  unlockFormDrawerApi.open();
};

const handleUnlockSuccessState = (state: boolean) => {
  if (state) {
    getActiveWarehouseListByMaterialData();
  }
};

defineExpose({
  getData,
  comparisonQuantitySum,
  currentSelectedWarehouseList,
  entryQuantitySum,
});
</script>
<template>
  <div class="rounded-lg bg-white" v-loading="loading">
    <Info
      :material-item-data="materialItemData"
      @handle-material-code="handleMaterialCode"
    >
      <template #rightExtra>
        <div
          class="flex justify-end text-sm font-medium text-red-500"
          v-if="
            warehouseListForMaterialList.length > 0 &&
            currentWarehouseAvailableQuantity < materialItemData.quantitySum
          "
        >
          <span class="mr-1">库存不足</span>

          <el-button
            type="primary"
            link
            class="underline"
            @click="handlePurchaseApply"
          >
            <el-icon><Right /></el-icon>
            采购申请
          </el-button>

          <el-button
            v-if="
              currentWarehouseInventoryQuantity >= materialItemData.quantitySum
            "
            type="primary"
            link
            class="underline"
            @click="handleUnlockInventory"
          >
            <el-icon><Right /></el-icon>
            解锁库存
          </el-button>
        </div>
        <div class="text-end text-sm text-gray-800">
          <span>应出 / 实出：</span>
          <span class="font-medium">{{ materialItemData.quantitySum }}</span>
          <span class="mx-1">/</span>
          <span
            class="font-medium"
            :class="[
              entryQuantitySum === materialItemData.quantitySum
                ? 'text-green-500'
                : 'text-red-500',
            ]"
          >
            {{ entryQuantitySum }}
          </span>
          <span class="ml-1 text-gray-500">
            ({{ materialItemData.baseUnitLabel || '-' }})
          </span>
        </div>
      </template>
    </Info>

    <div class="relative mt-6 space-y-3">
      <div class="absolute right-0 top-[-25px]">
        <ElButton
          type="primary"
          link
          @click="addWarehouseItem"
          v-if="
            warehouseItemList.length <
            warehouseListForMaterialList.filter((item) => item.inventory > 0)
              .length
          "
        >
          <el-icon class="!text-base">
            <Plus />
          </el-icon>
          添加仓库
        </ElButton>
      </div>
      <div
        v-if="currentWarehouseInventoryQuantity === 0"
        class="flex items-center justify-center"
      >
        <span class="text-gray-500">暂无库存</span>
      </div>
      <template v-else>
        <WarehouseItem
          v-for="item in warehouseItemList"
          :key="item.timestamp"
          ref="warehouseItemRef"
          :warehouse-item-data="item"
          :select-warehouse-list="selectWarehouseList"
          @entry-quantity-change="getEntryQuantitySum"
          @warehouse-change="warehouseChange"
          :material-id="materialItemData.materialId"
          :entry-quantity-sum="entryQuantitySum"
          :default-location-id="warehouseListForMaterial.mainLocationId"
        >
          <template #delete-wrapper v-if="warehouseItemList.length > 1">
            <ElButton
              type="danger"
              link
              size="small"
              @click="deleteWarehouseItem(item.timestamp)"
              title="删除仓库"
            >
              <el-icon class="!text-base">
                <Delete />
              </el-icon>
            </ElButton>
          </template>
        </WarehouseItem>
      </template>
    </div>
  </div>

  <UnlockFormDrawer class="h-full w-6/12">
    <UnlockForm
      :material-id="materialItemData.materialId"
      @unlock-success-state="handleUnlockSuccessState"
    />
  </UnlockFormDrawer>
</template>
