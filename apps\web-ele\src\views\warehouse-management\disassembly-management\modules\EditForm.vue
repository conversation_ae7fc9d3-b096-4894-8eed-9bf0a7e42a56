<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElButton, ElMessage, ElMessageBox } from 'element-plus';

import { delAssemblyDoc, saveOrModAssemblyDoc, submitAssemblyDoc } from '#/api';
import DisassemblyFormEdit from '#/views/warehouse-management/disassembly-management/components/disassembly-edit-form/index.vue';
import MaterialFormEdit from '#/views/warehouse-management/disassembly-management/components/material-edit-form/index.vue';

const loading = ref(false);
/** 共享数据 */
const sharedData = ref();
/** 拆卸信息ref*/
const disassemblyFormRef = ref();
/** 原料明细ref*/
const materialFormRef = ref();
/** 母件BomID */
const bomId = ref();
/** 拆卸数量 */
const currentQuantity = ref();
/** 拆卸旧数量 */
const oldQuantity = ref();

const [Modal, modalApi] = useVbenModal({
  confirmText: '提交',
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    onSubmit();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      sharedData.value = modalApi.getData<Record<string, any>>();
    }
  },
});

/** 刷新列表 */
const refreshList = () => {
  sharedData.value.refreshList();
  modalApi.close();
};

/** 拆卸成品变化 */
const productChanged = (id: string) => {
  bomId.value = id;
};

/** 拆卸数量变化 */
const quantityChange = (currentValue: number, oldValue: number) => {
  currentQuantity.value = currentValue;
  oldQuantity.value = oldValue;
};

/** 校验表单 */
const validateForm = async () => {
  // 校验调整信息表单 // 校验物料信息表单
  const [verification, verification2] = await Promise.all([
    disassemblyFormRef.value?.validateForm(),
    materialFormRef.value?.validateForm(),
  ]);

  // 获取表单数据
  const data = await getFormData();
  if (data.assemblyMaterialList.length === 0) {
    ElMessage.error('请填写原料信息');
    return false;
  }
  if (!verification || !verification2) {
    ElMessage.error('请填写完整表单');
    return false;
  }

  // 检查物料是否重复
  const seenIds = new Set();
  for (const item of data.assemblyMaterialList) {
    // 检查当前materialId是否已存在
    if (seenIds.has(item.materialId)) {
      // 发现重复，返回true和重复的ID
      ElMessage.error('原料存在重复，请确保所有原料唯一');
      return false;
    }
    // 将当前ID添加到集合中
    seenIds.add(item.materialId);
  }

  return true;
};

/** 获取表单数据 */
const getFormData = async () => {
  const [data, data2] = await Promise.all([
    disassemblyFormRef.value?.getFormData(),
    materialFormRef.value?.getFormData(),
  ]);
  return {
    ...data,
    assemblyMaterialList: data2,
  };
};

/** 公共弹窗 */
const dialog = async (content: string, title: string) => {
  try {
    await ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    });
    return true;
  } catch {
    return false;
  }
};

/** 提交表单 新增*/
const onSubmit = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    const isUpdate = !!sharedData.value.assemblyDocId;
    if (await dialog('确定提交单据吗？', '提示')) {
      loading.value = true;
      await submitAssemblyDoc({
        assemblyDocId: isUpdate ? sharedData.value.assemblyDocId : '',
        ...data,
      });
      ElMessage.success(isUpdate ? '提交成功' : '新增成功');
      refreshList();
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('提交失败');
  } finally {
    loading.value = false;
  }
};

/** 提交暂存 */
const onSave = async () => {
  try {
    if (!(await validateForm())) return;
    // 获取表单数据
    const data = await getFormData();
    const isUpdate = !!sharedData.value.assemblyDocId;
    if (await dialog('确定提交暂存吗？', '提示')) {
      loading.value = true;
      await saveOrModAssemblyDoc({
        assemblyDocId: isUpdate ? sharedData.value.assemblyDocId : '',
        ...data,
      });
      ElMessage.success('提交暂存成功');
      refreshList();
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('提交失败');
      console.error(error);
    }
  } finally {
    loading.value = false;
  }
};

/** 删除单据 */
const deleteDoc = async () => {
  try {
    if (await dialog('确定提交单据吗？', '提示')) {
      await delAssemblyDoc(sharedData.value.assemblyDocId);
      ElMessage.success('删除成功');
      refreshList();
    }
  } catch {
    ElMessage.error('删除失败');
  }
};

defineExpose({
  onSubmit,
  onSave,
  validateForm,
  deleteDoc,
  loading,
});
</script>
<template>
  <Modal>
    <div v-loading="loading">
      <DisassemblyFormEdit
        ref="disassemblyFormRef"
        :assembly-doc-id="sharedData.assemblyDocId"
        :assembly-doc-number="sharedData.assemblyDocNumber"
        @product-changed="productChanged"
        @quantity-change="quantityChange"
      />

      <MaterialFormEdit
        ref="materialFormRef"
        :assembly-doc-id="sharedData.assemblyDocId"
        :assembly-doc-number="sharedData.assemblyDocNumber"
        :bom-id="bomId"
        :current-quantity="currentQuantity"
        :old-quantity="oldQuantity"
      />
    </div>

    <template #center-footer>
      <ElButton
        v-if="sharedData.assemblyDocId"
        @click="deleteDoc"
        type="danger"
      >
        删除
      </ElButton>
      <ElButton type="primary" @click="onSave()"> 暂存 </ElButton>
    </template>
  </Modal>
</template>
