<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { getStagnantMaterialSummary } from '#/api/warehouse-management';
import PanelCard from '#/components/panel-card/Index.vue';

const chartRef = ref<EchartsUIType>();
const chartData = ref<any[]>([]);

// 初始化图表
const { renderEcharts, getChartInstance } = useEcharts(chartRef);

// 根据库存梯度计算点大小
const getSymbolSizeByInventory = (inventory: number): number => {
  if (inventory < 10) return 5; // (0-10)
  if (inventory < 100) return 15; // [10,100)
  if (inventory < 1000) return 30; // [100,1000)
  if (inventory < 10_000) return 50; // [1000,10000)
  return 70; // [10000, ∞）
};

// 根据点大小计算透明度 (点越大，透明度越低)
const getOpacityBySymbolSize = (size: number): number => {
  const maxSize = 70; // 最大点尺寸
  const baseOpacity = 0.8; // 基础透明度（点最小时的透明度）
  const minOpacity = 0.3; // 最小透明度（点最大时的透明度）

  const sizeRatio = Math.min(size / maxSize, 1);
  let opacity = baseOpacity - sizeRatio * (baseOpacity - minOpacity);

  opacity = Math.min(Math.max(opacity, minOpacity), baseOpacity);

  return Number.parseFloat(opacity.toFixed(1));
};

const fetchIdleSummaryData = async (params?: Object) => {
  try {
    const inBoundSummaryResult = await getStagnantMaterialSummary(params);
    // 排序数据，确保X轴按时间顺序显示
    inBoundSummaryResult.sort(
      (a: any, b: any) =>
        new Date(a.lastInTime as string).getTime() -
        new Date(b.lastInTime as string).getTime(),
    );

    inBoundSummaryResult.forEach((item: any) => {
      item.originalDate = item.lastInTime.split(' ')[0];
    });

    chartData.value = inBoundSummaryResult;
  } catch (error) {
    console.error('获取呆料图表数据失败:', error);
  }
};

// 返回类型设置为 any
const getInitialOptions = (): any => {
  const series = [
    {
      name: '物料库存',
      type: 'scatter',
      itemStyle: {
        opacity: 0.8, // 默认透明度，会被数据点的opacity覆盖
      },
    },
  ];

  return {
    grid: {
      top: '10%',
      right: '5%',
      left: '5%',
      bottom: '12%',
    },
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: any) => {
        const data = params.data;
        return `
          <div class="text-primary mb-1">${data.materialName}</div>
          <div>库存数量 : ${data.inventory}</div>
          <div>最近入库时间 : ${data.originalDate}</div>
          <div>最近出库时间 : ${data.lastOutTime}</div>
        `;
      },
    },
    toolbox: {
      feature: {
        dataView: { show: true, readOnly: false },
        saveAsImage: { show: true },
      },
    },
    xAxis: [
      {
        type: 'time', // 改为时间轴类型
        name: '最近入库时间',
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          // 格式化x轴标签显示（只显示日期部分）
          formatter: (value: any) => {
            const date = new Date(value);
            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '库存量',
      },
    ],
    series,
  };
};

const updateChartData = () => {
  const chart = getChartInstance();
  if (chart && chartData.value.length > 0) {
    const inventoryDates = chartData.value.map((item) => {
      const size = getSymbolSizeByInventory(item.inventory);
      const opacity = getOpacityBySymbolSize(size);
      return {
        value: [
          new Date(item.lastInTime), // x轴使用完整时间对象
          item.inventory, // y轴使用库存量
        ],
        symbolSize: size,
        itemStyle: {
          opacity,
        },
        // 保留其他属性用于tooltip
        materialName: item.materialName,
        originalDate: item.originalDate, // 格式化后的日期（仅年月日）
        lastOutTime: item.lastOutTime,
        inventory: item.inventory,
      };
    });

    chart.setOption({
      series: [{ data: inventoryDates }],
    });
  }
};

watch(chartData, updateChartData);

onMounted(() => {
  // 初始化图表配置（不含具体数据）
  const initialOptions = getInitialOptions();
  renderEcharts(initialOptions);

  // 设置初始数据
  updateChartData();

  fetchIdleSummaryData();
});
</script>

<template>
  <PanelCard title="呆料分析">
    <template #default>
      <div class="mt-3">
        <el-empty
          v-if="!chartData || chartData.length === 0"
          description="暂无数据"
        />
        <EchartsUI
          v-show="chartData && chartData.length > 0"
          height="400px"
          ref="chartRef"
        />
      </div>
    </template>
  </PanelCard>
</template>
