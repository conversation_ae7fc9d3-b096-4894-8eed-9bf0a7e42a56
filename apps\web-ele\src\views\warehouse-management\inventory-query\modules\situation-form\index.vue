<script setup lang="ts">
import type { InventoryQueryApi } from '#/api';

import { h, nextTick, onMounted, ref } from 'vue';

import { useAccess } from '@vben/access';
import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '@girant/adapter';
import {
  ElAlert,
  ElButton,
  ElIcon,
  ElInputNumber,
  ElMessage,
} from 'element-plus';

import { getInvcByWarehouseIdAndMaterialId } from '#/api';
import FormCard from '#/components/form-card/Index.vue';
import LockForm from '#/views/warehouse-management/inventory-lock/modules/lock-form/index.vue';

import UnlockForm from '../../../inventory-lock/unlock-table/List.vue';
import { useFormSchema } from './data';

const props = defineProps({
  /** 物料id */
  materialId: {
    type: String,
    default: '',
  },
  /** 仓库id*/
  warehouseId: {
    type: String,
    default: '',
  },
});
const { hasAccessByCodes } = useAccess();
const loading = ref(false);
/** 锁库表单ref */
const lockFormRef = ref<InstanceType<typeof LockForm>>();
/** 库存详情 */
const inventoryData = ref<InventoryQueryApi.InventoryData>();
/** 模态框组件 锁库*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  showCancelButton: true,
  showConfirmButton: true,
  confirmText: '提交',
  onConfirm: () => {
    lockFormRef.value!.formApi.validateAndSubmitForm();
  },
  onCancel: () => {
    lockFormRef.value!.formApi.resetForm();
    formModalApi.close();
  },
});
/** 模态框组件 解锁*/
const [UnlockFormModal, unlockformModalApi] = useVbenModal({
  footer: true,
  showCancelButton: false,
  showConfirmButton: true,
  closable: false,
  closeOnPressEscape: false,
  closeOnClickModal: false,
  confirmText: '关闭',
  onConfirm: () => {
    getData();
    unlockformModalApi.close();
  },
});
/** 库存情况表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 xl:grid-cols-3',
});

/** 获取数据 */
const getData = async () => {
  try {
    loading.value = true;
    const data = await getInvcByWarehouseIdAndMaterialId(
      props.warehouseId,
      props.materialId,
    );
    inventoryData.value = data;
    // 设置表单数据
    formApi.setValues(data);
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
const key = ref(0);
/** 锁库存 */
const lockInventory = () => {
  formModalApi
    .setState({
      title: '新增锁库',
    })
    .open();
  // 定制锁库表单
  nextTick(async () => {
    const schema = lockFormRef.value?.formApi.getState()?.schema;
    if (!schema) return;
    // 设置仓库
    const warehouse = schema.find((item) => item.fieldName === 'warehouseId');
    warehouse!.component = h('div', null, inventoryData.value?.warehouseName);
    warehouse!.rules = undefined;
    // 设置物料
    const material = schema.find((item) => item.fieldName === 'materialId');
    material!.component = h('div', null, inventoryData.value?.materialName);
    material!.rules = undefined;
    // 设置基本单位
    const baseUnit = schema.find((item) => item.fieldName === 'baseUnitLabel');
    baseUnit!.component = h('div', null, inventoryData.value?.baseUnitLabel);
    // 设置规格型号
    const materialSpecs = schema.find(
      (item) => item.fieldName === 'materialSpecs',
    );
    materialSpecs!.component = h(
      'div',
      null,
      inventoryData.value?.materialSpecs,
    );
    // 设置锁库表单
    const blockQuantity = schema.find(
      (item) => item.fieldName === 'blockQuantity',
    );
    blockQuantity!.component = (props: any) => {
      return h(
        'div',
        {
          class: 'flex items-center gap-2',
        },
        [
          h(
            ElInputNumber,
            {
              modelValue: props.modelValue,
              'onUpdate:modelValue': (value: number | undefined) => {
                if (value !== undefined && value !== null) {
                  // 向外部发送更新事件
                  props['onUpdate:modelValue']?.(value);
                }
              },
              min: 1,
              max: inventoryData.value?.availableInventory,
              precision: 0,
            },
            {},
          ),
          h(
            'div',
            {
              class: `text-sm ml-4`,
            },
            {
              default: () =>
                `当前库存量：${inventoryData.value?.inventory || 0},可用量：${inventoryData.value?.availableInventory || 0}`,
            },
          ),
        ],
      );
    };
    lockFormRef.value?.formApi.updateSchema(schema);
  });
};
/** 解锁库存 */
const unlockInventario = () => {
  unlockformModalApi
    .setState({
      title: '解锁列表',
    })
    .open();
};
/** 申请采购 */
const purchase = () => {
  ElMessage.success('申请采购 待开发');
};
/** 提交成功 */
const submitSuccess = () => {
  formModalApi.close();
  getData();
};
onMounted(() => {
  if (props.materialId && props.warehouseId) {
    getData();
  }
});
defineExpose({
  FormModal,
  formModalApi,
  Form,
  formApi,
});
</script>
<template>
  <FormModal class="h-full w-8/12">
    <LockForm
      :key="key"
      ref="lockFormRef"
      :inventory-data="inventoryData"
      @submit-success="submitSuccess"
    />
  </FormModal>
  <UnlockFormModal class="h-full w-8/12" @on-before-close="getData()">
    <UnlockForm :material-id="materialId" :warehouse-id="warehouseId" />
  </UnlockFormModal>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>库存情况</span>
    </template>
    <Form>
      <template #availableInventory="row">
        {{ row.value }}
        <ElButton
          v-if="row.value > 0"
          class="ml-[10px]"
          link
          type="primary"
          @click="lockInventory()"
          v-access:code="'wm:inventory:lock:lock'"
        >
          锁库存
        </ElButton>
      </template>
      <template #blockQuantity="row">
        <ElButton
          link
          type="primary"
          @click="unlockInventario()"
          v-access:code="'wm:inventory:lock:unlock'"
        >
          <span class="text-[16px] underline">
            {{ row.value }}
          </span>
        </ElButton>
        <span
          class="text-[16px]"
          v-if="!hasAccessByCodes(['wm:inventory:lock:unlock'])"
        >
          {{ row.value }}
        </span>
      </template>
      <template #safetyInventory="row">
        <span v-if="!row.value"> 暂无配置 </span>
        <div v-else class="flex flex-nowrap items-center">
          <span>{{ row.value }}</span>
          <span
            v-if="!inventoryData?.isJoinSafetyStockWarn"
            class="ml-[10px] text-[14px]"
          >
            (不做库存预警)
          </span>
          <span class="ml-[10px]" v-if="inventoryData?.isSafetyStockWarn">
            <ElAlert type="error" show-icon :closable="false">
              <template #icon>
                <ElIcon :size="16">
                  <WarningFilled />
                </ElIcon>
              </template>
              <template #title>
                当前可用量是{{
                  inventoryData?.availableInventory || 0
                }},低于安全库存！
              </template>
            </ElAlert>
          </span>
          <ElButton class="ml-[14px]" link type="primary" @click="purchase">
            申请采购
          </ElButton>
        </div>
      </template>
      <template #obsoletePeriod="row">
        <span v-if="!row.value"> 暂无配置 </span>
        <div v-else class="flex flex-nowrap items-center">
          <span>{{ row.value }}天</span>
          <span
            class="ml-[10px] text-[14px]"
            v-if="!inventoryData?.isJoinObsoleteAnalysis"
          >
            (不做呆滞分析)
          </span>
          <span class="ml-[10px]" v-if="inventoryData?.isObsoleteAnalysis">
            <ElAlert type="warning" show-icon :closable="false">
              <template #icon>
                <ElIcon :size="16">
                  <WarnTriangleFilled />
                </ElIcon>
              </template>
              <template #title>
                已呆滞{{ inventoryData?.obsoleteDay || 0 }}天
              </template>
            </ElAlert>
          </span>
        </div>
      </template>
    </Form>
  </FormCard>
</template>
