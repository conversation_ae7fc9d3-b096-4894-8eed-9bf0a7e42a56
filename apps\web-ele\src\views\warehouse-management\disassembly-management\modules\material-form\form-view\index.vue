<script setup lang="ts">
import type { DisassemblyApi } from '#/api';

import { onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElLink, ElMessage, ElPopover } from 'element-plus';

import { getAssemblyDocDetail, getProdBomDetail } from '#/api';
import FormCard from '#/components/form-card/Index.vue';
import MaterialForm from '#/views/warehouse-management/basic-data/material/modules/material-form/index.vue';

import MaterialViewField from '../../../components/material-view-field/index.vue';
import { openModal } from '../../method';
import { useBomColumns, useColumns } from './data';

const props = defineProps({
  /** 组装拆卸单据ID */
  assemblyDocId: {
    type: String,
    default: '',
  },
  /** 组装拆卸单据编号*/
  assemblyDocNumber: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
/** 拆卸单数据 */
const data = ref<DisassemblyApi.AssemblyDocDetail>();
/** 所选物料Id */
const productMaterialId = ref();
/** 母件Bom物料列表 */
const bomMaterialList = ref([]);

onMounted(() => {
  if (props.assemblyDocId || props.assemblyDocNumber) {
    getData();
  }
});

/**  获取数据*/
const getData = async () => {
  try {
    loading.value = true;
    const res = await getAssemblyDocDetail(
      props.assemblyDocId,
      props.assemblyDocNumber,
      true,
    );
    data.value = res;
    // 获取母件Bom信息
    const prodBomInfo = await getProdBomDetail({
      materialId: res.productMaterialId,
      isSonDetail: true,
    });
    // 保存bom物料列表
    bomMaterialList.value = prodBomInfo.children.map((item: any) => ({
      materialId: item.materialId,
      materialSpecs: item.materialSpecs,
      baseUnitLabel: item.baseUnitLabel,
      quantity: item.usageQuantity,
    }));
    // 刷新差异值
    refreshDifference();
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

/** 刷新所有Bom差异 */
const refreshDifference = async () => {
  // 创建物料ID映射表，用于快速查找
  const bomMaterialMap = new Map(
    bomMaterialList.value.map((item: any) => [item.materialId, true]),
  );
  // 处理数据
  data.value?.assemblyMaterialList.forEach((item: any) => {
    // 检查物料是否在BOM列表中存在，并添加isEditable属性
    if (bomMaterialMap.has(item.materialId)) {
      // 查找物料在bomMaterialList中的配置
      const materialItem: any = bomMaterialList.value.find(
        (ele: any) => ele.materialId === item.materialId,
      );
      if (materialItem) {
        // 计算差值 = 所需数量 - 当前数量
        item.difference =
          item.quantity - materialItem.quantity * (data.value?.quantity ?? 1);
      }
    } else {
      item.difference = item.quantity;
    }
  });
  // 设置表单内容
  gridApi.setGridOptions({
    data: data.value?.assemblyMaterialList,
  });
};

/** 原料明细表单 */
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useColumns(),
    data: [],
    minHeight: 150,
    border: true,
    pagerConfig: {
      enabled: false,
    },
  },
});

/** 差异表单 */
const [BomGrid, bomGridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useBomColumns(),
    data: [],
    minHeight: 150,
    border: true,
    pagerConfig: {
      enabled: false,
    },
  },
});

/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  footer: true,
  confirmText: '提交',
  showCancelButton: true,
  showConfirmButton: true,
});

/** 显示物料详情信息 */
const showProductMaterial = (row: any) => {
  productMaterialId.value = row.materialId;
  openModal(formModalApi, false, '原料详情');
};

/** 显示差异表单 */
const showPopover = async () => {
  const tableData = data.value?.assemblyMaterialList;
  // 获取表单内容
  const processedTableData = tableData
    ?.map((item: any) => {
      const baseData = {
        materialId: item.materialId,
        materialName: `${item.materialName} (${item.materialCode})`,
        usageQuantity: 0,
        quantity: item.quantity,
        difference: item.difference,
      };

      // 关联 bomMaterialList 中的 usageQuantity
      const matchedBomItem: any = bomMaterialList.value.find(
        (bomItem: any) => bomItem.materialId === item.materialId,
      );

      if (matchedBomItem) {
        baseData.usageQuantity =
          matchedBomItem.quantity * (data.value?.quantity ?? 1);
      }

      return baseData;
    })
    .filter((item: any) => item.difference && item.difference !== 0);
  // 设置表格内容
  bomGridApi.setGridOptions({
    data: processedTableData,
  });
};
</script>

<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>原料明细</span>
    </template>

    <FormModal class="h-full w-10/12">
      <MaterialForm :material-id="productMaterialId" />
    </FormModal>

    <Grid>
      <template #toolbar-tools>
        <ElPopover
          placement="bottom-end"
          width="600"
          trigger="click"
          @show="showPopover"
        >
          <BomGrid>
            <template #difference="{ row }">
              <span v-if="row.difference > 0" class="text-red-500">
                +{{ row.difference }}
              </span>
              <span v-else-if="row.difference < 0" class="text-green-500">
                {{ row.difference }}
              </span>
              <span v-else>-</span>
            </template>
          </BomGrid>
          <template #reference>
            <ElLink type="primary"> BOM差异分析 </ElLink>
          </template>
        </ElPopover>
      </template>
      <template #materialName="{ row }">
        <MaterialViewField
          :material-name="row.materialName"
          :material-code="row.materialCode"
          :picture-file-id="row.pictureFileId"
          @click="showProductMaterial(row)"
        />
      </template>
      <template #difference="{ row }">
        <span v-if="row.difference > 0" class="text-red-500">
          +{{ row.difference }}
        </span>
        <span v-else-if="row.difference < 0" class="text-green-500">
          {{ row.difference }}
        </span>
        <span v-else>-</span>
      </template>
    </Grid>
  </FormCard>
</template>
