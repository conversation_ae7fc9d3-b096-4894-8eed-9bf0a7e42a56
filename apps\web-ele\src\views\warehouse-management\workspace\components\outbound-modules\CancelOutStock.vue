<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';

import { CountTo } from '@vben/common-ui';

import {
  getOutApplyMaterialItemNum,
  getOutApplyMaterialNum,
  getOutBoundDocNum,
} from '#/api/warehouse-management';
import TimeSelect from '#/components/time-select/Index.vue';
import { WS } from '#/utils/socket/common-socketio';
import { fetchPeriodTime } from '#/utils/trans-period-time';

export default defineComponent({
  components: { CountTo, TimeSelect },
  emits: ['cardClick'],
  setup(_props, { emit }) {
    const wsType = [
      'wm.outbound.docstatus.rescind.passed',
      'wm.outbound.docstatus.rescind.checking',
      'wm.outbound.docstatus.rescind.reject',
    ];

    const DOC_STATUS = 'closed';
    const timeSelectRef = ref();

    const cancelDocLoading = ref(true);
    const actualLoading = ref(true);
    const quantityLoading = ref(true);
    const inBoundDocNum = ref(0); // 单据
    const actualItemNum = ref(0); // 物料类别
    const quantitySum = ref(0); // 物料数量

    interface FetchParams {
      executorTimePeriodType?: string;
      executorTimePeriodUnit?: string;
      executorTimeInterval?: string;
    }
    const fetchCancelOutReqDocNum = async (params: FetchParams) => {
      try {
        const result = await getOutBoundDocNum({
          docStatusList: DOC_STATUS,
          closeTimePeriodType: params.executorTimePeriodType,
          closeTimePeriodUnit: params.executorTimePeriodUnit,
          closeTimeInterval: params.executorTimeInterval,
        });
        inBoundDocNum.value = result.outBoundDocNum || 0;
      } catch (error) {
        console.error('获取出库取消申请单数量失败:', error);
      } finally {
        cancelDocLoading.value = false;
      }
    };

    const fetchActualItemNum = async (params: FetchParams) => {
      try {
        const actualItemNumResult = await getOutApplyMaterialItemNum({
          docStatusList: DOC_STATUS,
          closeTimePeriodType: params.executorTimePeriodType,
          closeTimePeriodUnit: params.executorTimePeriodUnit,
          closeTimeInterval: params.executorTimeInterval,
        });
        actualItemNum.value = actualItemNumResult.materialNum || 0;
      } catch (error) {
        console.error('获取出库取消申请物料种类失败:', error);
      } finally {
        actualLoading.value = false;
      }
    };

    const fetchActualMaterialNum = async (params: FetchParams) => {
      try {
        const quantitySumResult = await getOutApplyMaterialNum({
          docStatusList: DOC_STATUS,
          closeTimePeriodType: params.executorTimePeriodType,
          closeTimePeriodUnit: params.executorTimePeriodUnit,
          closeTimeInterval: params.executorTimeInterval,
        });
        quantitySum.value = quantitySumResult.quantitySum || 0;
      } catch (error) {
        console.error('获取出库取消申请物料数量失败:', error);
      } finally {
        quantityLoading.value = false;
      }
    };

    const fetchData = async () => {
      const params = timeSelectRef.value?.getValues();
      await fetchCancelOutReqDocNum(params);
      await fetchActualItemNum(params);
      await fetchActualMaterialNum(params);
    };

    const searchChange = (_values: any) => {
      fetchData();
    };

    const handleCardClick = async (name: string) => {
      try {
        const periodData = timeSelectRef.value.getValues();
        const times = await fetchPeriodTime(
          periodData.executorTimePeriodType,
          periodData.executorTimePeriodUnit,
          periodData.executorTimeInterval,
        );

        emit('cardClick', {
          name,
          params: {
            docStatusList: DOC_STATUS,
            closeStartTime: times.timeStart,
            closeEndTime: times.timeEnd,
          },
          attrs: {
            wrapperClass:
              'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4',
            collapsed: true,
            collapsedRows: 2,
            showCollapseButton: true,
          },
        });
      } catch (error) {
        console.error('获取周期时间失败:', error);
      }
    };

    onMounted(() => {
      WS.on(wsType, fetchData);
      fetchData();
    });

    return {
      inBoundDocNum,
      actualItemNum,
      quantitySum,
      searchChange,
      timeSelectRef,
      quantityLoading,
      actualLoading,
      cancelDocLoading,
      handleCardClick,
    };
  },
});
</script>
<template>
  <div
    class="hover:bg-primary-100 bg-primary-50 cursor-pointer rounded-lg px-3 py-2 text-black"
  >
    <div class="grid grid-cols-2 gap-4">
      <div class="pb-2 font-bold">已取消出库</div>
      <TimeSelect ref="timeSelectRef" @time-change="searchChange" />
    </div>
    <div class="mt-1 flex items-center justify-around text-sm">
      <div
        class="text-center"
        @click="handleCardClick('cancel-outbound-documents')"
      >
        <div class="flex h-7 items-end justify-center">
          <span class="text-xl font-bold">
            <CountTo
              :start-val="0"
              :end-val="inBoundDocNum"
              :duration="1500"
              separator=""
            />
          </span>
          <span class="mb-1 ml-1 text-xs">张</span>
        </div>
        <p>单据</p>
      </div>
      <div
        class="text-center"
        @click="handleCardClick('cancel-outbound-details')"
      >
        <div class="flex h-7 items-end justify-center">
          <span class="text-xl font-bold">
            <CountTo
              :start-val="0"
              :end-val="actualItemNum"
              :duration="1500"
              separator=""
            />
          </span>
          <span class="mb-1 ml-1 text-xs">项</span>
        </div>
        <p>物料项</p>
      </div>
      <div
        class="text-center"
        @click="handleCardClick('cancel-outbound-details')"
      >
        <div class="flex h-7 items-end justify-center">
          <span class="text-xl font-bold">
            <CountTo
              :start-val="0"
              :end-val="quantitySum"
              :duration="1500"
              separator=""
            />
          </span>
        </div>
        <p>物料数量</p>
      </div>
    </div>
  </div>
</template>
