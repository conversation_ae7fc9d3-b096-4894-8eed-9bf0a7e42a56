<script setup lang="ts">
import type { OutboundPendingApi } from '#/api/warehouse-management/index';

import { computed, onMounted, ref } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import {
  getOutboundDocDetail,
  submitInOutCancelDoc,
} from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';
import RemarkOptionSelect from '#/components/remark-option-select/index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  inOutBoundDocId: {
    type: String,
    default: '',
  },
  inOutBoundDocNumber: {
    type: String,
    default: '',
  },
  origDocTypeCode: {
    type: String,
    default: '',
  },
});

const emits = defineEmits([
  'closeBoundLoading',
  'closeBoundSuccess',
  'handleCancel',
]);

const outboundData = ref<OutboundPendingApi.OutboundDocDetail>();

const materialUserDeptName = computed(() => {
  return outboundData.value?.materialUserDeptName || '-';
});

/** 附件*/
const uploadSerialNumberRef = ref();

/** 表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' }, labelWidth: 100 },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

const filesSubmitSuccess = (field: string, res: any) => {
  formApi.setFieldValue(field, res.serialNumber);
};

/** 获取数据 */
const getOutboundDocDetailHandle = async () => {
  try {
    const outboundRes = await getOutboundDocDetail({
      outBoundDocId: props.inOutBoundDocId,
      outBoundDocNumber: props.inOutBoundDocNumber,
      isQueryItem: true,
    });
    outboundData.value = outboundRes;

    const formatRes = {
      ...outboundRes,
      remarkOptionList:
        outboundRes?.remarkOptionList?.map((item: any) => item.optionId) || [],
    };
    return formatRes;
  } catch {
    ElMessage.error('获取出库单据失败');
    return {};
  }
};

onMounted(async () => {
  emits('closeBoundLoading', true);

  try {
    const res = await getOutboundDocDetailHandle();
    formApi.setValues(res);
  } catch {
    ElMessage.error('获取出库单据失败');
  } finally {
    emits('closeBoundLoading', false);
  }
});

// 获取表单数据
const getFormData = async () => {
  const isCompleted_SerialNumber = await uploadSerialNumberRef.value.isComplete;
  if (!isCompleted_SerialNumber) {
    ElMessage.error('等待附件上传完成');
    return false;
  }
  const formValues = await formApi.getValues();
  return formValues;
};

const handleSubmit = async () => {
  emits('closeBoundLoading', true);
  try {
    const formValues = await getFormData();
    const { remark, serialNumber, remarkOptionList } = formValues as any;
    const params = {
      inOutBoundDocId: props.inOutBoundDocId,
      remark,
      serialNumber,
      remarkOptionList,
    };
    await submitInOutCancelDoc(params);
    ElMessage.success('提交成功');
    emits('closeBoundLoading', false);
    emits('closeBoundSuccess');
  } catch {
    ElMessage.error('提交失败');
  } finally {
    emits('closeBoundLoading', false);
  }
};

const handleCancel = () => {
  emits('handleCancel');
};

const handleRemarkOptionListChange = (_: any, isDescRequired: boolean) => {
  formApi.updateSchema([
    {
      rules: isDescRequired ? 'required' : '',
      fieldName: 'remark',
    },
  ]);
};

defineExpose({
  formApi,
  getFormData,
  handleSubmit,
});
</script>

<template>
  <div class="relative h-full p-5 pt-0">
    <div>
      <FormCard :is-footer="false">
        <template #title>
          <span>取消单信息</span>
        </template>
        <template #default>
          <Form>
            <template #materialUserName="{ modelValue }">
              <div>
                {{ modelValue }}
                <span v-if="materialUserDeptName">
                  ({{ materialUserDeptName }})
                </span>
              </div>
            </template>

            <template #remarkOptionList="{ modelValue }">
              <RemarkOptionSelect
                :model-value="modelValue"
                :doc-code="origDocTypeCode"
                doc-field-code="remark"
                @update:model-value="
                  (optionIds) => {
                    formApi.setFieldValue('remarkOptionList', optionIds);
                  }
                "
                @change="handleRemarkOptionListChange"
              />
            </template>

            <template #serialNumber="row">
              <div class="w-full">
                <UploadFiles
                  ref="uploadSerialNumberRef"
                  mode="editMode"
                  :show-operat-button="false"
                  :show-table="uploadSerialNumberRef?.fileList?.length > 0"
                  :show-thumbnail="false"
                  :auto-upload="true"
                  :serial-number="row.value"
                  @files-submit-success="
                    filesSubmitSuccess('serialNumber', $event)
                  "
                >
                  <template #trigger>
                    <div class="text-center">
                      <i class="icon-[bx--folder] mx-auto h-12 w-12"></i>
                      <p class="mt-2">点击或将文件拖拽到这里上传</p>
                      <p class="text-xs text-gray-500">
                        支持格式：.rar .zip .doc .docx .pdf .jpg...
                      </p>
                    </div>
                  </template>
                </UploadFiles>
              </div>
            </template>
          </Form>
        </template>
      </FormCard>
    </div>

    <div
      class="fixed bottom-0 right-0 z-10 h-[50px] w-full rounded-b-lg border-t bg-white p-2 text-right"
    >
      <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
      <ElButton type="primary" @click="handleSubmit"> 提交 </ElButton>
    </div>
  </div>
</template>
