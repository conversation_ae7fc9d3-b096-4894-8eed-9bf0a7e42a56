<script setup lang="ts">
import type { UploadRequestOptions } from 'element-plus';

import type { ParseMaterialListParams } from '#/api/warehouse-management';

import { ref } from 'vue';

import { downloadFileFromResponse } from '@vben/utils';

import {
  ElAlert,
  ElButton,
  ElMessage,
  ElMessageBox,
  ElRadio,
  ElRadioGroup,
  ElScrollbar,
  ElTooltip,
  ElUpload,
} from 'element-plus';

import {
  analyzeMaterialTemplate,
  parseMaterialList,
} from '#/api/warehouse-management';

const props = defineProps({
  /** 获取现有物料列表方法,需要物料id materialId,申请数量applyQuantity*/
  getExistMaterialList: {
    type: Function,
    required: true,
  },
});
const emits = defineEmits(['returnResult']);
/** 下载加载 */
const downloadLoading = ref(false);
/** 上传加载 */
const uploadLoading = ref(false);
/** 解析结果 */
const parseResult = ref<ParseMaterialListParams>();
/** 是否有异常 */
const hasError = ref(false);
/** 控制解析是否显示 */
const isParseResultShow = ref(false);
/** 是否导入成功 */
const isImportSuccess = ref(false);
/** 当前导入的文件名 */
const currentFileName = ref('');
/** 导入的数据条数 */
const importDataCount = ref(0);
/** 现有物料列表*/
const existMaterialList = ref<any[]>([]);
/** 与当前列表重复的物料编号 */
const repeatWithExistList = ref<string[]>([]);
/** 处理重复策略 true为跳过 false为覆盖 */
const handleRepeatWithExist = ref(true);
/** 导入异常说明内容 */
const importExceptionContent = ref<
  {
    materialList: string[];
    showBtn?: boolean;
    title: string;
  }[]
>([]);
/** 控制每个异常内容的展开/收起状态 */
const expandedStates = ref<Record<number, boolean>>({});
/** 切换展开/收起状态 */
const toggleExpanded = (index: number) => {
  expandedStates.value[index] = !expandedStates.value[index];
};
/** 下载模板 */
const getExportMaterialTemplate = async () => {
  try {
    downloadLoading.value = true;
    const response = await analyzeMaterialTemplate();
    downloadFileFromResponse(response);
  } catch {
    ElMessage.error('文件下载失败');
  } finally {
    downloadLoading.value = false;
  }
};
/** 数据导入*/
const importMaterials = async (options: UploadRequestOptions) => {
  importExceptionContent.value = [];
  expandedStates.value = {}; // 重置展开状态
  const { file } = options;
  currentFileName.value = file.name;
  isImportSuccess.value = false;
  try {
    uploadLoading.value = true;
    isParseResultShow.value = false;
    const data = { file };
    // 获取现有物料列表
    existMaterialList.value = await props.getExistMaterialList();
    // 解析物料清单excel获取物料信息
    parseResult.value = await parseMaterialList(data);
    // 添加处理结果的内容
    // 不存在的物料编号
    if (parseResult.value?.nonentityCodes.length > 0) {
      importExceptionContent.value.push({
        materialList: parseResult.value?.nonentityCodes,
        title: `有${parseResult.value.nonentityCodes.length}条不存在的物料编号（导入将跳过不存在的物料）`,
      });
    }
    if (parseResult.value?.repeatCodes.length > 0) {
      importExceptionContent.value.push({
        materialList: parseResult.value?.repeatCodes,
        title: `导入文件中有${parseResult.value.repeatCodes.length}条重复的物料编号（将使用第1条数据导入）`,
      });
    }
    // 检查与现有列表的重复 保存重复的物料编号
    repeatWithExistList.value = parseResult.value?.materialList
      .filter((item) =>
        existMaterialList.value.some(
          (existItem) => existItem.materialId === item.materialId,
        ),
      )
      .map((matchedItem) => matchedItem.materialCode);
    // 与现有列表重复的物料编号
    if (repeatWithExistList.value.length > 0) {
      importExceptionContent.value.push({
        materialList: repeatWithExistList.value,
        title: `导入文件与当前列表存在${repeatWithExistList.value.length}个重复的物料编号`,
        showBtn: true,
      });
    }
    // 非法格式
    if (parseResult.value?.unusualCodes.length > 0) {
      importExceptionContent.value.push({
        materialList: parseResult.value?.unusualCodes,
        title: `导入文件中有${parseResult.value.unusualCodes.length}个数量属于非法格式`,
      });
    }
    // 判断是否有异常
    hasError.value = importExceptionContent.value.length > 0;
    // 检查现有列表的长度 过滤没有materialCode的物料
    existMaterialList.value = existMaterialList.value.filter(
      (item) => item.materialCode,
    );
    // 如果没有异常 同时 现有列表没有数据 直接导入
    if (!hasError.value && existMaterialList.value.length === 0) {
      await dataImport(true, false);
    }
    isParseResultShow.value = true;
    ElMessage.success('数据解析成功');
  } catch (error) {
    console.error('数据解析失败:', error);
    isParseResultShow.value = false;
    ElMessage.error('数据解析失败');
  } finally {
    uploadLoading.value = false;
  }
};

/**
 * 合并并去重 以新列表为主
 * @param addList 要添加的列表
 * @param originalList 保留的列表
 * @param key 唯一标识的键名
 * @returns 合并后的列表
 */
const mergeLists = (
  addList: any[],
  originalList: any[],
  key = 'materialId',
) => {
  const map = new Map();
  addList.forEach((item) => map.set(item[key], item));
  originalList.forEach((item) => map.set(item[key], item));
  return [...map.values()];
};

/**
 * 根据处理重复策略处理数据
 * @param materialList 要处理的物料列表
 * @returns 处理后的物料列表
 */
const handleRepeatWithExistChange = async (
  materialList: any[],
  strategy: boolean = handleRepeatWithExist.value,
) => {
  // 跳过或覆盖
  return strategy
    ? mergeLists(materialList, existMaterialList.value) // 跳过
    : mergeLists(existMaterialList.value, materialList); // 覆盖
};

/**
 * 数据导入
 * @param isClearData 是否清空已有数据
 * @param viewTitle 是否显示提示框
 * 执行完成后会通知父组件,并传递要显示的数据
 */
const dataImport = async (
  isClearData: boolean = false,
  viewTitle: boolean = true,
) => {
  if (viewTitle) {
    await ElMessageBox.confirm(
      isClearData ? '确定清空已有列表数据导入吗？' : '确定追加导入吗？',
      '提示',
      {
        type: 'warning',
      },
    );
  }
  // 如果是清空然后导入
  if (isClearData) {
    existMaterialList.value = [];
  }
  // 将现有物料列表和解析结果进行合并
  const mergeData = await handleRepeatWithExistChange(
    parseResult.value!.materialList,
  );
  // 记录导入的数据条数
  importDataCount.value = mergeData.length;
  // 如果是追加导入并且策略是跳过，则需要减去重复的条数
  if (!isClearData && handleRepeatWithExist.value) {
    importDataCount.value = mergeData.length - existMaterialList.value.length;
  }
  // 转换数据格式
  const result = mergeData.map((item) => ({
    ...item,
    applyQuantity: item.applyQuantity || item.materialQuantity,
  }));
  isImportSuccess.value = true;
  // 设置一个定时器，执行一次
  setTimeout(() => {
    emits('returnResult', result);
  }, 100);
};
/** 取消导入 */
const cancelImport = async () => {
  await ElMessageBox.confirm('确定取消导入吗？', '提示', {
    type: 'warning',
  });
  isParseResultShow.value = false;
};
defineExpose({
  isParseResultShow,
});
</script>

<template>
  <div class="flex justify-end">
    <!-- 提示区域 -->
    <div v-if="isParseResultShow" class="flex flex-grow">
      <!-- 导入结果 -->
      <ElAlert
        v-if="!isImportSuccess"
        :type="hasError ? 'warning' : 'success'"
        show-icon
        :closable="true"
      >
        <div class="mb-2 text-stone-500">
          成功解析《{{ currentFileName }}》{{
            parseResult?.materialList.length
          }}条数据
          <span v-if="hasError">,请确认以下问题：</span>
        </div>
        <div v-if="hasError">
          <div
            v-for="(item, index) in importExceptionContent"
            :key="item.title"
            class="mb-2"
          >
            <!-- 问题标题 -->
            <div class="text-primary-500 mb-2">
              <span class="mr-2">{{ index + 1 }}.{{ item.title }}</span>
              <ElRadioGroup
                v-if="item?.showBtn"
                v-model="handleRepeatWithExist"
              >
                <ElRadio :value="true" size="small">跳过重复数据</ElRadio>
                <ElRadio :value="false" size="small">覆盖重复数据</ElRadio>
              </ElRadioGroup>
            </div>
            <!-- 问题内容 -->
            <div
              class="cursor-pointer text-stone-500"
              @click="toggleExpanded(index)"
            >
              <ElScrollbar max-height="200px">
                <div
                  class="transition-all duration-200"
                  :class="[
                    expandedStates[index] ? '' : 'line-clamp-2 text-ellipsis',
                  ]"
                >
                  <span
                    v-for="(material, materialIndex) in item.materialList"
                    :key="material"
                  >
                    {{ material }}
                    {{
                      materialIndex !== item.materialList.length - 1 ? '、' : ''
                    }}
                  </span>
                </div>
              </ElScrollbar>
            </div>
          </div>
        </div>
        <div class="flex items-center">
          <div class="text-stone-900">请选择操作：</div>
          <ElButton link type="primary" @click="dataImport(true)">
            {{
              existMaterialList.length === 0
                ? '确认导入'
                : '清空已有列表数据导入'
            }}
          </ElButton>
          <ElButton
            v-if="existMaterialList.length > 0"
            link
            type="primary"
            @click="dataImport(false)"
          >
            追加导入
          </ElButton>
          <ElButton link type="primary" @click="cancelImport">
            取消导入
          </ElButton>
        </div>
      </ElAlert>
      <!-- 成功导入 -->
      <ElAlert v-if="isImportSuccess" type="success" show-icon :closable="true">
        <template #default>
          <div class="text-stone-500">
            成功解析《{{ currentFileName }}》并导入{{ importDataCount }}条数据
          </div>
        </template>
      </ElAlert>
    </div>
    <!-- 上传下载按钮 -->
    <div class="ml-2 flex flex-wrap content-start gap-1">
      <ElTooltip effect="light" content="导入数据" placement="top-start">
        <ElUpload
          :show-file-list="false"
          :http-request="importMaterials"
          accept=".xlsx"
        >
          <ElButton :loading="uploadLoading" circle class="mr-2">
            <template #icon><IconFont name="shangchuan" /></template>
          </ElButton>
        </ElUpload>
      </ElTooltip>
      <ElTooltip effect="light" content="下载导入模板" placement="top-start">
        <ElButton
          circle
          @click="getExportMaterialTemplate()"
          class="mr-2"
          :loading="downloadLoading"
        >
          <template #icon><IconFont name="xiazaimoban" /></template>
        </ElButton>
      </ElTooltip>
    </div>
  </div>
</template>
