<script lang="ts" setup>
import type { InBoundDocApi } from '#/api/warehouse-management/index';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage } from 'element-plus';

import { execInbound, reconfirm } from '#/api/warehouse-management/index';

import InboundInfo from './inbound-info/index.vue';
import MaterialsInfo from './materials-info/index.vue';

defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
});

const emits = defineEmits([
  'inboundSuccess',
  'loadDataSuccess',
  'loadDataError',
  'inboundLoading',
  'handleCancel',
]);

/** 入库信息 */
const inboundInfoRef = ref<InstanceType<typeof InboundInfo>>();
const materialsInfoRef = ref<InstanceType<typeof MaterialsInfo>>();
// 二次确认的密码
const confirmPassword = ref('');

/** 二次确认弹窗 */
const [ConfirmModal, confirmModalApi] = useVbenModal({
  confirmText: '确认',
  onBeforeClose: () => {
    confirmPassword.value = '';
    return true;
  },
  destroyOnClose: true,
  onConfirm: () => {
    confirmSubmit();
  },
  centered: true,
  closeOnClickModal: false,
  fullscreenButton: false,
  showCancelButton: true,
});

/** 执行入库 */
const submitAllForm = async () => {
  const formData = await materialsInfoRef.value?.getAllFormData();

  if (!formData) {
    return false;
  }

  try {
    // 正在提交
    emits('inboundLoading', true);
    await execInbound(formData as InBoundDocApi.execInbound);
    ElMessage.success('入库成功');
    emits('inboundLoading', false);
    emits('inboundSuccess');
  } catch (error: any) {
    emits('inboundLoading', false);
    if (error.code === 10_004_032) {
      confirmModalApi.open();
    } else {
      ElMessage.error('执行入库失败');
    }
  }
};

/** 二次确认 */
const confirmSubmit = async () => {
  // 判断密码不能为空，不能是空格
  if (!confirmPassword.value || confirmPassword.value.trim() === '') {
    ElMessage.error('密码不能为空');
    return;
  }

  try {
    confirmModalApi.setState({ loading: true });
    await reconfirm(confirmPassword.value);
    submitAllForm();
    confirmModalApi.close();
  } catch {
    confirmModalApi.setState({ loading: false });
  }
};

const handleCancel = () => {
  emits('handleCancel');
};

/** 暴露方法 */
defineExpose({
  submitAllForm,
});
</script>

<template>
  <div class="relative mb-6 h-full">
    <div class="mb-14">
      <ConfirmModal title="入库前需要二次确认">
        请输入当前账号密码：
        <el-input
          v-model="confirmPassword"
          placeholder="请输入密码"
          class="mt-2 w-full"
          type="password"
          show-password
          @keyup.enter="confirmSubmit"
        />
      </ConfirmModal>
      <InboundInfo
        ref="inboundInfoRef"
        :in-bound-doc-id="inBoundDocId"
        :in-bound-doc-number="inBoundDocNumber"
      />
      <MaterialsInfo
        ref="materialsInfoRef"
        :in-bound-doc-id="inBoundDocId"
        :in-bound-doc-number="inBoundDocNumber"
      />
    </div>

    <div
      class="fixed bottom-0 right-0 z-10 h-[55px] w-full rounded-b-lg border-t bg-white p-4 text-right"
    >
      <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
      <ElButton type="primary" @click="submitAllForm"> 确认入库 </ElButton>
    </div>
  </div>
</template>
